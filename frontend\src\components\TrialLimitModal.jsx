import { useNavigate } from 'react-router-dom'
import { X } from 'lucide-react'

const TrialLimitModal = ({ isOpen, onClose, isLimitReached = false }) => {
  const navigate = useNavigate()

  if (!isOpen) return null

  const handleRegister = () => {
    onClose()
    navigate('/register')
  }

  const handleLogin = () => {
    onClose()
    navigate('/login')
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 animate-fade-in">
      <div className="bg-white rounded-xl max-w-md w-full p-4 sm:p-6 shadow-xl animate-slide-up">
        <div className="flex justify-between items-start mb-3 sm:mb-4">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900">
            {isLimitReached ? 'Free Search Limit Reached' : 'Sign Up to Search'}
          </h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600 p-1">
            <X className="w-4 h-4 sm:w-5 sm:h-5" />
          </button>
        </div>

        <div className="space-y-3 sm:space-y-4">
          <div className="text-gray-600 text-sm sm:text-base">
            <p className="mb-2 sm:mb-3">
              {isLimitReached
                ? "You've used your free search. Create an account to continue searching with more free searches!"
                : "To use CogBridges search, please create an account."
              }
            </p>
          </div>
          
          <div className="bg-primary-50 border border-primary-200 rounded-lg p-3 sm:p-4">
            <div className="flex items-start gap-2 sm:gap-3">
              <span className="text-xl sm:text-2xl">🎁</span>
              <div>
                <h3 className="font-semibold text-primary-900 mb-0.5 sm:mb-1 text-sm sm:text-base">Sign up and get 5 free deep searches!</h3>
                <p className="text-xs sm:text-sm text-primary-700">
                  Register now and receive <strong>100 points</strong> instantly - that's <strong>5 free searches</strong> to explore our powerful Reddit analysis.
                </p>
              </div>
            </div>
          </div>
          
          
          {isLimitReached && (
            <div className="flex gap-2 sm:gap-3 mt-4 sm:mt-6">
              <button
                onClick={handleRegister}
                className="flex-1 px-3 sm:px-4 py-2 sm:py-2.5 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium text-sm sm:text-base"
              >
                Sign up for free
              </button>
              <button
                onClick={handleLogin}
                className="px-3 sm:px-4 py-2 sm:py-2.5 bg-white text-primary-600 border border-primary-300 rounded-lg hover:bg-primary-50 transition-colors text-sm sm:text-base"
              >
                Log in
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default TrialLimitModal