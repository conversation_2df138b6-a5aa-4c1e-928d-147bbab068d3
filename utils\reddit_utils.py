from typing import List
import re


def filter_valid_commenters(commenters: List[str]) -> List[str]:
    """
    Return a filtered list of commenter usernames, removing bots/invalid users.
    """
    filtered: List[str] = []

    bot_patterns = [
        r".*bot$",
        r".*_bot$",
        r"bot_.*",
        r"auto.*",
        r".*moderator.*",
        r".*admin.*",
    ]

    invalid_patterns = [
        r"^\[deleted\]$",
        r"^\[removed\]$",
        r"^deleted$",
        r"^removed$",
    ]

    for commenter in commenters:
        if not commenter or len(commenter.strip()) == 0:
            continue

        commenter_lower = commenter.lower()

        # invalid users
        if any(re.match(pattern, commenter_lower) for pattern in invalid_patterns):
            continue

        # optional bot filter (kept disabled behavior)
        # if any(re.match(pattern, commenter_lower) for pattern in bot_patterns):
        #     continue

        if len(commenter) < 3 or len(commenter) > 20:
            continue

        filtered.append(commenter)

    return filtered


def convert_overview_to_legacy_format(overview_result: dict) -> dict:
    """Convert overview result into legacy per-subreddit posts/comments dict.
    This is a pure function with no side effects.
    """
    if overview_result.get("status") != "success":
        return {}

    legacy_format: dict = {}

    all_subreddits = []
    all_subreddits.extend([
        s.get("subreddit", "N/A")
        for s in overview_result.get("submissions", [])
        if s.get("subreddit") != "N/A"
    ])
    all_subreddits.extend([
        c.get("subreddit", "N/A")
        for c in overview_result.get("comments", [])
        if c.get("subreddit") != "N/A"
    ])
    unique_subreddits = list(set(all_subreddits))

    for submission in overview_result.get("submissions", []):
        subreddit = submission.get("subreddit", "N/A")
        if subreddit not in legacy_format:
            legacy_format[subreddit] = {"posts": [], "comments": []}
        legacy_format[subreddit]["posts"].append({
            "id": f"post_{submission.get('created_utc', 0)}",
            "title": submission.get("title", "N/A"),
            "score": submission.get("score", 0),
            "created_utc": submission.get("created_utc", 0),
        })

    for comment in overview_result.get("comments", []):
        subreddit = comment.get("subreddit", "N/A")
        if subreddit not in legacy_format:
            legacy_format[subreddit] = {"posts": [], "comments": []}
        body = comment.get("body", "N/A")
        if len(body) > 200:
            body = body[:200] + "..."
        legacy_format[subreddit]["comments"].append({
            "id": f"comment_{comment.get('created_utc', 0)}",
            "body": body,
            "score": comment.get("score", 0),
            "created_utc": comment.get("created_utc", 0),
        })

    legacy_format["_metadata"] = {
        "subreddits": unique_subreddits,
        "unique_subreddits_count": len(unique_subreddits),
        "total_posts": len(overview_result.get("submissions", [])),
        "total_comments": len(overview_result.get("comments", [])),
        "username": overview_result.get("username", ""),
        "total_items": overview_result.get("total_items", 0),
        "rate_per_second": overview_result.get("rate_per_second", 0),
    }

    return legacy_format


def prune_posts_to_top_n_comments(posts, top_n: int):
    """
    Based on comment score (or relevance score if available), select top N comments across posts and keep those comments and their parent posts.
    Behavior mirrors api._prune_posts_to_top_n_comments.
    """
    try:
        if not isinstance(posts, list) or top_n is None or top_n <= 0:
            return posts
        flat = []
        for pi, p in enumerate(posts):
            for ci, c in enumerate((p.get("comments") or [])):
                # 优先使用相关性分数，如果没有则使用Reddit score
                sort_score = c.get("_relevance_score", 0) or c.get("score", 0)
                flat.append((p, c, sort_score, pi, ci))  # 添加帖子索引和评论索引
        if not flat:
            return posts
        flat.sort(key=lambda t: t[2], reverse=True)
        out = []
        keys = []
        selected_by_post = {}  # 记录每个帖子选中的评论及其索引
        
        for _, c, _, pi, ci in flat[:top_n]:
            k = c.get("permalink") or c.get("id")
            if k:
                keys.append(k)
                if pi not in selected_by_post:
                    selected_by_post[pi] = []
                selected_by_post[pi].append((ci, c))
                
        keys_set = set(keys)
        for pi, p in enumerate(posts):
            if pi in selected_by_post:
                # 按原始索引排序以保持评论在帖子中的顺序
                selected_by_post[pi].sort(key=lambda x: x[0])
                kept = [c for _, c in selected_by_post[pi]]
                out.append({
                    "success": True,
                    "post": p.get("post", p.get("post") if isinstance(p, dict) else {}),
                    "comments": kept,
                    "commenters": list({c.get("author") for c in kept if c.get("author") and c.get("author") != "[deleted]"}),
                })
        return out or posts
    except Exception:
        return posts


def extract_users_and_subreddits(commenters_history: dict) -> list[dict]:
    """Pure function to extract users and their subreddits from commenters_history.
    Returns a list of { username, user_subreddits, user_data }.
    """
    users_data: list[dict] = []
    if not isinstance(commenters_history, dict):
        return users_data
    for username, user_data in commenters_history.items():
        if not isinstance(user_data, dict):
            continue
        user_subreddits = []
        metadata = user_data.get("_metadata") if isinstance(user_data, dict) else None
        if isinstance(metadata, dict):
            user_subreddits = metadata.get("subreddits", []) or []
        if not user_subreddits:
            user_subreddits = [sr for sr in user_data.keys() if sr != "_metadata"]
        if user_subreddits:
            users_data.append({
                "username": username,
                "user_subreddits": user_subreddits,
                "user_data": user_data,
            })
    return users_data


def build_focus_comments_by_user(reddit_posts: list[dict]) -> dict[str, list[dict]]:
    """Pure function to map username -> focus comments list from aggregated posts.
    The shape mirrors CogBridgesService._build_focus_comments_by_user.
    """
    mapping: dict[str, list[dict]] = {}
    try:
        for post_entry in (reddit_posts or []):
            post = post_entry.get("post", {}) if isinstance(post_entry, dict) else {}
            post_title = post.get("title", "")
            post_subreddit = post.get("subreddit", "")
            for comment in post_entry.get("comments", []) or []:
                username = comment.get("author")
                if not username or username == "[deleted]":
                    continue
                key = (
                    comment.get("permalink")
                    or comment.get("id")
                    or f"comment_{hash(comment.get('body', ''))}"
                )
                body = comment.get("body", "")
                item = {
                    "key": key,
                    "excerpt": body[:300],
                    "body": body,
                    "permalink": comment.get("permalink", ""),
                    "subreddit": post_subreddit,
                    "post_title": post_title,
                }
                mapping.setdefault(username, []).append(item)
    except Exception:
        # pure helper: keep silent and return what we got
        pass
    return mapping


def serialize_submission(submission) -> dict:
    try:
        return {
            "score": getattr(submission, 'score', 0),
            "title": getattr(submission, 'title', 'N/A'),
            "selftext": getattr(submission, 'selftext', 'N/A'),
            "subreddit": getattr(submission, 'subreddit_name_prefixed', 'N/A'),
            "created_utc": getattr(submission, 'created_utc', 0),
        }
    except Exception as e:
        return {
            "error": f"Failed to get post information: {str(e)}",
            "score": 0,
            "title": "N/A",
            "selftext": "N/A",
            "subreddit": "N/A",
            "created_utc": 0,
        }


def serialize_comment(comment) -> dict:
    try:
        parent_id = getattr(comment, 'parent_id', 'N/A')
        is_reply_to_submission = parent_id.startswith('t3_') if parent_id != 'N/A' else False
        return {
            "body": getattr(comment, 'body', 'N/A'),
            "score": getattr(comment, 'score', 0),
            "created_utc": getattr(comment, 'created_utc', 0),
            "subreddit": getattr(comment, 'subreddit_name_prefixed', 'N/A'),
            "is_reply_to_submission": is_reply_to_submission,
            "submission_title": getattr(comment, 'link_title', 'N/A'),
        }
    except Exception as e:
        return {
            "error": f"Failed to get comment information: {str(e)}",
            "body": "N/A",
            "score": 0,
            "created_utc": 0,
            "subreddit": "N/A",
            "is_reply_to_submission": False,
            "submission_title": "N/A",
        }