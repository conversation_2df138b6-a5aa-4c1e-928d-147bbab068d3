"""
EmailService - 简单邮件发送服务
支持通过SMTP发送验证码邮件；若未配置SMTP，则在控制台打印验证码，便于本地开发调试。
"""

from __future__ import annotations

import smtplib
from email.mime.text import MIMEText
from email.utils import formataddr
from typing import Optional

from config import config
from utils.logger_utils import get_logger


class EmailService:
    """封装邮件发送逻辑。

    配置项（.env）：
      - EMAIL_SMTP_HOST
      - EMAIL_SMTP_PORT
      - EMAIL_SMTP_USER
      - EMAIL_SMTP_PASSWORD
      - EMAIL_FROM
      - EMAIL_USE_TLS
      - EMAIL_USE_SSL

    若上述未配置，将降级为开发模式：仅日志打印验证码，不真正发信。
    """

    def __init__(self) -> None:
        self.logger = get_logger(__name__)

    @property
    def configured(self) -> bool:
        try:
            return bool(
                config.EMAIL_SMTP_HOST
                and config.EMAIL_SMTP_USER
                and config.EMAIL_SMTP_PASSWORD
            )
        except Exception:
            return False

    def send_email(self, to_email: str, subject: str, body: str) -> bool:
        """发送纯文本邮件。

        在未配置SMTP时，打印到日志并返回True（开发便捷）。
        """
        if not to_email:
            return False

        if not self.configured:
            # 开发降级：仅打印
            self.logger.info(
                f"[DEV] 未配置SMTP，模拟发送邮件到 {to_email}: {subject}\n{body}"
            )
            return True

        try:
            msg = MIMEText(body, _charset="utf-8")
            msg["Subject"] = subject
            msg["From"] = formataddr(("CogBridges", config.EMAIL_FROM))
            msg["To"] = to_email

            if config.EMAIL_USE_SSL:
                with smtplib.SMTP_SSL(config.EMAIL_SMTP_HOST, config.EMAIL_SMTP_PORT) as server:
                    server.login(config.EMAIL_SMTP_USER, config.EMAIL_SMTP_PASSWORD)
                    server.sendmail(config.EMAIL_FROM, [to_email], msg.as_string())
            else:
                with smtplib.SMTP(config.EMAIL_SMTP_HOST, config.EMAIL_SMTP_PORT) as server:
                    server.ehlo()
                    if config.EMAIL_USE_TLS:
                        server.starttls()
                        server.ehlo()
                    server.login(config.EMAIL_SMTP_USER, config.EMAIL_SMTP_PASSWORD)
                    server.sendmail(config.EMAIL_FROM, [to_email], msg.as_string())

            self.logger.info(f"邮件已发送至 {to_email}: {subject}")
            return True
        except Exception as e:
            self.logger.error(f"发送邮件失败: {e}")
            return False

    def send_verification_code(self, to_email: str, code: str) -> bool:
        subject = "CogBridges Email Verification Code"
        body = (
            f"Your verification code is: {code}\n\n"
            f"This code is used to verify your email address to complete sign-up or security actions."
            f"\nIf you did not request this, you can safely ignore this email."
        )
        return self.send_email(to_email, subject, body)


