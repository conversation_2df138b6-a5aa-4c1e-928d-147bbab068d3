const TestPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-800 mb-8 text-center">
          Tailwind CSS Style Test
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Card 1 - Basics */}
          <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Basics</h2>
            <p className="text-gray-600 mb-4">This is a test card to verify basic Tailwind CSS styles.</p>
            <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
              Test Button
            </button>
          </div>

          {/* Card 2 - Color system */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Color system</h2>
            <div className="space-y-2">
              <div className="w-full h-4 bg-red-500 rounded"></div>
              <div className="w-full h-4 bg-green-500 rounded"></div>
              <div className="w-full h-4 bg-blue-500 rounded"></div>
              <div className="w-full h-4 bg-yellow-500 rounded"></div>
              <div className="w-full h-4 bg-purple-500 rounded"></div>
            </div>
          </div>

          {/* Card 3 - Custom theme */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Primary color test</h2>
            <div className="space-y-3">
              <div className="bg-primary-50 text-primary-700 p-3 rounded">Primary 50</div>
              <div className="bg-primary-500 text-white p-3 rounded">Primary 500</div>
              <div className="bg-primary-600 text-white p-3 rounded">Primary 600</div>
            </div>
          </div>

          {/* Card 4 - Animations */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Animations</h2>
            <div className="space-y-3">
              <div className="w-16 h-16 bg-blue-500 rounded-full animate-pulse"></div>
              <div className="w-16 h-16 bg-green-500 rounded-full animate-bounce"></div>
              <div className="w-16 h-16 bg-red-500 rounded-full animate-spin"></div>
            </div>
          </div>

          {/* Card 5 - Custom animations */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Custom animations</h2>
            <div className="space-y-3">
              <div className="w-full h-4 bg-gradient-to-r from-blue-400 to-purple-500 rounded animate-fade-in"></div>
              <div className="w-full h-4 bg-gradient-to-r from-green-400 to-blue-500 rounded animate-slide-up"></div>
              <div className="w-full h-4 bg-gradient-to-r from-pink-400 to-red-500 rounded animate-pulse-slow"></div>
            </div>
          </div>

          {/* Card 6 - Responsive */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Responsive</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              <div className="bg-blue-100 p-2 rounded text-center text-sm">Small</div>
              <div className="bg-green-100 p-2 rounded text-center text-sm hidden sm:block">Medium+</div>
            </div>
          </div>
        </div>

        {/* Fonts */}
        <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">Fonts</h2>
          <div className="space-y-2">
            <p className="font-light text-gray-600">Inter Light - light weight text</p>
            <p className="font-normal text-gray-700">Inter Regular - regular weight text</p>
            <p className="font-medium text-gray-800">Inter Medium - medium weight text</p>
            <p className="font-semibold text-gray-800">Inter Semibold - semibold weight text</p>
            <p className="font-bold text-gray-900">Inter Bold - bold weight text</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TestPage
