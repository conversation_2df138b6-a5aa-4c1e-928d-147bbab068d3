import React, { useState, useRef, useEffect } from 'react'
import { User, ExternalLink, Tag, Globe, Award, ChevronDown } from 'lucide-react'

const CommenterCard = ({ credibilityData, commentAlignment, className = "" }) => {
  if (!credibilityData) return null

  const {
    username,
    profile_url,
    subreddits_active_in = [],
    expertise = {},
    background_similarity = {},
    worldview = {}
  } = credibilityData

  // Collapsed state: expanded by default
  const [isCollapsed, setIsCollapsed] = useState(false)
  const cardRef = useRef(null)
  const bottomToggleRef = useRef(null)

  // When collapsed, scroll the bottom toggle into view
  useEffect(() => {
    if (isCollapsed && bottomToggleRef.current) {
      const rect = bottomToggleRef.current.getBoundingClientRect()
      const y = window.scrollY + rect.top - 8
      window.scrollTo({ top: y, behavior: 'smooth' })
    }
  }, [isCollapsed])

  return (
    <div ref={cardRef} className={`bg-gray-50 border border-gray-200 rounded-lg p-3 sm:p-4 mt-3 sm:mt-4 ${className}`}>
      {/* Alignment justification (if any) */}
      {!isCollapsed && commentAlignment?.justification && (
        <div className="mb-2 sm:mb-3">
          <div className="text-xs text-gray-600 italic">
            {commentAlignment.justification}
          </div>
        </div>
      )}
      {/* Header */}
      <div className="flex items-center mb-3 sm:mb-4">
        <div className="flex items-center space-x-1.5 sm:space-x-2">
          <User className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
          <a
            href={(profile_url && profile_url.trim()) || `https://www.reddit.com/user/${encodeURIComponent(username || '')}`}
            target="_blank"
            rel="noopener noreferrer"
            className="font-medium text-blue-600 hover:text-blue-800 hover:underline text-sm sm:text-base"
            title={`View u/${username} on Reddit`}
          >
            @{username}
          </a>
          {profile_url && (
            <a 
              href={profile_url} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-500 hover:text-blue-700"
            >
              <ExternalLink className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
            </a>
          )}
        </div>
      </div>

      {/* Active subreddits */}
      {!isCollapsed && subreddits_active_in && subreddits_active_in.length > 0 && (
        <div className="mb-3 sm:mb-4">
          <div className="flex items-center space-x-1.5 sm:space-x-2 mb-1.5 sm:mb-2">
            <Tag className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-gray-500" />
            <span className="text-xs sm:text-sm font-medium text-gray-700">Active subreddits</span>
          </div>
          <div className="flex flex-wrap gap-1">
            {subreddits_active_in.slice(0, 5).map((subreddit, index) => (
              <span 
                key={index}
                className="text-xs bg-blue-100 text-blue-700 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded"
              >
                {subreddit}
              </span>
            ))}
            {subreddits_active_in.length > 5 && (
              <span className="text-xs text-gray-500">
                +{subreddits_active_in.length - 5} more
              </span>
            )}
          </div>
        </div>
      )}

      {/* Expertise */}
      {!isCollapsed && expertise.summary && (
        <div className="mb-3 sm:mb-4">
          <div className="flex items-center space-x-1.5 sm:space-x-2 mb-1.5 sm:mb-2">
            <Award className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-green-600" />
            <span className="text-xs sm:text-sm font-medium text-gray-700">Expertise</span>
          </div>
          <p className="text-xs sm:text-sm text-gray-600 mb-1.5 sm:mb-2">{expertise.summary}</p>
          {expertise.evidence_comments && expertise.evidence_comments.length > 0 && (
            <div className="text-xs text-gray-500">
              <span>Evidence: {expertise.evidence_comments.slice(0, 2).join(' • ')}</span>
              {expertise.evidence_comments.length > 2 && 
                <span> • +{expertise.evidence_comments.length - 2} more</span>
              }
            </div>
          )}
        </div>
      )}

      {/* Background similarity */}
      {!isCollapsed && background_similarity.summary && (
        <div className="mb-3 sm:mb-4">
          <div className="flex items-center space-x-1.5 sm:space-x-2 mb-1.5 sm:mb-2">
            <User className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-purple-600" />
            <span className="text-xs sm:text-sm font-medium text-gray-700">Background & Perspective</span>
          </div>
          <p className="text-xs sm:text-sm text-gray-600 mb-1.5 sm:mb-2">{background_similarity.summary}</p>
          {background_similarity.evidence_comments && background_similarity.evidence_comments.length > 0 && (
            <div className="text-xs text-gray-500">
              <span>Evidence: {background_similarity.evidence_comments.slice(0, 2).join(' • ')}</span>
              {background_similarity.evidence_comments.length > 2 && 
                <span> • +{background_similarity.evidence_comments.length - 2} more</span>
              }
            </div>
          )}
        </div>
      )}

      {/* Worldview */}
      {!isCollapsed && worldview.summary && (
        <div className="mb-3 sm:mb-4">
          <div className="flex items-center space-x-1.5 sm:space-x-2 mb-1.5 sm:mb-2">
            <Globe className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-indigo-600" />
            <span className="text-xs sm:text-sm font-medium text-gray-700">Values & Worldview</span>
          </div>
          <p className="text-xs sm:text-sm text-gray-600 mb-1.5 sm:mb-2">{worldview.summary}</p>
          {worldview.evidence_comments && worldview.evidence_comments.length > 0 && (
            <div className="text-xs text-gray-500">
              <span>Evidence: {worldview.evidence_comments.slice(0, 2).join(' • ')}</span>
              {worldview.evidence_comments.length > 2 && 
                <span> • +{worldview.evidence_comments.length - 2} more</span>
              }
            </div>
          )}
        </div>
      )}

      {/* Collapse/Expand button moved to bottom right */}
      <div 
        ref={bottomToggleRef} 
        className="flex justify-end"
      >
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="flex items-center space-x-1 text-xs sm:text-sm text-gray-500 hover:text-gray-700 transition-colors"
        >
          <ChevronDown className={`w-3.5 h-3.5 sm:w-4 sm:h-4 transition-transform ${isCollapsed ? 'rotate-180' : ''}`} />
          <span>{isCollapsed ? 'Show details' : 'Hide details'}</span>
        </button>
      </div>
    </div>
  )
}

export default CommenterCard


