#!/usr/bin/env python3
"""
Test feedback functionality
"""

import pytest
from unittest.mock import patch, MagicMock
from api.blueprints_feedback import bp_feedback


def test_send_feedback_success(test_client):
    """Test successful feedback submission"""
    with patch('api.blueprints_feedback.email_service.send_email') as mock_send:
        mock_send.return_value = True
        
        response = test_client.post('/api/feedback', json={
            'email': '<EMAIL>',
            'content': 'This is test feedback'
        })
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        assert data['message'] == 'Feedback sent successfully'
        
        # Verify email was sent with correct parameters
        mock_send.assert_called_once()
        call_args = mock_send.call_args[1]
        assert call_args['to_email'] == '<EMAIL>'
        assert 'CogBridges <NAME_EMAIL>' in call_args['subject']
        assert 'This is test feedback' in call_args['body']
        assert 'From: <EMAIL>' in call_args['body']


def test_send_feedback_with_auth(test_client):
    """Test feedback submission with authenticated user"""
    with patch('api.blueprints_feedback.email_service.send_email') as mock_send:
        mock_send.return_value = True
        
        with patch('api.blueprints_feedback.verify_token') as mock_verify:
            mock_verify.return_value = {'email': '<EMAIL>'}
            
            response = test_client.post('/api/feedback', 
                json={
                    'email': '<EMAIL>',
                    'content': 'Authenticated feedback'
                },
                headers={'Authorization': 'Bearer test-token'}
            )
            
            assert response.status_code == 200
            data = response.get_json()
            assert data['success'] is True
            
            # Verify authenticated user info is included
            call_args = mock_send.call_args[1]
            assert 'Authenticated User: <EMAIL>' in call_args['body']


def test_send_feedback_missing_data(test_client):
    """Test feedback submission with missing data"""
    # Test with empty JSON object
    response = test_client.post('/api/feedback', json={})
    
    assert response.status_code == 400
    data = response.get_json()
    assert data['success'] is False
    assert 'Email and feedback content are required' in data['message']
    
    # Test with no JSON data
    response = test_client.post('/api/feedback')
    
    assert response.status_code == 400
    data = response.get_json()
    assert data['success'] is False
    assert 'Invalid request data' in data['message']


def test_send_feedback_empty_fields(test_client):
    """Test feedback submission with empty fields"""
    response = test_client.post('/api/feedback', json={
        'email': '  ',
        'content': '  '
    })
    
    assert response.status_code == 400
    data = response.get_json()
    assert data['success'] is False
    assert 'Email and feedback content are required' in data['message']


def test_send_feedback_invalid_email(test_client):
    """Test feedback submission with invalid email"""
    response = test_client.post('/api/feedback', json={
        'email': 'invalid-email',
        'content': 'Test feedback'
    })
    
    assert response.status_code == 400
    data = response.get_json()
    assert data['success'] is False
    assert 'Invalid email address' in data['message']


def test_send_feedback_no_json(test_client):
    """Test feedback submission without JSON data"""
    response = test_client.post('/api/feedback', data='not json')
    
    assert response.status_code == 400
    data = response.get_json()
    assert data['success'] is False
    assert 'Invalid request data' in data['message']


def test_send_feedback_email_failure(test_client):
    """Test feedback submission when email sending fails"""
    with patch('api.blueprints_feedback.email_service.send_email') as mock_send:
        mock_send.return_value = False
        
        response = test_client.post('/api/feedback', json={
            'email': '<EMAIL>',
            'content': 'Test feedback'
        })
        
        assert response.status_code == 500
        data = response.get_json()
        assert data['success'] is False
        assert 'Failed to send feedback' in data['message']


def test_send_feedback_exception(test_client):
    """Test feedback submission when exception occurs"""
    with patch('api.blueprints_feedback.email_service.send_email') as mock_send:
        mock_send.side_effect = Exception('Test error')
        
        response = test_client.post('/api/feedback', json={
            'email': '<EMAIL>',
            'content': 'Test feedback'
        })
        
        assert response.status_code == 500
        data = response.get_json()
        assert data['success'] is False
        assert 'An error occurred while sending feedback' in data['message']


def test_send_feedback_valid_email_formats(test_client):
    """Test various valid email formats"""
    valid_emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    with patch('api.blueprints_feedback.email_service.send_email') as mock_send:
        mock_send.return_value = True
        
        for email in valid_emails:
            response = test_client.post('/api/feedback', json={
                'email': email,
                'content': 'Test feedback'
            })
            
            assert response.status_code == 200, f"Failed for email: {email}"
            data = response.get_json()
            assert data['success'] is True


def test_send_feedback_long_content(test_client):
    """Test feedback with long content"""
    with patch('api.blueprints_feedback.email_service.send_email') as mock_send:
        mock_send.return_value = True
        
        long_content = 'This is a very long feedback. ' * 100
        # Remove trailing space for cleaner comparison
        long_content = long_content.rstrip()
        
        response = test_client.post('/api/feedback', json={
            'email': '<EMAIL>',
            'content': long_content
        })
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        
        # Verify long content is included
        call_args = mock_send.call_args[1]
        email_body = call_args['body']
        assert long_content in email_body
        # Also verify the email structure
        assert 'Feedback Content:' in email_body
        assert 'From: <EMAIL>' in email_body