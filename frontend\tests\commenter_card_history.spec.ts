import { test, expect } from '@playwright/test'

// This test verifies that when opening a history session, the commenter card
// (which depends on llm_analysis.credibility_analysis) is rendered if the
// analysis exists in raw_data even when the DB-provided llm_analysis lacks it.

test('should show commenter credibility card when viewing a history session using raw_data.llm_analysis', async ({ page }) => {
  const sessionId = 'search_hist_card'

  const mockSession = {
    success: true,
    data: {
      id: sessionId,
      query: 'Test query for history',
      // DB shape: has posts and comments
      reddit_posts: [
        {
          id: 'p1',
          title: 'A test post title',
          author: 'post_author',
          subreddit: 'testsub',
          score: 123,
          permalink: '/r/testsub/comments/abc123/a_test_post_title/'
        }
      ],
      reddit_comments: [
        {
          id: 'c1',
          post_id: 'p1',
          body: 'This is a credible comment body',
          author: 'credible_user',
          score: 7,
          permalink: '/r/testsub/comments/abc123/a_test_post_title/c1/'
        }
      ],
      commenters_history: {
        credible_user: { comment_karma: 4321, link_karma: 100 }
      },
      // DB-aggregated llm_analysis may not include credibility_analysis
      llm_analysis: {
        similarity_analysis: {},
        motivation_analysis: {}
      },
      // raw_data contains the full llm_analysis with credibility_analysis
      raw_data: {
        llm_analysis: {
          credibility_analysis: {
            credible_user: {
              username: 'credible_user',
              profile_url: 'https://www.reddit.com/user/credible_user',
              expertise: { summary: 'Expert on testing and QA', evidence_comments: ['evidence 1'] },
              background_similarity: { summary: 'Similar background to the topic', evidence_comments: ['evidence 2'] },
              worldview: { summary: 'Pragmatic', evidence_comments: [] },
              per_comment: {}
            }
          }
        }
      }
    }
  }

  // Mock the session endpoint
  await page.route(`**/api/sessions/${sessionId}`, route => route.fulfill({ json: mockSession }))

  // Navigate to results page and inject session id via history state (like HistoryPage does)
  await page.goto('/#/results')

  await page.evaluate((sid) => {
    window.history.replaceState(
      { sessionId: sid },
      '',
      window.location.href
    )
    window.dispatchEvent(new PopStateEvent('popstate'))
  }, sessionId)

  // Basic page visible
      await expect(page.getByText('Featured Comment')).toBeVisible({ timeout: 5000 })
  await expect(page.getByText('A test post title')).toBeVisible()
  await expect(page.getByText('This is a credible comment body')).toBeVisible()

  // Commenter credibility card should show
  await expect(page.getByText('@credible_user')).toBeVisible()
  await expect(page.getByText('Expertise')).toBeVisible()
  await expect(page.getByText('Expert on testing and QA')).toBeVisible()
})

