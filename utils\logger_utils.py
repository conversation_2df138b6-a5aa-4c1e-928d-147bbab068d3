"""
CogBridges Search - Logging Utilities
Provides unified logging configuration and management
"""

import logging
import sys
from pathlib import Path
from typing import Optional
import inspect
from datetime import datetime
import json
from config import config


class JSONFormatter(logging.Formatter):
    """JSON format log formatter"""
    
    def format(self, record):
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add exception info
        if record.exc_info:
            log_entry["exception"] = self.formatException(record.exc_info)
        
        # Add extra fields
        if hasattr(record, 'extra_data'):
            log_entry["extra"] = record.extra_data
        
        return json.dumps(log_entry, ensure_ascii=False)


def setup_logger(
    name: str,
    level: Optional[str] = None,
    log_file: Optional[str] = None,
    json_format: bool = False
) -> logging.Logger:
    """
    Setup logger
    
    Args:
        name: Logger name
        level: Log level
        log_file: Log file path
        json_format: Whether to use JSON format
    
    Returns:
        Configured logger
    """
    logger = logging.getLogger(name)
    
    # Avoid duplicate configuration
    if logger.handlers:
        return logger
    
    # Set log level
    log_level = getattr(logging, level or config.LOG_LEVEL.upper())
    logger.setLevel(log_level)
    
    # Create formatter
    if json_format:
        formatter = JSONFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    # Console handler (always add for Render logs visibility)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler (only in development environment)
    if (log_file or config.SAVE_DETAILED_LOGS) and not config.IS_PRODUCTION:
        if not log_file:
            # Use default log file
            log_file = config.LOGS_DIR / f"{name}.log"
        else:
            log_file = Path(log_file)
        
        # Ensure log directory exists
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    Get logger
    
    Args:
        name: Logger name
    
    Returns:
        Logger instance
    """
    return setup_logger(name, json_format=config.ENABLE_JSON_LOGS)


class LoggerMixin:
    """Logger mixin class"""
    
    @property
    def logger(self) -> logging.Logger:
        """Get logger for current class"""
        return get_logger(self.__class__.__module__)


def log_function_call(func):
    """
    Function call logging decorator
    
    Args:
        func: Function to be decorated
    
    Returns:
        Decorated function
    """
    logger = get_logger(func.__module__)
    
    def wrapper(*args, **kwargs):
        # Log function call
        logger.debug(f"Calling function {func.__name__}")
        
        try:
            result = func(*args, **kwargs)
            logger.debug(f"Function {func.__name__} executed successfully")
            return result
        except Exception as e:
            logger.error(f"Function {func.__name__} failed: {e}")
            raise
    
    return wrapper


def log_async_function_call(func):
    """
    Async function call logging decorator
    
    Args:
        func: Async function to be decorated
    
    Returns:
        Decorated async function
    """
    logger = get_logger(func.__module__)
    
    async def wrapper(*args, **kwargs):
        # Log function call
        logger.debug(f"Calling async function {func.__name__}")
        
        try:
            result = await func(*args, **kwargs)
            logger.debug(f"Async function {func.__name__} executed successfully")
            return result
        except Exception as e:
            logger.error(f"Async function {func.__name__} failed: {e}")
            raise
    
    return wrapper


def log_api_call(service_name: str, endpoint: str, method: str = "GET"):
    """
    API call logging decorator
    
    Args:
        service_name: Service name
        endpoint: API endpoint
        method: HTTP method
    
    Returns:
        Decorator function
    """
    def decorator(func):
        # Support both sync and async functions to avoid misuse of sync wrapper on async functions
        if inspect.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                logger = get_logger(func.__module__)
                logger.info(f"Starting {service_name} API call: {method} {endpoint}")
                
                start_time = datetime.now()
                try:
                    result = await func(*args, **kwargs)
                    duration = (datetime.now() - start_time).total_seconds()
                    logger.info(f"{service_name} API call successful, duration: {duration:.2f}s")
                    return result
                except Exception as e:
                    duration = (datetime.now() - start_time).total_seconds()
                    logger.error(f"{service_name} API call failed, duration: {duration:.2f}s, error: {e}")
                    raise
            
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                logger = get_logger(func.__module__)
                # Log API call start
                logger.info(f"Starting {service_name} API call: {method} {endpoint}")
                
                start_time = datetime.now()
                try:
                    result = func(*args, **kwargs)
                    # Log API call success
                    duration = (datetime.now() - start_time).total_seconds()
                    logger.info(f"{service_name} API call successful, duration: {duration:.2f}s")
                    return result
                except Exception as e:
                    # Log API call failure
                    duration = (datetime.now() - start_time).total_seconds()
                    logger.error(f"{service_name} API call failed, duration: {duration:.2f}s, error: {e}")
                    raise
            
            return sync_wrapper
    
    return decorator


def save_io_snapshot(
    service: str, 
    action: str, 
    inputs: dict = None, 
    outputs: dict = None
):
    """Save input/output snapshot for a single call as JSON file for troubleshooting.
    
    在生产环境下，此功能被禁用以节省存储空间。
    
    Args:
        service: Service name (e.g., 'grok', 'reddit')
        action: Action name (e.g., 'search', 'get_comments')
        inputs: Input data (dict)
        outputs: Output data (dict)
    """
    # 生产环境禁用I/O快照功能
    if not config.ENABLE_IO_SNAPSHOTS:
        return None
        
    try:
        from uuid import uuid4
        ts = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        safe_service = str(service or "service").replace("/", "_")
        safe_action = str(action or "action").replace("/", "_")
        suffix = uuid4().hex[:8]
        io_dir = config.LOGS_DIR / "io"
        io_dir.mkdir(parents=True, exist_ok=True)
        filepath = io_dir / f"{safe_service}_{safe_action}_{ts}_{suffix}.json"

        record = {
            "timestamp": datetime.now().isoformat(),
            "service": safe_service,
            "action": safe_action,
            "inputs": inputs or {},
            "outputs": outputs or {},
        }

        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(record, f, ensure_ascii=False, indent=2)

        return str(filepath)
    except Exception:
        # Cannot let log persistence failure affect main flow
        return None


def persist_io_record(
    service: str,
    action: str,
    request_payload: dict = None,
    response_payload: dict = None,
    error: str = None,
    extra: dict = None,
    filename_hint: str = None
):
    """Persist input/output record for a service call.
    
    Save location: config.LOGS_DIR / "io" / {service}_{action}_{timestamp}_{hint}.json
    
    Args:
        service: Service name (e.g., 'grok', 'reddit', 'replicate')
        action: Action name (e.g., 'search', 'get_comments', 'generate_text')
        request_payload: Request data (dict)
        response_payload: Response data (dict)
        error: Error message if failed
        extra: Additional metadata
        filename_hint: Optional filename hint
    """
    if not config.ENABLE_IO_SNAPSHOTS:
        return None
        
    try:
        from uuid import uuid4
        ts = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        safe_service = str(service or "service").replace("/", "_")
        safe_action = str(action or "action").replace("/", "_")
        suffix = filename_hint or uuid4().hex[:8]
        io_dir = config.LOGS_DIR / "io"
        io_dir.mkdir(parents=True, exist_ok=True)
        filepath = io_dir / f"{safe_service}_{safe_action}_{ts}_{suffix}.json"

        record = {
            "timestamp": datetime.now().isoformat(),
            "service": safe_service,
            "action": safe_action,
            "request": request_payload or {},
            "response": response_payload or {},
            "error": error,
            "extra": extra or {},
        }

        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(record, f, ensure_ascii=False, indent=2)

        return str(filepath)
    except Exception:
        # Cannot let log persistence failure affect main flow
        return None


def create_operation_logger(operation_name: str) -> logging.Logger:
    """
    Create operation-specific logger
    
    Args:
        operation_name: Operation name
        
    Returns:
        Operation-specific logger
    """
    logger_name = f"operation.{operation_name}"
    
    # 在生产环境下，不创建专用日志文件，只使用控制台输出
    if config.IS_PRODUCTION:
        return setup_logger(logger_name, json_format=config.ENABLE_JSON_LOGS)
    else:
        # 本地开发环境创建专用日志文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = config.LOGS_DIR / f"{operation_name}_{timestamp}.log"
        return setup_logger(logger_name, log_file=str(log_file), json_format=True)


def log_search_operation(query: str, results_count: int, duration: float):
    """
    Log search operation
    
    Args:
        query: Search query
        results_count: Number of results
        duration: Execution time
    """
    logger = create_operation_logger("search")
    
    log_data = {
        "operation": "search",
        "query": query,
        "results_count": results_count,
        "duration": duration,
        "timestamp": datetime.now().isoformat()
    }
    
    # Use extra parameter to pass structured data
    logger.info("Search operation completed", extra={"extra_data": log_data})


def log_reddit_operation(operation_type: str, data: dict):
    """
    Log Reddit operation
    
    Args:
        operation_type: Operation type (get_post, get_comments, get_user_history)
        data: Operation data
    """
    logger = create_operation_logger("reddit")
    
    log_data = {
        "operation": operation_type,
        "data": data,
        "timestamp": datetime.now().isoformat()
    }
    
    logger.info(f"Reddit operation: {operation_type}", extra={"extra_data": log_data})


def log_reddit_rate_limit(
    endpoint: str,
    rate_limit_remaining: int = None,
    rate_limit_used: int = None,
    rate_limit_reset: int = None,
    response_headers: dict = None
):
    """
    Log Reddit API rate limit information from X-Ratelimit headers
    
    Args:
        endpoint: API endpoint that was called
        rate_limit_remaining: Value from X-Ratelimit-Remaining header
        rate_limit_used: Value from X-Ratelimit-Used header  
        rate_limit_reset: Value from X-Ratelimit-Reset header (seconds until reset)
        response_headers: Full response headers dict (optional)
    """
    logger = get_logger("reddit.ratelimit")
    
    log_data = {
        "endpoint": endpoint,
        "timestamp": datetime.now().isoformat()
    }
    
    # Add rate limit values if provided
    if rate_limit_remaining is not None:
        log_data["remaining"] = rate_limit_remaining
    if rate_limit_used is not None:
        log_data["used"] = rate_limit_used
    if rate_limit_reset is not None:
        log_data["reset_in_seconds"] = rate_limit_reset
        # Calculate actual reset time from seconds until reset
        reset_timestamp = datetime.now().timestamp() + rate_limit_reset
        reset_time = datetime.fromtimestamp(reset_timestamp)
        log_data["reset_time"] = reset_time.isoformat()
        log_data["reset_timestamp"] = reset_timestamp
    
    # Extract rate limit headers from response headers if provided
    if response_headers:
        for header, value in response_headers.items():
            if header.lower().startswith('x-ratelimit-'):
                log_data[f"header_{header.lower()}"] = value
    
    # Determine log level based on remaining quota
    if rate_limit_remaining is not None:
        if rate_limit_remaining == 0:
            logger.error(f"Reddit API rate limit exhausted for {endpoint}", extra={"extra_data": log_data})
        elif rate_limit_remaining < 10:
            logger.warning(f"Reddit API rate limit low for {endpoint}: {rate_limit_remaining} remaining", extra={"extra_data": log_data})
        else:
            logger.info(f"Reddit API rate limit for {endpoint}: {rate_limit_remaining} remaining", extra={"extra_data": log_data})
    else:
        logger.info(f"Reddit API call to {endpoint}", extra={"extra_data": log_data})
    
    # Persist detailed rate limit information for analysis
    if rate_limit_remaining is not None or rate_limit_used is not None or rate_limit_reset is not None:
        persist_io_record(
            service="reddit",
            action="ratelimit",
            request_payload={"endpoint": endpoint},
            response_payload=log_data,
            filename_hint=f"ratelimit_{rate_limit_remaining or 'unknown'}"
        )


# Removed direct execution entry to avoid script behavior pollution in production and test environments
