// Lightweight localStorage-based persistence for active search session
// Ensures recovery after tab switch, reload, crash, or app reopen

const STORAGE_KEY = 'active_search'

function safeParse(json) {
  try {
    return JSON.parse(json)
  } catch (_) {
    return null
  }
}

export function getActiveSearch() {
  try {
    const raw = window.localStorage.getItem(STORAGE_KEY)
    if (!raw) return null
    return safeParse(raw)
  } catch (_) {
    return null
  }
}

export function setActiveSearch(payload) {
  try {
    const data = {
      // expected fields: query, clientSessionId, sessionId?, status, progress?, startedAt?, finishedAt?
      ...payload,
      // normalize status values
      status: payload?.status || 'searching'
    }
    window.localStorage.setItem(STORAGE_KEY, JSON.stringify(data))
  } catch (_) {}
}

export function updateActiveSearch(patch) {
  try {
    const cur = getActiveSearch() || {}
    const next = { ...cur, ...patch }
    window.localStorage.setItem(STORAGE_KEY, JSON.stringify(next))
  } catch (_) {}
}

export function clearActiveSearch() {
  try {
    window.localStorage.removeItem(STORAGE_KEY)
  } catch (_) {}
}

export function isSearchInProgress(status) {
  const s = (status || '').toLowerCase()
  return s === 'searching' || s === 'running' || s === 'pending' || s === 'starting'
}

