"""
匿名用户限制功能的单元测试
测试匿名用户搜索次数限制、Cookie管理、IP限制等核心功能
"""

import pytest
import tempfile
import shutil
import os
from pathlib import Path
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from services.anonymous_limit_service import AnonymousLimitService


class TestAnonymousLimitService:
    """匿名用户限制服务测试类"""
    
    @pytest.fixture
    def service(self, db_session):
        """创建匿名限制服务实例（数据库版）"""
        return AnonymousLimitService()
    
    def test_check_anonymous_limit_first_time(self, service):
        """测试第一次搜索时的限制检查"""
        # 直接模拟服务内部方法，避免请求上下文问题
        with patch.object(service, '_get_client_ip', return_value='*************'), \
             patch.object(service, '_get_anonymous_cookie_id', return_value=None):
            
            # 第一次搜索应该允许
            can_search, cookie_id, limit_info = service.check_anonymous_limit()
            
            assert can_search is True
            assert cookie_id is not None
            assert 'anon_' in cookie_id
            assert limit_info['ip_count'] == 0
            assert limit_info['cookie_count'] == 0
            assert limit_info['limit'] == 2
    
    def test_check_anonymous_limit_second_time_same_ip(self, service):
        """测试同一IP第二次搜索时的限制检查"""
        with patch.object(service, '_get_client_ip', return_value='*************'), \
             patch.object(service, '_get_anonymous_cookie_id', return_value=None):
            
            # 第一次搜索
            can_search1, cookie_id1, _ = service.check_anonymous_limit()
            assert can_search1 is True
            
            # 记录第一次搜索
            service.record_anonymous_search(cookie_id1)
            
            # 第二次搜索应该允许
            can_search2, cookie_id2, limit_info = service.check_anonymous_limit()
            assert can_search2 is True
            assert limit_info['ip_count'] == 1
            assert limit_info['limit'] == 2
    
    def test_check_anonymous_limit_third_time_same_ip(self, service):
        """测试同一IP第三次搜索时的限制检查"""
        with patch.object(service, '_get_client_ip', return_value='*************'), \
             patch.object(service, '_get_anonymous_cookie_id', return_value=None):
            
            # 第一次搜索
            can_search1, cookie_id1, _ = service.check_anonymous_limit()
            assert can_search1 is True
            service.record_anonymous_search(cookie_id1)
            
            # 第二次搜索
            can_search2, cookie_id2, _ = service.check_anonymous_limit()
            assert can_search2 is True
            service.record_anonymous_search(cookie_id2)
            
            # 第三次搜索应该被拒绝
            can_search3, cookie_id3, limit_info = service.check_anonymous_limit()
            assert can_search3 is False
            assert limit_info['ip_count'] == 2
            assert limit_info['limit'] == 2
    
    def test_check_anonymous_limit_different_ip(self, service):
        """测试不同IP的搜索限制"""
        # IP1 第一次搜索
        with patch.object(service, '_get_client_ip', return_value='*************'), \
             patch.object(service, '_get_anonymous_cookie_id', return_value=None):
            
            can_search1, cookie_id1, _ = service.check_anonymous_limit()
            assert can_search1 is True
            service.record_anonymous_search(cookie_id1)
        
        # IP2 第一次搜索应该允许
        with patch.object(service, '_get_client_ip', return_value='*************'), \
             patch.object(service, '_get_anonymous_cookie_id', return_value=None):
            
            can_search2, cookie_id2, limit_info = service.check_anonymous_limit()
            assert can_search2 is True
            assert limit_info['ip_count'] == 0  # 新IP，计数为0
    
    def test_cookie_based_limit(self, service):
        """测试基于Cookie的搜索限制"""
        # 第一次搜索，无Cookie
        with patch.object(service, '_get_client_ip', return_value='*************'), \
             patch.object(service, '_get_anonymous_cookie_id', return_value=None):
            
            can_search1, cookie_id1, _ = service.check_anonymous_limit()
            assert can_search1 is True
            service.record_anonymous_search(cookie_id1)
        
        # 第二次搜索，使用相同的Cookie
        with patch.object(service, '_get_client_ip', return_value='*************'), \
             patch.object(service, '_get_anonymous_cookie_id', return_value=cookie_id1):
            
            can_search2, cookie_id2, limit_info = service.check_anonymous_limit()
            assert can_search2 is True
            assert limit_info['cookie_count'] == 1
            # 记录第二次搜索
            service.record_anonymous_search(cookie_id1)
        
        # 第三次搜索，使用相同的Cookie，应该被拒绝
        with patch.object(service, '_get_client_ip', return_value='*************'), \
             patch.object(service, '_get_anonymous_cookie_id', return_value=cookie_id1):
            
            can_search3, cookie_id3, limit_info = service.check_anonymous_limit()
            assert can_search3 is False
            assert limit_info['cookie_count'] == 2
    
    def test_limit_configuration(self, service):
        """测试限制配置的正确性"""
        from config import config as cfg
        assert cfg.ANONYMOUS_TRIAL_LIMIT == 2
        assert cfg.ANONYMOUS_COOKIE_MAX_AGE == 365 * 24 * 3600  # 1年


if __name__ == '__main__':
    pytest.main([__file__])
