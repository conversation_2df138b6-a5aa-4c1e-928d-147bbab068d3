// Centralized event-only logger to avoid exposing sensitive API details
// In production, logs are minimal event slugs; in development, you can enable
// verbose mode by setting localStorage.debug_verbose = '1' or VITE_DEBUG_VERBOSE=1

const isProd = typeof import.meta !== 'undefined' && import.meta.env && import.meta.env.PROD
const verboseEnv = typeof import.meta !== 'undefined' && import.meta.env && import.meta.env.VITE_DEBUG_VERBOSE === '1'

function isVerboseEnabled() {
  try {
    if (verboseEnv) return true
    if (typeof window !== 'undefined' && window.localStorage) {
      return window.localStorage.getItem('debug_verbose') === '1'
    }
  } catch {}
  return false
}

function safeLog(fn, message) {
  try {
    fn.call(console, message)
  } catch {}
}

export const logger = Object.freeze({
  // Record a non-sensitive event. Do not include URLs, payloads, tokens, or PII.
  event(eventName) {
    if (!eventName) return
    safeLog(console.log, `[event] ${eventName}`)
  },

  // Record a non-sensitive warning event
  warn(eventName) {
    if (!eventName) return
    safeLog(console.warn, `[warn] ${eventName}`)
  },

  // Record a non-sensitive error event
  error(eventName) {
    if (!eventName) return
    safeLog(console.error, `[error] ${eventName}`)
  },

  // Optional debug logs; only emitted when verbose mode is explicitly enabled
  debug(eventName) {
    if (!isVerboseEnabled()) return
    if (!eventName) return
    safeLog(console.log, `[debug] ${eventName}`)
  },
})

export default logger

