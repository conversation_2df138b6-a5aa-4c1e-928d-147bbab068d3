import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from services.reddit_service import RedditService
from models.reddit_models import RedditPost, RedditComment


class TestRedditServiceEnhanced:
    """Enhanced tests for Reddit service focusing on core functionality"""

    @pytest.fixture
    def reddit_service(self):
        return RedditService()

    @pytest.fixture
    def mock_reddit_post_data(self):
        return {
            'id': 'test_post_123',
            'title': 'Test Reddit Post',
            'selftext': 'This is a test post content',
            'author': 'test_author',
            'subreddit': 'testsubreddit',
            'score': 100,
            'num_comments': 5,
            'created_utc': 1700000000,
            'url': 'https://reddit.com/r/testsubreddit/comments/test_post_123/',
            'permalink': '/r/testsubreddit/comments/test_post_123/'
        }

    @pytest.fixture
    def mock_reddit_comment_data(self):
        return {
            'id': 'comment_123',
            'body': 'This is a test comment',
            'author': 'comment_author',
            'score': 25,
            'created_utc': 1700001000,
            'permalink': '/r/testsubreddit/comments/test_post_123/comment_123/'
        }

    def test_parse_reddit_url_valid_post(self, reddit_service):
        """Test parsing valid Reddit post URL"""
        url = "https://www.reddit.com/r/Python/comments/abc123/test_post/"
        result = reddit_service.parse_reddit_url(url)
        
        assert result is not None
        assert result['subreddit'] == 'Python'
        assert result['post_id'] == 'abc123'

    def test_parse_reddit_url_valid_comment(self, reddit_service):
        """Test parsing valid Reddit comment URL"""
        url = "https://www.reddit.com/r/Python/comments/abc123/test_post/def456/"
        result = reddit_service.parse_reddit_url(url)

        assert result is not None
        assert result['type'] == 'post'
        assert result['subreddit'] == 'Python'
        assert result['post_id'] == 'abc123'
        assert result['url'] == url

    def test_parse_reddit_url_invalid(self, reddit_service):
        """Test parsing invalid Reddit URL"""
        invalid_urls = [
            "https://google.com",
            "not_a_url",
            "https://reddit.com/invalid",
            ""
        ]

        for url in invalid_urls:
            result = reddit_service.parse_reddit_url(url)
            assert result['type'] == 'unknown'

    def test_extract_post_id_from_url(self, reddit_service):
        """Test extracting post ID from various URL formats using parse_reddit_url"""
        test_cases = [
            ("https://www.reddit.com/r/Python/comments/abc123/", "abc123"),
            ("https://reddit.com/r/test/comments/xyz789/title/", "xyz789"),
            ("https://old.reddit.com/r/sub/comments/def456/", "def456"),
        ]

        for url, expected_id in test_cases:
            result = reddit_service.parse_reddit_url(url)
            assert result is not None
            assert result['post_id'] == expected_id

    def test_extract_post_id_invalid_url(self, reddit_service):
        """Test extracting post ID from invalid URLs"""
        invalid_urls = [
            "https://google.com",
            "not_a_url",
            "https://reddit.com/invalid_format"
        ]

        for url in invalid_urls:
            result = reddit_service.parse_reddit_url(url)
            assert result['type'] == 'unknown'

    def test_is_valid_reddit_url(self, reddit_service):
        """Test Reddit URL validation using parse_reddit_url"""
        valid_urls = [
            "https://www.reddit.com/r/Python/comments/abc123/",
            "https://reddit.com/r/test/comments/xyz789/title/",
            "https://old.reddit.com/r/sub/comments/def456/"
        ]

        invalid_urls = [
            "https://google.com",
            "not_a_url",
            "https://reddit.com/invalid",
            ""
        ]

        for url in valid_urls:
            result = reddit_service.parse_reddit_url(url)
            assert result is not None
            assert result['type'] == 'post'

        for url in invalid_urls:
            result = reddit_service.parse_reddit_url(url)
            if result is None:
                assert True  # Some invalid URLs return None
            else:
                assert result['type'] == 'unknown'

    @pytest.mark.asyncio
    async def test_get_post_details_invalid_url(self, reddit_service):
        """Test post details fetching with invalid URL"""
        url = "https://google.com"
        result = await reddit_service.get_post_details(url)

        assert result is None

    @pytest.mark.asyncio
    async def test_get_post_comments_invalid_url(self, reddit_service):
        """Test comments fetching with invalid URL"""
        url = "https://google.com"
        result = await reddit_service.get_post_comments(url)

        assert result == []

    @pytest.mark.asyncio
    async def test_get_user_overview_success(self, reddit_service):
        """Test successful user overview fetching"""
        with patch.object(reddit_service, '_ensure_async_reddit') as mock_reddit:
            mock_item = Mock()
            mock_item.id = 'item_123'
            mock_item.body = 'Test comment'
            mock_item.author.name = 'test_user'
            mock_item.score = 10
            mock_item.created_utc = 1700000000
            mock_item.subreddit.display_name = 'testsubreddit'

            mock_redditor = Mock()
            mock_redditor.top = AsyncMock()
            mock_redditor.top.return_value.__aiter__ = AsyncMock(return_value=iter([mock_item]))

            mock_reddit_client = Mock()
            mock_reddit_client.redditor.return_value = mock_redditor
            mock_reddit.return_value = mock_reddit_client

            result = await reddit_service.get_user_overview('test_user', max_items=5)

            assert result is not None
            assert 'comments' in result or 'posts' in result

    @pytest.mark.asyncio
    async def test_get_user_overview_not_found(self, reddit_service):
        """Test user overview fetching when user not found"""
        with patch.object(reddit_service, '_ensure_async_reddit') as mock_reddit:
            mock_reddit.side_effect = Exception("User not found")

            result = await reddit_service.get_user_overview('nonexistent_user')

            assert result is None

    @pytest.mark.asyncio
    async def test_close_service(self, reddit_service):
        """Test closing the Reddit service"""
        # Mock the async reddit client
        reddit_service._async_initialized = True
        reddit_service.async_reddit = Mock()
        reddit_service.async_reddit.close = AsyncMock()

        await reddit_service.close()

        reddit_service.async_reddit.close.assert_called_once()
        assert reddit_service.async_reddit is None
        assert reddit_service._async_initialized is False

    def test_service_initialization(self, reddit_service):
        """Test Reddit service initialization"""
        assert reddit_service.logger is not None
        assert hasattr(reddit_service, 'configured')
        assert hasattr(reddit_service, 'async_reddit')
        assert hasattr(reddit_service, '_async_initialized')

    def test_parse_reddit_url_edge_cases(self, reddit_service):
        """Test Reddit URL parsing edge cases"""
        edge_cases = [
            ("https://www.reddit.com/r/Python/comments/abc123", "abc123"),  # No trailing slash
            ("https://old.reddit.com/r/test/comments/xyz789/", "xyz789"),   # Old Reddit
            ("https://reddit.com/r/sub/comments/def456/title_here/", "def456"),  # With title
        ]

        for url, expected_id in edge_cases:
            result = reddit_service.parse_reddit_url(url)
            if result and result['type'] == 'post':
                assert result['post_id'] == expected_id

    @pytest.mark.asyncio
    async def test_context_manager(self, reddit_service):
        """Test Reddit service as async context manager"""
        with patch.object(reddit_service, '_ensure_async_reddit') as mock_ensure:
            with patch.object(reddit_service, 'close') as mock_close:
                mock_ensure.return_value = Mock()
                mock_close.return_value = None

                async with reddit_service as service:
                    assert service is reddit_service
                    mock_ensure.assert_called_once()

                mock_close.assert_called_once()
