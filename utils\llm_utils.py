from typing import Any, Dict, List


def extract_high_score_content(user_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Extract top posts/comments by score, limited to top 20 items.
    Pure function with no logging/side effects.
    """
    high_score_content: List[Dict[str, Any]] = []

    for subreddit, sub_data in (user_data or {}).items():
        posts = (sub_data or {}).get("posts", [])
        for post in posts:
            high_score_content.append({
                "type": "post",
                "content": f"{post.get('title', '') or ''} {post.get('selftext', '') or ''}",
                "score": post.get("score", 0) or 0,
                "subreddit": subreddit,
                "created_utc": post.get("created_utc", 0) or 0,
            })
        comments = (sub_data or {}).get("comments", [])
        for comment in comments:
            high_score_content.append({
                "type": "comment",
                "content": comment.get("body", "") or "",
                "score": comment.get("score", 0) or 0,
                "subreddit": subreddit,
                "created_utc": comment.get("created_utc", 0) or 0,
            })

    high_score_content.sort(key=lambda x: x.get("score", 0) or 0, reverse=True)
    return high_score_content[:20]


def process_credibility_results(results: List[Any]) -> Dict[str, Any]:
    """Reduce a list of per-user credibility results to a username->result mapping."""
    out: Dict[str, Any] = {}
    for result in results or []:
        if isinstance(result, dict) and "username" in result:
            out[result["username"]] = result
    return out


def generate_credibility_analysis_summary(credibility_results: Dict[str, Any]) -> Dict[str, Any]:
    """Summarize analysis outcome counts in a deterministic structure."""
    summary = {
        "total_users_analyzed": len(credibility_results or {}),
        "successful_analyses": 0,
        "failed_analyses": 0,
        "users_with_expertise": 0,
        "users_with_background_info": 0,
        "users_with_worldview_info": 0,
    }
    for _, result in (credibility_results or {}).items():
        if isinstance(result, dict) and "error" not in result:
            summary["successful_analyses"] += 1
            if (result.get("expertise", {}) or {}).get("summary"):
                summary["users_with_expertise"] += 1
            if (result.get("background_similarity", {}) or {}).get("summary"):
                summary["users_with_background_info"] += 1
            if (result.get("worldview", {}) or {}).get("summary"):
                summary["users_with_worldview_info"] += 1
        else:
            summary["failed_analyses"] += 1
    return summary