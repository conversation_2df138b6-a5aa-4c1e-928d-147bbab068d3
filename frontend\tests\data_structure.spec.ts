import { test, expect } from '@playwright/test'

// Mock different data structures that backend might return
const mockSessionWithEmptyPosts = {
  success: true,
  data: {
    id: 'search_123',
    query: '产品市场匹配之前如何迭代产品，思考产品方向',
    reddit_posts: [], // Empty array - this is the actual issue
    reddit_comments: [],
    success: true,
    reddit_posts_count: 0,
    reddit_comments_count: 0
  }
}

const mockSessionWithRawDataString = {
  success: true,
  data: {
    id: 'search_456',
    query: '产品市场匹配之前如何迭代产品，思考产品方向',
    reddit_posts: [], // Empty at top level
    reddit_comments: [],
    raw_data: JSON.stringify({
      reddit_posts: [
        {
          id: 'p1',
          title: 'How to iterate product before PMF',
          author: 'startup_guru',
          subreddit: 'startups',
          score: 150,
          url: 'https://reddit.com/r/startups/p1',
          permalink: '/r/startups/comments/p1'
        }
      ],
      reddit_comments: [
        {
          id: 'c1',
          post_id: 'p1',
          body: 'The key is to talk to customers early and often...',
          author: 'product_expert',
          score: 50
        }
      ],
      commenters_history: {
        product_expert: { comment_karma: 5000 }
      }
    })
  }
}

const mockSessionWithRawDataDict = {
  success: true,
  data: {
    id: 'search_789',
    query: 'PMF strategies',
    reddit_posts: [],
    raw_data: { // Direct object, not string
      reddit_posts: [
        {
          id: 'p2',
          title: 'Product Market Fit strategies',
          author: 'entrepreneur123',
          subreddit: 'entrepreneur',
          score: 200
        }
      ],
      reddit_comments: []
    }
  }
}

const mockSessionWithNormalData = {
  success: true,
  data: {
    id: 'search_normal',
    query: 'test query',
    reddit_posts: [
      {
        id: 'p3',
        title: 'Normal post structure',
        author: 'user123',
        subreddit: 'test',
        score: 100
      }
    ],
    reddit_comments: [
      {
        id: 'c3',
        post_id: 'p3',
        body: 'Great post!',
        author: 'commenter1',
        score: 10
      }
    ],
    commenters_history: {
      commenter1: { comment_karma: 1000 }
    }
  }
}

// 新增：数据在顶层 reddit_data.posts/comments 结构
const mockSessionWithRedditDataTopLevel = {
  success: true,
  data: {
    id: 'search_rd_top',
    query: 'reddit_data top level',
    reddit_posts: [],
    reddit_comments: [],
    reddit_data: {
      posts: [
        {
          id: 'p_rd_1',
          title: 'Post from reddit_data',
          author: 'rd_user',
          subreddit: 'startups',
          score: 77,
          permalink: '/r/startups/comments/p_rd_1'
        }
      ],
      comments: [
        {
          id: 'c_rd_1',
          post_id: 'p_rd_1',
          body: 'Comment from reddit_data',
          author: 'rd_commenter',
          score: 5,
          permalink: '/r/startups/comments/p_rd_1/c_rd_1'
        }
      ],
      user_histories: {
        rd_commenter: { comment_karma: 321 }
      }
    }
  }
}

// 新增：数据在 raw_data.reddit_data 结构
const mockSessionWithRedditDataInRawData = {
  success: true,
  data: {
    id: 'search_rd_raw',
    query: 'reddit_data in raw',
    reddit_posts: [],
    reddit_comments: [],
    raw_data: JSON.stringify({
      reddit_data: {
        posts: [
          {
            id: 'p_rd_raw_1',
            title: 'Post from raw_data.reddit_data',
            author: 'raw_user',
            subreddit: 'entrepreneur',
            score: 88,
            permalink: '/r/entrepreneur/comments/p_rd_raw_1'
          }
        ],
        comments: [
          {
            id: 'c_rd_raw_1',
            post_id: 'p_rd_raw_1',
            body: 'Comment from raw_data.reddit_data',
            author: 'raw_commenter',
            score: 6,
            permalink: '/r/entrepreneur/comments/p_rd_raw_1/c_rd_raw_1'
          }
        ],
        user_histories: {
          raw_commenter: { comment_karma: 456 }
        }
      }
    })
  }
}

test.describe('Data structure handling', () => {
  test.beforeEach(async ({ page }) => {
    // Mock health and auth endpoints
    await page.route('**/api/health', route => 
      route.fulfill({ json: { status: 'healthy' } })
    )
    await page.route('**/api/auth/me', route => 
      route.fulfill({ json: { authenticated: false } })
    )
  })

  test('should handle empty reddit_posts array', async ({ page }) => {
    // Mock the session endpoint to return empty posts
    await page.route('**/api/sessions/search_123', route => 
      route.fulfill({ json: mockSessionWithEmptyPosts })
    )

    // Navigate directly to results page with session ID
    await page.goto('/#/results', { 
      waitUntil: 'networkidle' 
    })
    
    // Manually set the location state since we can't pass it through URL
    await page.evaluate((sessionId) => {
      window.history.replaceState(
        { sessionId },
        '',
        window.location.href
      )
      // Trigger a re-render
      window.dispatchEvent(new PopStateEvent('popstate'))
    }, 'search_123')

    // Should show "No results yet" message
    await expect(page.getByText('No results yet')).toBeVisible({ timeout: 5000 })
    await expect(page.getByText('Try another search')).toBeVisible()
  })

  test('should extract data from raw_data JSON string', async ({ page }) => {
    // Mock the session endpoint to return data in raw_data as string
    await page.route('**/api/sessions/search_456', route => 
      route.fulfill({ json: mockSessionWithRawDataString })
    )

    await page.goto('/#/results')
    
    await page.evaluate((sessionId) => {
      window.history.replaceState(
        { sessionId },
        '',
        window.location.href
      )
      window.dispatchEvent(new PopStateEvent('popstate'))
    }, 'search_456')

    // Should display the post from raw_data
    await expect(page.getByText('Featured Comment')).toBeVisible({ timeout: 5000 })
    await expect(page.getByText('How to iterate product before PMF')).toBeVisible()
    await expect(page.getByText('The key is to talk to customers')).toBeVisible()
  })

  test('should extract data from raw_data object', async ({ page }) => {
    // Mock the session endpoint to return data in raw_data as object
    await page.route('**/api/sessions/search_789', route => 
      route.fulfill({ json: mockSessionWithRawDataDict })
    )

    await page.goto('/#/results')
    
    await page.evaluate((sessionId) => {
      window.history.replaceState(
        { sessionId },
        '',
        window.location.href
      )
      window.dispatchEvent(new PopStateEvent('popstate'))
    }, 'search_789')

    // Should display the post from raw_data
    await expect(page.getByText('Product Market Fit strategies')).toBeVisible({ timeout: 5000 })
  })

  test('should handle normal data structure', async ({ page }) => {
    // Mock the session endpoint to return normal structure
    await page.route('**/api/sessions/search_normal', route => 
      route.fulfill({ json: mockSessionWithNormalData })
    )

    await page.goto('/#/results')
    
    await page.evaluate((sessionId) => {
      window.history.replaceState(
        { sessionId },
        '',
        window.location.href
      )
      window.dispatchEvent(new PopStateEvent('popstate'))
    }, 'search_normal')

    // Should display the posts normally
    await expect(page.getByText('Normal post structure')).toBeVisible({ timeout: 5000 })
    await expect(page.getByText('Great post!')).toBeVisible()
  })

  test('should extract data from top-level reddit_data.posts/comments', async ({ page }) => {
    await page.route('**/api/sessions/search_rd_top', route => 
      route.fulfill({ json: mockSessionWithRedditDataTopLevel })
    )

    await page.goto('/#/results')

    await page.evaluate((sessionId) => {
      window.history.replaceState(
        { sessionId },
        '',
        window.location.href
      )
      window.dispatchEvent(new PopStateEvent('popstate'))
    }, 'search_rd_top')

    await expect(page.getByText('Featured Comment')).toBeVisible({ timeout: 5000 })
    await expect(page.getByText('Post from reddit_data')).toBeVisible()
    await expect(page.getByText('Comment from reddit_data')).toBeVisible()
  })

  test('should extract data from raw_data.reddit_data.posts/comments', async ({ page }) => {
    await page.route('**/api/sessions/search_rd_raw', route => 
      route.fulfill({ json: mockSessionWithRedditDataInRawData })
    )

    await page.goto('/#/results')

    await page.evaluate((sessionId) => {
      window.history.replaceState(
        { sessionId },
        '',
        window.location.href
      )
      window.dispatchEvent(new PopStateEvent('popstate'))
    }, 'search_rd_raw')

    await expect(page.getByText('Featured Comment')).toBeVisible({ timeout: 5000 })
    await expect(page.getByText('Post from raw_data.reddit_data')).toBeVisible()
    await expect(page.getByText('Comment from raw_data.reddit_data')).toBeVisible()
  })

  test('should log proper debugging info for empty posts', async ({ page }) => {
    const consoleLogs: string[] = []
    
    // Capture console logs
    page.on('console', msg => {
      if (msg.type() === 'log') {
        consoleLogs.push(msg.text())
      }
    })

    await page.route('**/api/sessions/search_123', route => 
      route.fulfill({ json: mockSessionWithEmptyPosts })
    )

    await page.goto('/#/results')
    
    await page.evaluate((sessionId) => {
      window.history.replaceState(
        { sessionId },
        '',
        window.location.href
      )
      window.dispatchEvent(new PopStateEvent('popstate'))
    }, 'search_123')

    // Wait for logs to be captured
    await page.waitForTimeout(1000)

    // Verify that proper debugging logs are present
    const hasRawDataLog = consoleLogs.some(log => log.includes('session.raw_data:'))
    const hasRedditPostsLog = consoleLogs.some(log => log.includes('session.reddit_posts:'))
    const hasFinalDataLog = consoleLogs.some(log => log.includes('Final data for processing:'))

    expect(hasRawDataLog).toBe(true)
    expect(hasRedditPostsLog).toBe(true) 
    expect(hasFinalDataLog).toBe(true)
  })
})