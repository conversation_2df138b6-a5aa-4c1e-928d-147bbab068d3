{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "jsx": "react-jsx", "strict": true, "skipLibCheck": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "types": ["@playwright/test", "vite/client", "node"]}, "include": ["src", "tests", "playwright.config.ts"], "exclude": ["node_modules", "dist"]}