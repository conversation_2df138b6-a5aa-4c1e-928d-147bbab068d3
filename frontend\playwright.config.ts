import { defineConfig, devices } from '@playwright/test'

const disableTopLevelWebServer = process.env.PW_DISABLE_WEBSERVER === '1'

export default defineConfig({
  testDir: './tests',
  timeout: 60000, // 增加测试超时时间到60秒
  retries: process.env.CI ? 2 : 1, // 本地也允许重试
  use: {
    headless: true,
    baseURL: 'http://127.0.0.1:5173',
    viewport: { width: 1280, height: 800 },
    trace: 'retain-on-failure',
    actionTimeout: 15000, // 增加操作超时时间
    navigationTimeout: 30000, // 增加导航超时时间
  },
  webServer: disableTopLevelWebServer ? undefined : {
    command: 'npm run preview:pw',
    port: 5173,
    reuseExistingServer: false,
    timeout: 180000, // 增加服务器启动超时时间到3分钟
  },
  // 配置项目以支持不同的测试类型
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'mobile',
      use: { ...devices['iPhone 12'] },
    },
    // 新增：真正的端到端测试项目
    {
      name: 'e2e-real-backend',
      use: { 
        ...devices['Desktop Chrome'],
        baseURL: 'http://127.0.0.1:5173',
      },
      testMatch: '**/real-e2e.spec.ts',
      // 不启动webServer，假设前端和后端都已运行
      webServer: null,
    },
  ],
})
