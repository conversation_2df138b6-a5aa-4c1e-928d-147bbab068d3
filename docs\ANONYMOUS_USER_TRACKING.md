# 匿名用户跟踪功能

## 概述

CogBridges现在支持完整的匿名用户搜索会话跟踪，确保未登录用户的使用情况和查询结果也被计入数据库，方便后续分析用户流失原因和优化转化策略。

## 功能特性

### 1. 匿名用户会话保存
- 匿名用户的每次搜索都会被保存到数据库
- 包含完整的搜索查询、结果、性能指标等信息
- 支持匿名用户cookie ID跟踪

### 2. 数据分析API
- `POST /api/analytics/visit` - 记录站点访问（访问->搜索->注册漏斗）。数据仅落库到 `site_visits` 表。
- `GET /api/analytics/anonymous` - 匿名用户使用分析
- `GET /api/analytics/conversion` - 转化分析
- `GET /api/analytics/retention` - 用户留存分析

### 3. 数据库字段扩展
- `anonymous_cookie_id` - 匿名用户cookie标识
- `is_anonymous` - 是否为匿名用户标志
- 新增索引优化查询性能

## 实现细节

### 数据库模型更新（标准）

`SearchSession`表新增字段：

```sql
-- 匿名用户标识字段
anonymous_cookie_id VARCHAR(100) NULL,
is_anonymous BOOLEAN DEFAULT FALSE,

-- 新增索引
CREATE INDEX ix_search_sessions_anonymous_created 
ON search_sessions (anonymous_cookie_id, created_at);

-- 新增站点访问表
CREATE TABLE IF NOT EXISTS site_visits (
  id SERIAL PRIMARY KEY,
  anonymous_cookie_id VARCHAR(100) NULL,
  ip_hash VARCHAR(128) NULL,
  user_agent TEXT NULL,
  referrer TEXT NULL,
  landing_path TEXT NULL,
  utm_source VARCHAR(100) NULL,
  utm_medium VARCHAR(100) NULL,
  utm_campaign VARCHAR(100) NULL,
  created_at TIMESTAMP DEFAULT NOW()
);
CREATE INDEX IF NOT EXISTS ix_site_visits_cookie_created ON site_visits (anonymous_cookie_id, created_at);
CREATE INDEX IF NOT EXISTS ix_site_visits_created ON site_visits (created_at);
```

### 搜索流程更新

1. **匿名用户检测**：通过`owner_user_id`判断是否为匿名用户
2. **Cookie跟踪**：为匿名用户生成唯一cookie ID
3. **数据保存**：搜索完成后保存到数据库，包含匿名用户标识
4. **限制管理**：通过匿名限制服务控制使用次数

### 分析功能

#### 匿名用户使用分析
```bash
GET /api/analytics/anonymous?days=30&limit=100
```

> 注意：历史上曾尝试引入 `visit_events` 表用于细粒度事件，但目前已统一为 `site_visits`（只记录一次进入站点的访问）。`visit_events` 如仍存在于数据库，可保留为空或安全删除以避免混淆。

返回数据包括：
- 总搜索数、成功/失败统计
- 按日期分组的统计数据
- 热门查询排行
- 最近会话详情

#### 转化分析
```bash
GET /api/analytics/conversion?days=30
```

返回数据包括：
- 匿名用户使用统计
- 搜索次数分布
- 转化指标和建议
- 访问->搜索->注册漏斗（UV/访问数、匿名搜索数、新注册数）

#### 留存分析
```bash
GET /api/analytics/retention?days=30
```

返回数据包括：
- 匿名用户vs注册用户对比
- 用户留存模式分析
- 优化建议

## 部署步骤

### 1. 数据库迁移

运行迁移脚本添加新字段：

```bash
python scripts/migrate_add_site_visits.py
```

### 2. 重启服务

确保新的分析蓝图被正确加载：

```bash
# 重启API服务
python api_server.py
```

### 3. 验证功能

运行测试确保功能正常：

```bash
pytest tests/test_anonymous_user_tracking.py -v
```

## 使用示例

### 前端集成

```javascript
// 获取匿名用户分析数据
async function getAnonymousAnalytics() {
    const response = await fetch('/api/analytics/anonymous?days=7');
    const data = await response.json();
    
    if (data.success) {
        console.log('匿名用户统计:', data.summary);
        console.log('每日趋势:', data.daily_stats);
        console.log('热门查询:', data.popular_queries);
    }
}

// 获取转化分析
async function getConversionAnalytics() {
    const response = await fetch('/api/analytics/conversion?days=30');
    const data = await response.json();
    
    if (data.success) {
        console.log('转化指标:', data.conversion_metrics);
        console.log('优化建议:', data.recommendations);
    }
}
```

### 数据分析洞察

通过分析匿名用户数据，可以：

1. **识别流失原因**
   - 分析失败搜索的模式
   - 了解用户查询意图
   - 发现产品功能缺陷

2. **优化转化策略**
   - 针对高频查询优化结果
   - 改进用户引导流程
   - 个性化推荐内容

3. **提升用户体验**
   - 优化搜索算法
   - 改进错误处理
   - 增强功能可用性

## 配置选项

### 环境变量

```bash
# 匿名用户cookie配置
ANONYMOUS_COOKIE_NAME=anon_user_id
ANONYMOUS_COOKIE_MAX_AGE=31536000  # 1年

# 匿名用户搜索限制
ANONYMOUS_TRIAL_LIMIT=3  # 免费搜索次数
```

### 数据库配置

```bash
# 启用数据库存储
ENABLE_DATABASE=True

# 存储原始数据（可选）
DB_STORE_RAW_DATA=True
```

## 监控和维护

### 性能监控

- 监控匿名用户查询性能
- 跟踪数据库查询效率
- 观察API响应时间

### 数据清理

定期清理过期数据：

```python
# 在匿名限制服务中
anonymous_limit_service.cleanup_old_records(max_age_days=90)
```

### 安全考虑

- 匿名用户数据不包含个人身份信息
- Cookie ID使用随机生成，无法追踪真实用户
- 支持GDPR合规要求

## 故障排除

### 常见问题

1. **字段不存在错误**
   - 确保运行了数据库迁移脚本
   - 检查数据库连接和权限

2. **分析API返回空数据**
   - 验证匿名用户搜索是否正常保存
   - 检查数据库查询权限

3. **性能问题**
   - 确保索引已正确创建
   - 考虑分页查询大量数据

### 日志分析

查看相关日志：

```bash
# 匿名用户搜索日志
grep "ANON_CHECK" logs/app.log

# 数据库保存日志
grep "搜索会话数据保存成功" logs/app.log

# 分析API日志
grep "analytics" logs/app.log
```

## 未来扩展

### 计划功能

1. **用户行为分析**
   - 搜索路径分析
   - 用户兴趣建模
   - 个性化推荐

2. **A/B测试支持**
   - 匿名用户分组测试
   - 转化率对比分析
   - 功能效果评估

3. **实时分析**
   - 实时用户行为监控
   - 动态优化建议
   - 异常行为检测

## 总结

匿名用户跟踪功能为CogBridges提供了完整的用户行为洞察能力，帮助产品团队：

- 理解用户需求和痛点
- 优化产品功能和用户体验
- 制定有效的转化策略
- 提升整体产品竞争力

通过数据驱动的决策，可以更好地服务用户并实现业务目标。
