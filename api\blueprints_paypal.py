#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Blueprint, request, jsonify
try:
    import paypalrestsdk  # optional in test environment
except Exception:  # pragma: no cover - allow tests without paypal
    paypalrestsdk = None
from config import config as cfg
from api.tokens import verify_token
from utils.logger_utils import get_logger as _get_logger
from api.app import get_cogbridges_service
logger = _get_logger(__name__)
import datetime
from urllib.parse import urlparse
import json

bp_paypal = Blueprint('paypal', __name__)

# Initialize PayPal
if paypalrestsdk is not None:
    paypalrestsdk.configure({
        "mode": cfg.PAYPAL_MODE,  # sandbox or live
        "client_id": cfg.PAYPAL_CLIENT_ID,
        "client_secret": cfg.PAYPAL_CLIENT_SECRET
    })


def _get_current_user_id():
    """Get current user ID from token"""
    auth_header = request.headers.get('Authorization', '')
    if not auth_header.startswith('Bearer '):
        return None
    
    token = auth_header[7:]  # Remove 'Bearer ' prefix
    # verify_token in this project returns an int user_id.
    # Some tests may monkeypatch it to return a dict. Be compatible with both.
    try:
        user_data = verify_token(token)
        # Dict style (tests): { 'user_id': 123 } or { 'uid': 123 } or { 'id': 123 }
        if isinstance(user_data, dict):
            return (
                user_data.get('user_id')
                or user_data.get('uid')
                or user_data.get('id')
            )
        # Int style (prod): 123
        if user_data is not None:
            return int(user_data)
    except Exception:
        pass
    return None


@bp_paypal.route('/api/paypal/create-order', methods=['POST'])
def create_paypal_order():
    """Create PayPal order"""
    try:
        # Verify user identity
        user_id = _get_current_user_id()
        if not user_id:
            return jsonify({"success": False, "error": "Not logged in"}), 401
        
        # Guard: paypal unavailable (allow tests that monkeypatch SDK without real credentials)
        if paypalrestsdk is None:
            return jsonify({"success": False, "error": "Payment service unavailable"}), 503
        
        # Get request parameters
        data = request.get_json() or {}
        plan_id = data.get('plan_id')  # "250", "500", or "1000"
        
        # Validate plan
        if plan_id not in cfg.PAYPAL_PLANS:
            return jsonify({"success": False, "error": "Invalid plan option"}), 400
        
        plan = cfg.PAYPAL_PLANS[plan_id]

        # Resolve dynamic return/cancel URL from request origin (fallback to configured FRONTEND_URL)
        def _resolve_origin() -> str:
            # Priority: Origin header → Referer base → X-Forwarded-Proto/Host → request.host_url → FRONTEND_URL
            try:
                origin = (request.headers.get('Origin') or '').strip()
                if origin:
                    return origin.rstrip('/')
                referer = (request.headers.get('Referer') or '').strip()
                if referer:
                    p = urlparse(referer)
                    if p.scheme and p.netloc:
                        return f"{p.scheme}://{p.netloc}".rstrip('/')
                scheme = (request.headers.get('X-Forwarded-Proto') or request.scheme or 'https').split(',')[0].strip()
                host = (request.headers.get('X-Forwarded-Host') or request.headers.get('Host') or '').split(',')[0].strip()
                if scheme and host:
                    return f"{scheme}://{host}".rstrip('/')
            except Exception:
                pass
            try:
                return (cfg.FRONTEND_URL or '').rstrip('/')
            except Exception:
                return ''

        origin_base = _resolve_origin() or (cfg.FRONTEND_URL or "").rstrip('/')
        # Use hash-based routes to avoid server-side 404s in static hosting
        return_url = f"{origin_base}/#/payment/success"
        cancel_url = f"{origin_base}/#/payment/cancel"
        
        # Create PayPal payment
        payment = paypalrestsdk.Payment({
            "intent": "sale",
            "payer": {
                "payment_method": "paypal"
            },
            "redirect_urls": {
                "return_url": return_url,
                "cancel_url": cancel_url
            },
            "transactions": [{
                "item_list": {
                    "items": [{
                        "name": f"CogBridges Points - {plan['description']}",
                        "sku": f"points_{plan_id}",
                        "price": plan['price'],
                        "currency": "USD",
                        "quantity": 1
                    }]
                },
                "amount": {
                    "total": plan['price'],
                    "currency": "USD"
                },
                "description": f"Purchase {plan['points']} points for CogBridges",
                "custom": json.dumps({
                    "user_id": user_id,
                    "points": plan['points'],
                    "plan_id": plan_id
                })
            }]
        })
        
        if payment.create():
            # Find approval URL
            approval_url = None
            for link in payment.links:
                if link.rel == "approval_url":
                    approval_url = link.href
                    break
            
            if approval_url:
                return jsonify({
                    "success": True,
                    "payment_id": payment.id,
                    "approval_url": approval_url
                })
            else:
                return jsonify({"success": False, "error": "No approval URL found"}), 500
        else:
            logger.error(f"PayPal payment creation failed: {payment.error}")
            return jsonify({"success": False, "error": "Payment creation failed"}), 500
            
    except Exception as e:
        logger.error(f"Failed to create PayPal order: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


@bp_paypal.route('/api/paypal/execute-payment', methods=['POST'])
def execute_paypal_payment():
    """Execute PayPal payment after user approval"""
    try:
        # Verify user identity
        user_id = _get_current_user_id()
        if not user_id:
            return jsonify({"success": False, "error": "Not logged in"}), 401
        
        # Guard: paypal unavailable (allow tests that monkeypatch SDK without real credentials)
        if paypalrestsdk is None:
            return jsonify({"success": False, "error": "Payment service unavailable"}), 503
        
        # Get request parameters
        data = request.get_json() or {}
        payment_id = data.get('payment_id')
        payer_id = data.get('payer_id')
        
        if not payment_id or not payer_id:
            return jsonify({"success": False, "error": "Missing payment_id or payer_id"}), 400
        
        # Find and execute payment
        payment = paypalrestsdk.Payment.find(payment_id)
        
        if payment.execute({"payer_id": payer_id}):
            # Payment successful, add points to user
            try:
                custom_data = json.loads(payment.transactions[0].custom)
                points = custom_data.get('points')
                expected_user_id = custom_data.get('user_id')
                
                # Verify user ID matches
                if str(expected_user_id) != str(user_id):
                    return jsonify({"success": False, "error": "User ID mismatch"}), 400
                
                # Add points to user account
                try:
                    from models.database_models import UserAccount, PointsLedger

                    # Try to use app service first, fallback to direct database access for tests
                    service = get_cogbridges_service()
                    if service and service.data_service and service.data_service.storage_service:
                        with service.data_service.storage_service.get_session() as db_session:
                            user = db_session.get(UserAccount, user_id)
                            if user:
                                # Update points balance
                                user.points_balance = int(user.points_balance or 0) + points
                                # Record ledger entry (use payment id for idempotency)
                                existing_entry = db_session.query(PointsLedger).filter_by(
                                    ref=payment_id
                                ).first()

                                if not existing_entry:
                                    entry = PointsLedger(
                                        user_id=user_id,
                                        delta=points,
                                        reason='purchase',
                                        ref=payment_id,
                                        meta={
                                            'type': 'paypal',
                                            'points': points,
                                            'payment_id': payment_id,
                                            'payer_id': payer_id,
                                            'amount': payment.transactions[0].amount.total
                                        }
                                    )
                                    db_session.add(entry)

                                db_session.commit()

                                return jsonify({
                                    "success": True,
                                    "points_added": points,
                                    "new_balance": user.points_balance
                                })
                            else:
                                return jsonify({"success": False, "error": "User not found"}), 404
                    else:
                        # Fallback for tests - use Flask-SQLAlchemy directly
                        from models.database_models import db
                        user = db.session.get(UserAccount, user_id)
                        if user:
                            # Update points balance
                            user.points_balance = int(user.points_balance or 0) + points
                            # Record ledger entry (use payment id for idempotency)
                            existing_entry = db.session.query(PointsLedger).filter_by(
                                ref=payment_id
                            ).first()

                            if not existing_entry:
                                entry = PointsLedger(
                                    user_id=user_id,
                                    delta=points,
                                    reason='purchase',
                                    ref=payment_id,
                                    meta={
                                        'type': 'paypal',
                                        'points': points,
                                        'payment_id': payment_id,
                                        'payer_id': payer_id,
                                        'amount': payment.transactions[0].amount.total
                                    }
                                )
                                db.session.add(entry)

                            db.session.commit()

                            return jsonify({
                                "success": True,
                                "points_added": points,
                                "new_balance": user.points_balance
                            })
                        else:
                            return jsonify({"success": False, "error": "User not found"}), 404
                except Exception as e:
                    logger.error(f"Database error in PayPal payment execution: {str(e)}")
                    import os
                    if os.getenv('TEST_MODE', 'False') == 'True':
                        return jsonify({"success": False, "error": f"Database error: {str(e)}"}), 500
                    return jsonify({"success": False, "error": "Database error"}), 500
                    
            except Exception as e:
                logger.error(f"Failed to add points after PayPal payment: {str(e)}")
                return jsonify({"success": False, "error": "Failed to add points"}), 500
        else:
            logger.error(f"PayPal payment execution failed: {payment.error}")
            return jsonify({"success": False, "error": "Payment execution failed"}), 500
            
    except Exception as e:
        logger.error(f"Failed to execute PayPal payment: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


@bp_paypal.route('/api/paypal/webhook', methods=['POST'])
def paypal_webhook():
    """Handle PayPal webhook events"""
    try:
        # Guard: paypal unavailable
        if paypalrestsdk is None:
            return jsonify({"success": False, "error": "Payment service unavailable"}), 503
        
        # Get webhook data
        webhook_data = request.get_json()
        
        # For now, just log the webhook event
        # In production, you would verify the webhook signature
        logger.info(f"PayPal webhook received: {webhook_data.get('event_type', 'unknown')}")
        
        # Handle payment completion events
        if webhook_data.get('event_type') == 'PAYMENT.SALE.COMPLETED':
            # This is handled by the execute_payment endpoint
            # Webhooks can be used for additional verification or logging
            pass
        
        return jsonify({"success": True}), 200
        
    except Exception as e:
        logger.error(f"PayPal webhook error: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500
