// 导入axios HTTP客户端库，用于发送API请求
import axios from 'axios'
import logger from '../utils/logger'

// 从环境变量获取API基础URL，默认使用localhost:5000
// 增强：带有候选地址与自动回退策略，解决后端仅绑定127.0.0.1时本机/手机访问不一致的问题
const PRIMARY_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000'

// 为了确保登录态 cookie 能被发送，优先使用“与当前页面同主机”的 API 基址
function getSameHostBaseUrl() {
  try {
    const { protocol, hostname } = window.location
    const port = '5000'
    // 仅在本地/局域网主机时才使用同主机端口，以避免线上 https://domain:5000 超时
    if (!isLocalOrLanHost(hostname)) return null
    return `${protocol}//${hostname}:${port}`
  } catch {
    return null
  }
}

function isLocalOrLanHost(hostname) {
  if (!hostname) return false
  if (hostname === 'localhost' || hostname === '127.0.0.1') return true
  if (/^192\.168\./.test(hostname)) return true
  if (/^10\./.test(hostname)) return true
  if (/^172\.(1[6-9]|2[0-9]|3[0-1])\./.test(hostname)) return true
  return false
}

function computeCandidateBaseUrls(primary) {
  const list = []
  const sameHost = getSameHostBaseUrl()

  // 线上优先用配置的后端地址（Render: https://cogbridges-api.onrender.com）
  if (primary && !list.includes(primary)) list.push(primary)

  // 仅在本地/局域网下才追加同主机端口
  if (sameHost && !list.includes(sameHost)) list.push(sameHost)

  try {
    const currentHost = window.location.hostname || ''
    if (isLocalOrLanHost(currentHost)) {
      // 仅在本地/局域网访问时，增加本地与常见局域网回退
      const u = new URL(primary || 'http://localhost:5000')
      const port = u.port || '5000'
      const proto = u.protocol || 'http:'
      const localhostUrl = `${proto}//localhost:${port}`
      const loopbackUrl = `${proto}//127.0.0.1:${port}`
      if (!list.includes(localhostUrl)) list.push(localhostUrl)
      if (!list.includes(loopbackUrl)) list.push(loopbackUrl)

      const commonIPs = ['*************', '*************', '**********']
      for (const ip of commonIPs) {
        const ipUrl = `http://${ip}:${port}`
        if (!list.includes(ipUrl)) list.push(ipUrl)
      }
    }
  } catch (e) {}

  return list
}

const CANDIDATE_BASE_URLS = computeCandidateBaseUrls(PRIMARY_BASE_URL)
let resolvedBaseURL = null

// Add a promise to track base URL resolution
let baseURLResolutionPromise = null

// 统一的友好错误消息提取
export function getFriendlyErrorMessage(error, fallback = 'Something went wrong. Please try again.') {
  try {
    const status = error?.response?.status
    const serverMsg = error?.response?.data?.error || error?.response?.data?.message
    if (serverMsg) return serverMsg

    const message = (error?.message || '').toLowerCase()
    if (error?.code === 'ECONNABORTED' || message.includes('timeout')) {
      return 'Request timed out. Please try again.'
    }
    if (message.includes('network error')) {
      return 'Cannot reach the server. Please check your network or server status.'
    }
    if (status === 401) return 'Authentication required. Please log in.'
    if (status === 403) return 'Access denied. Please verify your email or log in again.'
    if (status === 404) return 'Not found.'
    if (typeof status === 'number' && status >= 500) return 'Server error. Please try again later.'
  } catch {}
  return fallback
}

async function requestWithFallback(method, url, payload = undefined) {
  // If we're already resolving the base URL, wait for it
  if (baseURLResolutionPromise && !resolvedBaseURL) {
    try {
      await baseURLResolutionPromise
    } catch (e) {
      // Continue with normal flow if resolution failed
    }
  }

  const bases = resolvedBaseURL ? [resolvedBaseURL] : CANDIDATE_BASE_URLS
  let lastError = null
  let successfulBase = null
  
  for (let i = 0; i < bases.length; i++) {
    const base = bases[i]
    try {
      const client = axios.create({
        baseURL: base,
        timeout: url === '/api/health' ? 2000 : 5000, // 健康检查使用更短的超时时间
        headers: { 'Content-Type': 'application/json', ...(authStore.token ? { Authorization: `Bearer ${authStore.token}` } : {}) },
        withCredentials: true,
        // Add validation to prevent axios from throwing on valid HTTP responses
        validateStatus: function (status) {
          return status >= 200 && status < 600; // Don't throw on any HTTP status
        }
      })
      logger.event(`api_request:${method.toUpperCase()}`)
      let resp
      if (method === 'get') {
        resp = await client.get(url)
      } else if (method === 'post') {
        resp = await client.post(url, payload)
      } else if (method === 'delete') {
        resp = await client.delete(url)
      } else {
        resp = await client.request({ method, url, data: payload })
      }
      
      logger.event(`api_response:${method.toUpperCase()}:${resp.status}`)
      
      // Check if this is an actual error response
      if (resp.status >= 400) {
        // This is an HTTP error, but the request reached the server
        const error = new Error(resp.data?.error || resp.data?.message || `Request failed with status ${resp.status}`)
        error.response = resp
        
        // If we've already found a working base or this is the last base, throw the error
        if (resolvedBaseURL === base || i === bases.length - 1) {
          throw error
        }
        
        // Otherwise, try the next base URL
        lastError = error
        logger.warn(`api_http_error:${method.toUpperCase()}:${resp.status}:try_next_base`)
        continue
      }
      
      // Success! Set the resolved base URL if not already set
      if (!resolvedBaseURL) {
        resolvedBaseURL = base
        logger.event('api_base_url_resolved')
      }
      
      return resp
    } catch (err) {
      // This is a network error or timeout
      lastError = err
      
      // If this error has a response, it means we reached the server
      if (err.response) {
        // We got an HTTP error response, which means the server is reachable
        if (!successfulBase && err.response.status !== 404) {
          successfulBase = base
        }
        
        // If this is our known good base URL or the last attempt, throw the error
        if (resolvedBaseURL === base || i === bases.length - 1) {
          logger.error(`api_request_failed_status:${err.response.status}`)
          throw err
        }
      }
      
      logger.warn('api_request_failed_network_or_timeout')
    }
  }
  
  // If we found a base that gave us a non-404 response, use it
  if (successfulBase && !resolvedBaseURL) {
    resolvedBaseURL = successfulBase
    logger.event('api_base_url_resolved_from_error_response')
  }
  
  logger.error('api_all_endpoints_failed')
  throw lastError
}

// Add a function to resolve the base URL on app startup
async function resolveBaseURL() {
  if (resolvedBaseURL) return resolvedBaseURL
  
  if (!baseURLResolutionPromise) {
    baseURLResolutionPromise = (async () => {
      for (const base of CANDIDATE_BASE_URLS) {
        try {
          const client = axios.create({
            baseURL: base,
            timeout: 1500, // 更快的初始连接检查
            headers: { 'Content-Type': 'application/json' }
          })
          
          logger.debug('api_testing_base_url')
          const resp = await client.get('/api/health')
          
          if (resp.status === 200) {
            logger.event('api_base_url_resolved')
            resolvedBaseURL = base
            return base
          }
        } catch (err) {
          logger.debug('api_base_url_test_failed')
        }
      }
      return null
    })()
  }
  
  return baseURLResolutionPromise
}

// Call resolveBaseURL on module load
resolveBaseURL().catch(() => {
  logger.warn('api_resolve_base_url_startup_failed')
})

// Add request deduplication for critical endpoints
const pendingRequests = new Map()

function dedupedRequest(key, requestFn) {
  // Check if there's already a pending request with this key
  if (pendingRequests.has(key)) {
    logger.debug('api_dedup_request')
    return pendingRequests.get(key)
  }
  
  // Create the request promise
  const promise = requestFn()
    .finally(() => {
      // Remove from pending requests after completion
      pendingRequests.delete(key)
    })
  
  // Store the pending request
  pendingRequests.set(key, promise)
  
  return promise
}

// API methods
export const api = {
  // Health check
  async healthCheck() {
    try {
      const response = await requestWithFallback('get', '/api/health')
      return response.data
    } catch (error) {
      // Don't throw error immediately for timeout issues
      // Let the calling code decide based on other API calls
      if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
        logger.warn('api_health_check_timeout')
      }
      throw new Error(getFriendlyErrorMessage(error, 'Request timed out. Please try again.'))
    }
  },

  // Auth
  async register(email, password, code = '') {
    const payload = { email, password }
    if (code) payload.code = code
    
    // Use deduplication for registration requests
    const requestKey = `register:${email}:${code || 'initial'}`
    
    return dedupedRequest(requestKey, async () => {
      const resp = await requestWithFallback('post', '/api/auth/register', payload)
      if (resp?.data?.token) {
        authStore.token = resp.data.token
        try { window.localStorage.setItem('auth_token', resp.data.token) } catch (e) {}
      }
      return resp.data
    })
  },
  async login(email, password) {
    try {
      const resp = await requestWithFallback('post', '/api/auth/login', { email, password })
      // Store token if provided
      if (resp?.data?.token) {
        authStore.token = resp.data.token
        try { window.localStorage.setItem('auth_token', resp.data.token) } catch (e) {}
      }
      return resp.data
    } catch (error) {
      // 登录特殊提示：401 显示“邮箱或密码不正确”，403 引导验证邮箱
      const status = error?.response?.status
      if (status === 401) {
        throw new Error('Invalid email or password')
      }
      if (status === 403) {
        // 透传后续UI可用的信息
        const err = new Error(getFriendlyErrorMessage(error, 'Account not verified. Please verify your email.'))
        err.need_verification = true
        throw err
      }
      throw new Error(getFriendlyErrorMessage(error, 'Login failed. Please try again.'))
    }
  },
  async loginWithGoogle(credential) {
    try {
      const resp = await requestWithFallback('post', '/api/auth/google', { credential })
      // Store token if provided
      if (resp?.data?.token) {
        authStore.token = resp.data.token
        try { window.localStorage.setItem('auth_token', resp.data.token) } catch (e) {}
      }
      return resp.data
    } catch (error) {
      throw new Error(getFriendlyErrorMessage(error, 'Google login failed. Please try again.'))
    }
  },
  async sendCode(email) {
    try {
      const resp = await requestWithFallback('post', '/api/auth/send-code', { email })
      return resp.data
    } catch (error) {
      throw new Error(getFriendlyErrorMessage(error, 'Failed to send verification code.'))
    }
  },
  async verifyCode(email, code) {
    try {
      const resp = await requestWithFallback('post', '/api/auth/verify', { email, code })
      // 若后端返回token（已在后端新增），保存以便跨域环境下使用Authorization header
      if (resp?.data?.token) {
        authStore.token = resp.data.token
        try { window.localStorage.setItem('auth_token', resp.data.token) } catch (e) {}
      }
      return resp.data
    } catch (error) {
      throw new Error(getFriendlyErrorMessage(error, 'Verification failed.'))
    }
  },
  async logout() {
    try {
      const resp = await requestWithFallback('post', '/api/auth/logout')
      authStore.token = null
      try { window.localStorage.removeItem('auth_token') } catch (e) {}
      return resp.data
    } catch (error) {
      throw new Error(getFriendlyErrorMessage(error, 'Logout failed.'))
    }
  },
  async me() {
    try {
      const resp = await requestWithFallback('get', '/api/auth/me')
      return resp.data
    } catch (error) {
      throw new Error(getFriendlyErrorMessage(error, 'Failed to check login status.'))
    }
  },

  // Analytics
  async analyticsVisit(payload) {
    try {
      const resp = await requestWithFallback('post', '/api/analytics/visit', payload)
      return resp.data
    } catch (error) {
      // Do not break UX on analytics failure
      console.warn('Analytics visit reporting failed:', error)
      return { success: false, error: getFriendlyErrorMessage(error, 'Analytics reporting failed') }
    }
  },

  // Search API (async)
  async search(query, clientSessionId = null) {
    try {
      const response = await requestWithFallback('post', '/api/search', {
        query,
        enhanced: true,
        llm_analysis: true,
        ...(clientSessionId ? { client_session_id: clientSessionId } : {})
      })
      return response.data
    } catch (error) {
      const err = new Error(getFriendlyErrorMessage(error, 'Search failed. Please try again.'))
      try {
        err.status = error?.response?.status
        err.retryAfterSeconds = error?.response?.data?.retry_after_seconds
      } catch {}
      throw err
    }
  },

  // Progress
  async getSearchProgress(sessionId) {
    try {
      const response = await requestWithFallback('get', `/api/search/progress/${sessionId}`)
      // Treat 202 as in-progress
      if (response.status === 202) {
        return { success: true, session_id: sessionId, status: 'pending', progress: 0 }
      }
      return response.data
    } catch (error) {
      throw new Error(getFriendlyErrorMessage(error, 'Failed to get progress.'))
    }
  },

  // Cancel an in-flight search session
  async cancelSearch(sessionId) {
    try {
      const response = await requestWithFallback('post', `/api/search/cancel/${sessionId}`)
      return response.data
    } catch (error) {
      throw new Error(getFriendlyErrorMessage(error, 'Failed to cancel.'))
    }
  },

  // Poll until completed
  async pollSearchProgress(sessionId, onProgress = null, maxWaitTime = 300000, pollIntervalMs = 2000) { // 5 minutes timeout
    const startTime = Date.now()

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const progressData = await this.getSearchProgress(sessionId)
        
        if (progressData.success) {
          // Progress callback
          if (onProgress) {
            onProgress(progressData)
          }

          // Check if we need to set anonymous trial cookie
          if (progressData.set_anonymous_trial_cookie) {
            // Set cookie with proper attributes for 1 year
            // Note: We cannot set HttpOnly from JavaScript, but we can set other attributes
            const expiryDate = new Date();
            expiryDate.setFullYear(expiryDate.getFullYear() + 1);
            document.cookie = `anonymous_trial_used=true;expires=${expiryDate.toUTCString()};path=/;SameSite=Lax`;
            logger.debug('anonymous_trial_cookie_set');
          }

          // Completion
          if (progressData.status === 'completed') {
            // Include search_time from the progress response if available
            const result = progressData.result || {}
            if (progressData.search_time !== undefined) {
              result.search_time = progressData.search_time
            }
            return result
          } else if (progressData.status === 'error') {
            throw new Error(progressData.error || 'Search failed')
          }
        } else {
          throw new Error(progressData.error || 'Failed to get progress')
        }

        // Wait
        await new Promise(resolve => setTimeout(resolve, pollIntervalMs))
      } catch (error) {
        console.error('Polling failed:', error)
        throw error
      }
    }

    throw new Error('Search timeout')
  },

  // Detail
  async getResultDetail(sessionId) {
    try {
      logger.event('api_fetch_session_detail')
      const response = await requestWithFallback('get', `/api/sessions/${sessionId}`)
      logger.debug('api_fetch_session_detail_response')
      // Backend returns { success, data }
      const payload = response.data
      logger.debug('api_fetch_session_detail_payload')
      const result = payload?.data ?? payload
      logger.debug('api_fetch_session_detail_return')
      return result
    } catch (error) {
      logger.error('api_fetch_session_detail_error')
      throw new Error(getFriendlyErrorMessage(error, 'Failed to load result detail.'))
    }
  },

  // History
  async getSearchHistory() {
    try {
      const response = await requestWithFallback('get', '/api/history')
      // Normalize to array
      const payload = response.data
      return Array.isArray(payload) ? payload : (payload?.data || [])
    } catch (error) {
      logger.error('api_get_history_error')
      throw new Error(getFriendlyErrorMessage(error, 'Failed to load history.'))
    }
  },
  
  async removeHistory(sessionId) {
    try {
      const response = await requestWithFallback('delete', `/api/sessions/${sessionId}`)
      return response.data
    } catch (error) {
      logger.error('api_delete_history_error')
      throw new Error(getFriendlyErrorMessage(error, 'Failed to delete history.'))
    }
  },
  
  // Feedback
  async sendFeedback({ email, feedback }) {
    try {
      const response = await requestWithFallback('post', '/api/feedback', { email, content: feedback })
      return response.data
    } catch (error) {
      logger.error('api_send_feedback_error')
      throw new Error(getFriendlyErrorMessage(error, 'Failed to send feedback.'))
    }
  },
  
}

// PayPal API
export const createPayPalOrder = async (planId) => {
  const resp = await requestWithFallback('post', '/api/paypal/create-order', { plan_id: planId })
  return resp.data
};

export const executePayPalPayment = async (paymentId, payerId) => {
  const resp = await requestWithFallback('post', '/api/paypal/execute-payment', { payment_id: paymentId, payer_id: payerId })
  return resp.data
};

export const getUserPoints = async () => {
  const resp = await requestWithFallback('get', '/api/user/points')
  return resp.data
};

export const getPointsHistory = async () => {
  const resp = await requestWithFallback('get', '/api/user/points/history')
  return resp.data
};

// Lightweight auth store for UI
export const authStore = {
  token: (typeof window !== 'undefined' && window.localStorage?.getItem('auth_token')) || null,
  authenticated: false,
  user: null
}

export function getDisplayNameByEmail(email) {
  const name = (email || '').split('@')[0] || ''
  return name || email || 'User'
}

export function getAvatarTextByEmail(email) {
  const name = (email || '').split('@')[0] || ''
  if (!name) return 'US'
  const a = name.charAt(0) || 'U'
  const b = name.charAt(1) || 's'
  return `${a}${b}`.toUpperCase()
}

// 导出默认实例
export default api 