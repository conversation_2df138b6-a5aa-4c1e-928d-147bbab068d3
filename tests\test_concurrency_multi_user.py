import asyncio
import time
import json

import pytest

from api.app import create_app
from api.tokens import issue_token


@pytest.fixture()
def client():
    app = create_app()
    app.config.update(TESTING=True)
    with app.test_client() as c:
        yield c


@pytest.fixture()
def setup_users():
    """Create test users for concurrent testing"""
    from api.app import get_cogbridges_service
    from models.database_models import UserAccount
    from werkzeug.security import generate_password_hash
    
    service = get_cogbridges_service()
    if service and service.data_service and service.data_service.storage_service:
        with service.data_service.storage_service.get_session() as session:
            # Create user 101
            user1 = session.query(UserAccount).filter_by(id=101).first()
            if not user1:
                user1 = UserAccount(
                    id=101,
                    email='<EMAIL>',
                    password_hash=generate_password_hash('testpass'),
                    is_active=True,
                    email_verified=True,
                    points_balance=100
                )
                session.add(user1)
            
            # Create user 202
            user2 = session.query(UserAccount).filter_by(id=202).first()
            if not user2:
                user2 = UserAccount(
                    id=202,
                    email='<EMAIL>',
                    password_hash=generate_password_hash('testpass'),
                    is_active=True,
                    email_verified=True,
                    points_balance=100
                )
                session.add(user2)
            
            # Create user 3
            user3 = session.query(UserAccount).filter_by(id=3).first()
            if not user3:
                user3 = UserAccount(
                    id=3,
                    email='<EMAIL>',
                    password_hash=generate_password_hash('testpass'),
                    is_active=True,
                    email_verified=True,
                    points_balance=100
                )
                session.add(user3)
            
            session.commit()


def _start_search(c, q, token=None):
    headers = {}
    if token:
        headers["Authorization"] = f"Bearer {token}"
    r = c.post("/api/search", json={"query": q}, headers=headers)
    assert r.status_code == 200
    payload = r.get_json()
    assert payload.get("success") is True
    return payload["session_id"]


def _poll_until_done(c, sid, timeout=5.0):
    start = time.time()
    status = None
    while time.time() - start < timeout:
        r = c.get(f"/api/search/progress/{sid}")
        if r.status_code == 200:
            data = r.get_json()
            status = data.get("status")
            if status in ("completed", "error", "cancelled"):
                return status, data
        time.sleep(0.1)
    return status, None


def test_concurrent_searches_isolated_between_users(client, setup_users):
    # 两个不同用户（用不同 token 模拟）
    token_a = issue_token(101)
    token_b = issue_token(202)

    # 顺序发起两次搜索；后端会分别在后台线程执行，形成并发
    sid_a = _start_search(client, "gpu buyer guide", token_a)
    sid_b = _start_search(client, "best espresso grinder", token_b)

    assert sid_a and sid_b and sid_a != sid_b

    # 轮询两边进度直至终态
    st_a, data_a = _poll_until_done(client, sid_a, timeout=8.0)
    st_b, data_b = _poll_until_done(client, sid_b, timeout=8.0)

    assert st_a in ("completed", "error", "cancelled")
    assert st_b in ("completed", "error", "cancelled")

    # 不同会话的 result 不应互相混淆
    if data_a and data_a.get("result") and data_b and data_b.get("result"):
        ra = data_a["result"]
        rb = data_b["result"]
        assert ra.get("session_id") != rb.get("session_id")
        # 查询字段不同（如果服务可返回）
        qa = (ra.get("query") or "").lower()
        qb = (rb.get("query") or "").lower()
        if qa and qb:
            assert qa != qb


def test_many_quick_requests_do_not_collide(client, setup_users):
    # 快速连发 5 个请求，确保 session_id 唯一且可进度
    token = issue_token(3)  # 使用用户3的token
    headers = {"Authorization": f"Bearer {token}"}
    
    sids = []
    for i in range(5):
        r = client.post("/api/search", json={"query": f"q{i}"}, headers=headers)
        assert r.status_code == 200
        sids.append(r.get_json()["session_id"])
    assert len(set(sids)) == 5
    # 任意挑两个轮询到终态
    for sid in sids[:2]:
        st, _ = _poll_until_done(client, sid, timeout=6.0)
        assert st in ("completed", "error", "cancelled")