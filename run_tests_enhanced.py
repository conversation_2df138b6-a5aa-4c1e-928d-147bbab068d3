#!/usr/bin/env python3
"""
CogBridges Enhanced Test Runner
增强的测试运行器，提供更好的测试执行体验

功能：
- 分类运行测试（单元测试、集成测试、端到端测试）
- 生成测试报告
- 测试覆盖率分析
- 性能测试
- 并行测试执行
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path
from typing import List, Optional


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.tests_dir = self.project_root / "tests"
        
    def run_command(self, cmd: List[str], capture_output: bool = False) -> subprocess.CompletedProcess:
        """运行命令"""
        print(f"🔧 Running: {' '.join(cmd)}")
        
        if capture_output:
            return subprocess.run(cmd, capture_output=True, text=True, cwd=self.project_root)
        else:
            return subprocess.run(cmd, cwd=self.project_root)
    
    def check_dependencies(self) -> bool:
        """检查测试依赖"""
        print("📋 Checking test dependencies...")
        
        required_packages = ['pytest', 'pytest-cov', 'pytest-xdist', 'pytest-html']
        missing_packages = []
        
        for package in required_packages:
            result = self.run_command([sys.executable, '-c', f'import {package.replace("-", "_")}'], capture_output=True)
            if result.returncode != 0:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ Missing packages: {', '.join(missing_packages)}")
            print("📦 Installing missing packages...")
            
            install_cmd = [sys.executable, '-m', 'pip', 'install'] + missing_packages
            result = self.run_command(install_cmd)
            
            if result.returncode != 0:
                print("❌ Failed to install dependencies")
                return False
        
        print("✅ All dependencies available")
        return True
    
    def check_database(self) -> bool:
        """检查数据库连接"""
        print("🗄️ Checking database connection...")
        
        # 检查DATABASE_URL环境变量
        if not os.getenv('DATABASE_URL'):
            print("⚠️ DATABASE_URL not set, some tests may fail")
            return False
        
        # 尝试连接数据库
        try:
            from services.database_service import DatabaseService
            db_service = DatabaseService()
            if db_service.is_available():
                print("✅ Database connection successful")
                return True
            else:
                print("❌ Database connection failed")
                return False
        except Exception as e:
            print(f"❌ Database check failed: {e}")
            return False
    
    def run_unit_tests(self, verbose: bool = False, coverage: bool = False) -> int:
        """运行单元测试"""
        print("\n🧪 Running unit tests...")
        
        cmd = [
            sys.executable, '-m', 'pytest',
            '-m', 'unit',
            '--tb=short',
            '--maxfail=5'
        ]
        
        if verbose:
            cmd.append('-v')
        
        if coverage:
            cmd.extend(['--cov=services', '--cov=api', '--cov=models', '--cov-report=html', '--cov-report=term'])
        
        result = self.run_command(cmd)
        return result.returncode
    
    def run_integration_tests(self, verbose: bool = False) -> int:
        """运行集成测试"""
        print("\n🔗 Running integration tests...")
        
        cmd = [
            sys.executable, '-m', 'pytest',
            '-m', 'integration',
            '--tb=short',
            '--maxfail=3'
        ]
        
        if verbose:
            cmd.append('-v')
        
        result = self.run_command(cmd)
        return result.returncode
    
    def run_e2e_tests(self, verbose: bool = False) -> int:
        """运行端到端测试"""
        print("\n🎯 Running end-to-end tests...")
        
        cmd = [
            sys.executable, '-m', 'pytest',
            '-m', 'e2e',
            '--tb=short',
            '--maxfail=1'
        ]
        
        if verbose:
            cmd.append('-v')
        
        result = self.run_command(cmd)
        return result.returncode
    
    def run_api_tests(self, verbose: bool = False) -> int:
        """运行API测试"""
        print("\n🌐 Running API tests...")
        
        cmd = [
            sys.executable, '-m', 'pytest',
            '-m', 'api',
            '--tb=short',
            '--maxfail=3'
        ]
        
        if verbose:
            cmd.append('-v')
        
        result = self.run_command(cmd)
        return result.returncode
    
    def run_all_tests(self, verbose: bool = False, coverage: bool = False, parallel: bool = False) -> int:
        """运行所有测试"""
        print("\n🚀 Running all tests...")
        
        cmd = [
            sys.executable, '-m', 'pytest',
            '--tb=short',
            '--maxfail=5'
        ]
        
        if verbose:
            cmd.append('-v')
        
        if coverage:
            cmd.extend(['--cov=services', '--cov=api', '--cov=models', '--cov-report=html', '--cov-report=term'])
        
        if parallel:
            cmd.extend(['-n', 'auto'])  # 自动检测CPU核心数
        
        result = self.run_command(cmd)
        return result.returncode
    
    def run_specific_test(self, test_path: str, verbose: bool = False) -> int:
        """运行特定测试"""
        print(f"\n🎯 Running specific test: {test_path}")
        
        cmd = [
            sys.executable, '-m', 'pytest',
            test_path,
            '--tb=short'
        ]
        
        if verbose:
            cmd.append('-v')
        
        result = self.run_command(cmd)
        return result.returncode
    
    def generate_report(self) -> int:
        """生成测试报告"""
        print("\n📊 Generating test report...")
        
        cmd = [
            sys.executable, '-m', 'pytest',
            '--html=test_report.html',
            '--self-contained-html',
            '--tb=short',
            '--maxfail=10'
        ]
        
        result = self.run_command(cmd)
        
        if result.returncode == 0:
            print("✅ Test report generated: test_report.html")
        else:
            print("❌ Failed to generate test report")
        
        return result.returncode
    
    def run_performance_tests(self) -> int:
        """运行性能测试"""
        print("\n⚡ Running performance tests...")
        
        cmd = [
            sys.executable, '-m', 'pytest',
            '-m', 'slow',
            '--tb=short',
            '--durations=10'  # 显示最慢的10个测试
        ]
        
        result = self.run_command(cmd)
        return result.returncode
    
    def clean_test_artifacts(self) -> None:
        """清理测试产物"""
        print("\n🧹 Cleaning test artifacts...")
        
        artifacts = [
            '.pytest_cache',
            'htmlcov',
            '.coverage',
            'test_report.html',
            '__pycache__'
        ]
        
        for artifact in artifacts:
            artifact_path = self.project_root / artifact
            if artifact_path.exists():
                if artifact_path.is_dir():
                    import shutil
                    shutil.rmtree(artifact_path)
                else:
                    artifact_path.unlink()
                print(f"🗑️ Removed {artifact}")
        
        print("✅ Test artifacts cleaned")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CogBridges Enhanced Test Runner')
    parser.add_argument('--unit', action='store_true', help='Run unit tests only')
    parser.add_argument('--integration', action='store_true', help='Run integration tests only')
    parser.add_argument('--e2e', action='store_true', help='Run end-to-end tests only')
    parser.add_argument('--api', action='store_true', help='Run API tests only')
    parser.add_argument('--performance', action='store_true', help='Run performance tests')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    parser.add_argument('--test', type=str, help='Run specific test file or function')
    parser.add_argument('--report', action='store_true', help='Generate HTML test report')
    parser.add_argument('--coverage', action='store_true', help='Generate coverage report')
    parser.add_argument('--parallel', action='store_true', help='Run tests in parallel')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--clean', action='store_true', help='Clean test artifacts')
    parser.add_argument('--check-deps', action='store_true', help='Check dependencies only')
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    print("🧪 CogBridges Enhanced Test Runner")
    print("=" * 50)
    
    # 清理测试产物
    if args.clean:
        runner.clean_test_artifacts()
        return 0
    
    # 检查依赖
    if args.check_deps:
        success = runner.check_dependencies()
        return 0 if success else 1
    
    # 检查依赖和数据库
    if not runner.check_dependencies():
        return 1
    
    # 检查数据库（非强制）
    runner.check_database()
    
    start_time = time.time()
    exit_code = 0
    
    try:
        if args.unit:
            exit_code = runner.run_unit_tests(args.verbose, args.coverage)
        elif args.integration:
            exit_code = runner.run_integration_tests(args.verbose)
        elif args.e2e:
            exit_code = runner.run_e2e_tests(args.verbose)
        elif args.api:
            exit_code = runner.run_api_tests(args.verbose)
        elif args.performance:
            exit_code = runner.run_performance_tests()
        elif args.test:
            exit_code = runner.run_specific_test(args.test, args.verbose)
        elif args.report:
            exit_code = runner.generate_report()
        elif args.all:
            exit_code = runner.run_all_tests(args.verbose, args.coverage, args.parallel)
        else:
            # 默认运行所有测试
            exit_code = runner.run_all_tests(args.verbose, args.coverage, args.parallel)
    
    except KeyboardInterrupt:
        print("\n⚠️ Tests interrupted by user")
        exit_code = 130
    
    except Exception as e:
        print(f"\n❌ Test runner error: {e}")
        exit_code = 1
    
    finally:
        elapsed = time.time() - start_time
        print(f"\n⏱️ Total time: {elapsed:.2f} seconds")
        
        if exit_code == 0:
            print("✅ All tests completed successfully!")
        else:
            print(f"❌ Tests failed with exit code {exit_code}")
    
    return exit_code


if __name__ == '__main__':
    sys.exit(main())
