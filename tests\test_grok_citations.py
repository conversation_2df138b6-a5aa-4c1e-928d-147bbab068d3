import types

from services.grok_reddit_service import GrokRedditService


class DummyResponse:
    def __init__(self, data, status_code=200):
        self._data = data
        self.status_code = status_code
        self.text = "MOCK"
    def json(self):
        return self._data


class DummySession:
    def __init__(self):
        self.trust_env = None
        self.last_post_args = None
        self.last_post_kwargs = None
    def post(self, *args, **kwargs):
        self.last_post_args = args
        self.last_post_kwargs = kwargs
        # Return citations containing duplicate and unrelated domains
        data = {
            "choices": [
                {"finish_reason": "stop", "message": {"content": "{}"}}
            ],
            "citations": [
                {"url": "https://www.reddit.com/r/China/comments/1LxMxW0/title/", "title": "A"},
                {"url": "https://reddit.com/r/china/comments/1lxmxw0/?tl=zh-hans"},
                {"url": "https://example.com/whatever"},
                "https://reddit.com/r/china/comments/1lxmxw0/",
            ],
        }
        return DummyResponse(data)


def test_grok_search_only_dedup(monkeypatch):
    dummy_session = DummySession()

    # Replace requests.Session with our Dummy
    import requests
    monkeypatch.setattr(requests, "Session", lambda: dummy_session, raising=True)

    import asyncio
    svc = GrokRedditService()
    result = asyncio.run(svc.search_reddit("x", search_only=True))

    assert result["success"] is True
    citations = result.get("citations", [])
    assert len(citations) == 1
    assert citations[0]["url"] == "https://reddit.com/r/china/comments/1lxmxw0"


def test_grok_search_citations_edge_cases(monkeypatch):
    """测试citations去重的边界情况"""
    # 返回重复与变体 reddit 链接
    response_data = {
        "choices": [
            {"finish_reason": "stop", "message": {"content": "{}"}}
        ],
        "citations": [
            {"url": "https://www.reddit.com/r/China/comments/1LxMxW0/title/"},
            {"url": "https://reddit.com/r/china/comments/1lxmxw0/?tl=zh-hans"},
            {"url": "https://reddit.com/r/china/comments/1lxmxw0/title/comment/abc"},
            "https://reddit.com/r/china/comments/1lxmxw0/",
        ],
    }

    class EdgeCaseSession:
        def post(self, url, headers=None, json=None, timeout=None):
            return DummyResponse(response_data)

    import requests
    monkeypatch.setattr(requests, "Session", lambda: EdgeCaseSession(), raising=True)

    import asyncio
    svc = GrokRedditService()
    svc.configured = True  # 强制配置为已配置

    result = asyncio.run(svc.search_reddit("edge case test", search_only=True))

    assert result["success"] is True
    citations = result.get("citations", [])
    # 应该去重为一个唯一的帖子URL
    assert len(citations) == 1
    assert "1lxmxw0" in citations[0]["url"].lower()


def test_grok_search_non_200_response(monkeypatch):
    """测试非200响应的处理"""
    class ErrorSession:
        def post(self, url, headers=None, json=None, timeout=None):
            return DummyResponse({"error": "Rate limit exceeded"}, status_code=429)

    import requests
    monkeypatch.setattr(requests, "Session", lambda: ErrorSession(), raising=True)

    import asyncio
    svc = GrokRedditService()
    svc.configured = True

    result = asyncio.run(svc.search_reddit("error test", search_only=True))

    assert result["success"] is False
    assert "429" in result.get("error", "")


