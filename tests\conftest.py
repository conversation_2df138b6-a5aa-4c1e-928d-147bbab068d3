import os
import socket
import pytest
import logging
import time
from unittest.mock import patch, MagicMock
from datetime import datetime

# 在测试期间禁用日志，避免 atexit 时写入已关闭的流
logging.disable(logging.CRITICAL)

# 基础测试环境变量，避免测试触发真实外部依赖
os.environ.setdefault("TEST_MODE", "True")
# 启用数据库（测试需要真实的 PostgreSQL 连接）
os.environ.setdefault("ENABLE_DATABASE", "True")
# Note: Tests now require actual PostgreSQL connection via DATABASE_URL environment variable

# 测试配置常量
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "TestPass123!"
TEST_SESSION_ID = "test_session_123"


@pytest.fixture(autouse=True)
def _block_external_network(monkeypatch):
	"""禁止外网访问，只允许 127.0.0.1/localhost。
	如需临时放开，可设置环境变量 TEST_ALLOW_NET=1。
	"""
	allow = os.getenv("TEST_ALLOW_NET", "0") == "1"
	if allow:
		return

	real_connect = socket.socket.connect
	real_connect_ex = socket.socket.connect_ex

	def _guarded_connect(self, address):
		host = address[0] if isinstance(address, tuple) and address else address
		host_str = str(host)
		if host_str in ("127.0.0.1", "localhost") or host_str.startswith("::1"):
			return real_connect(self, address)
		raise RuntimeError(f"Network disabled in tests: attempted connect to {host_str}")

	def _guarded_connect_ex(self, address):
		try:
			_guarded_connect(self, address)
			return 0
		except Exception:
			return 111  # ECONNREFUSED-like

	monkeypatch.setattr(socket.socket, "connect", _guarded_connect, raising=True)
	monkeypatch.setattr(socket.socket, "connect_ex", _guarded_connect_ex, raising=True)


@pytest.fixture()
def test_client():
	from api.app import create_app
	app = create_app()
	app.config.update(TESTING=True)
	with app.test_client() as c:
		yield c


@pytest.fixture()
def db_session():
	"""提供与应用共享的数据库会话。"""
	from api.app import get_cogbridges_service
	service = get_cogbridges_service()
	db_service = getattr(getattr(service, 'data_service', None), 'database_service', None)
	if not db_service or not db_service.is_available():
		from services.database_service import DatabaseService
		db_service = DatabaseService()
		service.data_service.storage_service = db_service
	# 为每个测试用例提供干净的数据库：先清空再建表
	try:
		from models.database_models import Base
		Base.metadata.drop_all(bind=db_service.engine)
		Base.metadata.create_all(bind=db_service.engine)
	except Exception:
		# 兜底：直接调用服务方法
		try:
			db_service.drop_tables()
		except Exception:
			pass
		db_service.create_tables()
	session = db_service.SessionLocal()
	try:
		yield session
	finally:
		session.close()


@pytest.fixture()
def auth_headers():
	return {"Authorization": "Bearer test"}


@pytest.fixture()
def mock_user_data():
	"""提供标准的测试用户数据"""
	return {
		"id": 1,
		"email": TEST_USER_EMAIL,
		"password_hash": "hashed_password",
		"email_verified": True,
		"points_balance": 100,
		"created_at": datetime.utcnow()
	}


@pytest.fixture()
def mock_search_result():
	"""提供标准的搜索结果数据"""
	return {
		"session_id": TEST_SESSION_ID,
		"query": "test query",
		"success": True,
		"reddit_posts": [
			{
				"post": {
					"id": "test_post_1",
					"title": "Test Post",
					"selftext": "Test content",
					"author": "test_author",
					"subreddit": "test",
					"score": 100,
					"num_comments": 10,
					"created_utc": 1700000000,
					"url": "https://reddit.com/r/test/comments/test_post_1/",
					"permalink": "/r/test/comments/test_post_1/"
				},
				"comments": [
					{
						"id": "test_comment_1",
						"body": "Test comment",
						"author": "test_commenter",
						"score": 50,
						"created_utc": 1700001000,
						"permalink": "/r/test/comments/test_post_1/test_comment_1/"
					}
				],
				"commenters": ["test_commenter"]
			}
		],
		"commenters_history": {
			"test_commenter": {
				"_metadata": {
					"subreddits": ["test"],
					"total_comments": 100,
					"total_posts": 10
				}
			}
		},
		"llm_analysis": {
			"success": True,
			"analysis_summary": {
				"overall_credibility": "high",
				"key_insights": ["Test insight"]
			}
		}
	}


@pytest.fixture()
def mock_reddit_service():
	"""提供模拟的Reddit服务"""
	with patch('services.reddit_service.RedditService') as mock_service:
		mock_instance = MagicMock()
		mock_service.return_value = mock_instance

		# 配置常用方法的返回值
		mock_instance.configured = True
		mock_instance.check_availability.return_value = True
		mock_instance.parse_reddit_url.return_value = {
			"type": "post",
			"subreddit": "test",
			"post_id": "test_post_1"
		}

		yield mock_instance


@pytest.fixture()
def mock_llm_service():
	"""提供模拟的LLM服务"""
	with patch('services.llm_service.LLMService') as mock_service:
		mock_instance = MagicMock()
		mock_service.return_value = mock_instance

		# 配置常用方法的返回值
		mock_instance.configured = True
		mock_instance.generate_text.return_value = "Generated text response"
		mock_instance.analyze_commenter_credibility.return_value = {
			"expertise_level": "expert",
			"credibility_score": 0.9,
			"specialization": "test domain",
			"background": "Test background"
		}

		yield mock_instance


@pytest.fixture()
def mock_grok_service():
	"""提供模拟的Grok服务"""
	with patch('services.grok_reddit_service.GrokRedditService') as mock_service:
		mock_instance = MagicMock()
		mock_service.return_value = mock_instance

		# 配置常用方法的返回值
		mock_instance.configured = True
		mock_instance.search_reddit.return_value = {
			"success": True,
			"citations": [
				{
					"title": "Test Reddit Post",
					"url": "https://reddit.com/r/test/comments/test_post_1/",
					"source": "Reddit"
				}
			],
			"search_time": 0.5
		}

		yield mock_instance


@pytest.fixture()
def mock_email_service():
	"""提供模拟的邮件服务"""
	with patch('services.email_service.EmailService') as mock_service:
		mock_instance = MagicMock()
		mock_service.return_value = mock_instance

		# 配置邮件发送总是成功
		mock_instance.send_email.return_value = True

		yield mock_instance


@pytest.fixture(autouse=True)
def _cookie_header_to_environ(monkeypatch):
	"""将测试请求中的 Cookie 头拷贝到 WSGI 环境变量 HTTP_COOKIE，
	以兼容 Flask test_client 对 Cookie 的处理方式。"""
	try:
		from flask.testing import FlaskClient
		_original_open = FlaskClient.open
		def _patched_open(self, *args, **kwargs):
			headers = kwargs.get('headers') or {}
			cookie_value = None
			if isinstance(headers, dict):
				cookie_value = headers.get('Cookie') or headers.get('cookie')
			else:
				try:
					for k, v in headers:
						if str(k).lower() == 'cookie':
							cookie_value = v
							break
				except Exception:
					pass
			if cookie_value:
				environ_overrides = kwargs.get('environ_overrides') or {}
				environ_overrides['HTTP_COOKIE'] = cookie_value
				kwargs['environ_overrides'] = environ_overrides
			return _original_open(self, *args, **kwargs)
		monkeypatch.setattr(FlaskClient, 'open', _patched_open, raising=True)
	except Exception:
		pass
