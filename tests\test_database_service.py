"""
CogBridges Search - 存储服务测试
测试存储服务的各项功能
"""

import pytest
import os
import tempfile
from datetime import datetime
from unittest.mock import patch, MagicMock

# 设置测试环境变量
os.environ["ENABLE_DATABASE"] = "True"
os.environ["TEST_MODE"] = "True"

from services.database_service import DatabaseService
from services.data_service import DataService
from models.search_models import SearchQuery, SearchResult
from models.database_models import Base
from config import config


@pytest.fixture
def mock_database_service():
    """模块级：创建模拟的存储服务（供度量测试使用）"""
    with patch('services.storage_service.create_engine') as mock_engine:
        with patch('services.storage_service.sessionmaker') as mock_session:
            # 模拟数据库连接成功
            mock_engine.return_value.connect.return_value.__enter__.return_value.execute.return_value = None
            
            from config import config as app_config
            app_config.ENABLE_DATABASE = True
            app_config.DATABASE_URL = 'postgresql://user:pass@localhost:5432/test_db'

            service = DatabaseService()
            service.engine = mock_engine.return_value
            service.SessionLocal = mock_session.return_value
            
            yield service


@pytest.fixture
def sample_reddit_data():
    """模块级：示例Reddit数据（供度量测试使用）"""
    return {
        "reddit_posts": [
            {
                "id": "test_post_1",
                "title": "Test Post Title",
                "selftext": "Test post content",
                "author": "test_user",
                "score": 100,
                "num_comments": 10,
                "created_utc": 1640995200.0,
                "subreddit": "test",
                "permalink": "/r/test/comments/test_post_1/",
                "url": "https://reddit.com/r/test/comments/test_post_1/"
            }
        ],
        "reddit_comments": [
            {
                "id": "test_comment_1",
                "body": "Test comment content",
                "author": "test_commenter",
                "score": 50,
                "created_utc": **********.0,
                "parent_id": "test_post_1",
                "subreddit": "test",
                "permalink": "/r/test/comments/test_post_1/test_comment_1/"
            }
        ],
        "user_histories": [
            {
                "username": "test_user",
                "total_comments": 100,
                "total_posts": 20,
                "account_created_utc": **********.0,
                "comments_data": [],
                "posts_data": []
            }
        ]
    }


@pytest.fixture
def sample_search_result():
    """模块级示例搜索结果，用于跨测试类复用"""
    query = SearchQuery(
        query="test query",
        timestamp=datetime.now(),
        search_type="reddit",
        max_results=5
    )

    result = SearchResult(
        query=query,
        success=True
    )

    result.google_results = [{
        "title": "Test Title",
        "url": "https://example.com", 
        "snippet": "Test snippet",
        "display_url": "example.com",
        "rank": 1
    }]

    return result


class TestDatabaseService:
    """存储服务测试类"""
    
    @pytest.fixture
    def mock_database_service(self):
        """创建模拟的存储服务"""
        with patch('services.storage_service.create_engine') as mock_engine:
            with patch('services.storage_service.sessionmaker') as mock_session:
                # 模拟数据库连接成功
                mock_engine.return_value.connect.return_value.__enter__.return_value.execute.return_value = None
                
                # 强制开启数据库开关
                from config import config as app_config
                app_config.ENABLE_DATABASE = True
                app_config.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db'

                service = DatabaseService()
                service.engine = mock_engine.return_value
                service.SessionLocal = mock_session.return_value
                
                yield service
    
    @pytest.fixture
    def sample_search_result(self):
        """创建示例搜索结果"""
        query = SearchQuery(
            query="test query",
            timestamp=datetime.now(),
            search_type="reddit",
            max_results=5
        )
        
        result = SearchResult(
            query=query,
            success=True
        )
        
        result.google_results = [{
            "title": "Test Title",
            "url": "https://example.com",
            "snippet": "Test snippet", 
            "display_url": "example.com",
            "rank": 1
        }]
        
        return result
    
    @pytest.fixture
    def sample_reddit_data(self):
        """创建示例Reddit数据"""
        return {
            "reddit_posts": [
                {
                    "id": "test_post_1",
                    "title": "Test Post Title",
                    "selftext": "Test post content",
                    "author": "test_user",
                    "score": 100,
                    "num_comments": 10,
                    "created_utc": 1640995200.0,
                    "subreddit": "test",
                    "permalink": "/r/test/comments/test_post_1/",
                    "url": "https://reddit.com/r/test/comments/test_post_1/"
                }
            ],
            "reddit_comments": [
                {
                    "id": "test_comment_1",
                    "body": "Test comment content",
                    "author": "test_commenter",
                    "score": 50,
                    "created_utc": **********.0,
                    "parent_id": "test_post_1",
                    "subreddit": "test",
                    "permalink": "/r/test/comments/test_post_1/test_comment_1/"
                }
            ],
            "user_histories": [
                {
                    "username": "test_user",
                    "total_comments": 100,
                    "total_posts": 20,
                    "account_created_utc": **********.0,
                    "comments_data": [],
                    "posts_data": []
                }
            ]
        }
    
    def test_database_service_initialization(self):
        """测试存储服务初始化"""
        with patch('services.storage_service.create_engine') as mock_engine:
            with patch('services.storage_service.sessionmaker') as mock_session:
                # 模拟数据库连接成功
                mock_engine.return_value.connect.return_value.__enter__.return_value.execute.return_value = None
                
                from config import config as app_config
                app_config.ENABLE_DATABASE = True
                app_config.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db'

                service = DatabaseService()
                
                assert service.engine is not None
                assert service.SessionLocal is not None
                mock_engine.assert_called_once()
                mock_session.assert_called_once()
    
    def test_database_service_initialization_failure(self):
        """测试存储服务初始化失败"""
        with patch('services.storage_service.create_engine') as mock_engine:
            # 模拟数据库连接失败
            mock_engine.side_effect = Exception("Database connection failed")
            
            from config import config as app_config
            app_config.ENABLE_DATABASE = True
            app_config.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db'

            with pytest.raises(Exception):
                DatabaseService()
    
    def test_create_tables(self, mock_database_service):
        """测试创建数据库表"""
        with patch.object(Base.metadata, 'create_all') as mock_create:
            mock_database_service.create_tables()
            mock_create.assert_called_once_with(bind=mock_database_service.engine)
    
    def test_save_search_session(self, mock_database_service, sample_search_result, sample_reddit_data):
        """测试保存搜索会话"""
        session_id = "test_session_123"
        
        # 模拟数据库会话
        mock_session = MagicMock()
        mock_database_service.SessionLocal.return_value = mock_session
        mock_session.__enter__.return_value = mock_session
        
        result = mock_database_service.save_search_session(
            session_id=session_id,
            search_result=sample_search_result,
            reddit_data=sample_reddit_data
        )
        
        assert result is True
        mock_session.add.assert_called()
        mock_session.commit.assert_called()
    
    def test_load_search_session(self, mock_database_service):
        """测试加载搜索会话"""
        session_id = "test_session_123"
        
        # 模拟数据库查询结果
        mock_session = MagicMock()
        mock_search_session = MagicMock()
        mock_search_session.to_dict.return_value = {
            "id": session_id,
            "query": "test query",
            "success": True
        }
        
        mock_session.query.return_value.filter.return_value.first.return_value = mock_search_session
        mock_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = []
        mock_session.query.return_value.filter.return_value.all.return_value = []
        
        mock_database_service.SessionLocal.return_value = mock_session
        mock_session.__enter__.return_value = mock_session
        
        result = mock_database_service.load_search_session(session_id)
        
        assert result is not None
        assert result["id"] == session_id
        assert result["query"] == "test query"
    
    def test_delete_search_session(self, mock_database_service):
        """测试删除搜索会话"""
        session_id = "test_session_123"
        
        # 模拟数据库查询和删除
        mock_session = MagicMock()
        mock_search_session = MagicMock()
        
        mock_session.query.return_value.filter.return_value.first.return_value = mock_search_session
        mock_database_service.SessionLocal.return_value = mock_session
        mock_session.__enter__.return_value = mock_session
        
        result = mock_database_service.delete_search_session(session_id)
        
        assert result is True
        mock_session.delete.assert_called_with(mock_search_session)
        mock_session.commit.assert_called()
    
    def test_get_database_statistics(self, mock_database_service):
        """测试获取数据库统计"""
        # 模拟数据库查询结果
        mock_session = MagicMock()
        mock_session.query.return_value.count.return_value = 10
        mock_session.query.return_value.order_by.return_value.first.return_value = None
        
        mock_database_service.SessionLocal.return_value = mock_session
        mock_session.__enter__.return_value = mock_session
        
        stats = mock_database_service.get_database_statistics()
        
        assert "database_enabled" in stats
        assert "sessions_count" in stats
        assert stats["sessions_count"] == 10
    
    def test_is_available(self, mock_database_service):
        """测试存储服务可用性检查"""
        from config import config as app_config
        app_config.ENABLE_DATABASE = True
        app_config.DATABASE_URL = 'postgresql://user:pass@localhost:5432/db'

        # 构造可用条件
        mock_database_service.engine = object()
        mock_database_service.SessionLocal = object()
        assert mock_database_service.is_available() is True
        
        # 测试不可用情况
        mock_database_service.engine = None
        assert mock_database_service.is_available() is False


class TestSessionMetrics:
    def test_save_and_load_session_metrics(self, mock_database_service, sample_search_result, sample_reddit_data):
        sid = "metrics_sess_1"
        # 模拟时间度量
        sample_search_result.google_search_time = 0.111
        sample_search_result.reddit_posts_time = 0.222
        sample_search_result.commenters_history_time = 0.333
        sample_search_result.llm_analysis_time = 0.444
        # 总时长
        sample_search_result.total_time = 1.234

        mock_session = MagicMock()
        # 在 add 时捕捉所有对象以供后续断言
        added_objects = []
        def _add(obj):
            added_objects.append(obj)
        mock_session.add.side_effect = _add
        mock_database_service.SessionLocal.return_value = mock_session
        mock_session.__enter__.return_value = mock_session

        ok = mock_database_service.save_search_session(
            session_id=sid,
            search_result=sample_search_result,
            reddit_data=sample_reddit_data
        )
        assert ok is True

        # 找到SearchSession对象（应该是第一个添加的）
        search_session_obj = None
        for obj in added_objects:
            if hasattr(obj, '__class__') and obj.__class__.__name__ == 'SearchSession':
                search_session_obj = obj
                break

        assert search_session_obj is not None, "SearchSession object should be added"

        # 度量值应已写入（毫秒）
        assert search_session_obj.total_time_ms == 1234
        assert search_session_obj.grok_time_ms == 111
        assert search_session_obj.reddit_time_ms == 222
        assert search_session_obj.history_time_ms == 333
        assert search_session_obj.llm_time_ms == 444


class TestDataServiceWithDatabase:
    """测试DataService与数据库的集成"""
    
    @pytest.fixture
    def mock_data_service(self):
        """创建模拟的数据服务"""
        with patch('services.data_service.DatabaseService') as mock_db_service:
            mock_db_instance = MagicMock()
            mock_db_instance.is_available.return_value = True
            mock_db_service.return_value = mock_db_instance
            
            service = DataService()
            service.storage_service = mock_db_instance
            
            yield service, mock_db_instance
    
    def test_save_complete_session_with_database(self, mock_data_service, sample_search_result):
        """测试使用数据库保存完整会话"""
        data_service, mock_db_service = mock_data_service
        
        session_id = "test_session_123"
        data_to_save = {
            "session_id": session_id,
            "search_result": sample_search_result.to_dict(),
            "reddit_data": {},
            "timestamp": datetime.now().isoformat()
        }
        
        mock_db_service.save_search_session.return_value = True
        
        # PostgreSQL-only: 不再写入JSON备份
        result = data_service.save_complete_session(session_id, data_to_save)
        
        # 验证数据库保存被调用
        mock_db_service.save_search_session.assert_called_once()
        
        assert result is not None
    
    def test_load_session_data_from_database(self, mock_data_service):
        """测试从存储服务加载会话数据"""
        data_service, mock_db_service = mock_data_service
        
        session_id = "test_session_123"
        expected_data = {"id": session_id, "query": "test query"}
        
        mock_db_service.load_search_session.return_value = expected_data
        
        result = data_service.load_session_data(session_id)
        
        assert result == expected_data
        mock_db_service.load_search_session.assert_called_once_with(session_id)
    
    def test_get_storage_statistics_db_only(self, mock_data_service):
        """测试获取数据库存储统计（PostgreSQL-only）"""
        data_service, mock_db_service = mock_data_service
        
        mock_db_service.get_database_statistics.return_value = {
            "sessions_count": 10,
            "database_enabled": True
        }
        
        stats = data_service.get_storage_statistics()
        
        assert stats["storage_type"] == "database"
        assert "database" in stats
        assert stats["database"]["sessions_count"] == 10


class TestForeignKeyConstraint:
    """测试外键约束处理"""

    def test_session_creation_order_prevents_foreign_key_violation(self, mock_database_service):
        """测试修复后的保存顺序，确保search_session先于reddit_posts创建，避免外键约束违反"""
        from models.search_models import SearchQuery, SearchResult

        # 创建搜索结果
        query = SearchQuery(
            query="test query for foreign key fix",
            timestamp=datetime.now(),
            search_type="reddit"
        )
        search_result = SearchResult(query=query, success=True)

        # 创建包含帖子的Reddit数据
        reddit_data = {
            "reddit_posts": [
                {
                    "id": "test_post_1",
                    "title": "Test Post Title",
                    "selftext": "Test post content",
                    "author": "test_user",
                    "score": 100,
                    "num_comments": 10,
                    "created_utc": 1640995200.0,
                    "subreddit": "test",
                    "permalink": "/r/test/comments/test_post_1/",
                    "url": "https://reddit.com/r/test/comments/test_post_1/"
                }
            ],
            "reddit_comments": [
                {
                    "id": "test_comment_1",
                    "post_id": "test_post_1",
                    "body": "Test comment content",
                    "author": "test_commenter",
                    "score": 50,
                    "created_utc": **********.0,
                    "subreddit": "test",
                    "permalink": "/r/test/comments/test_post_1/test_comment_1/"
                }
            ]
        }

        # 模拟数据库会话
        mock_session = MagicMock()
        mock_database_service.SessionLocal.return_value = mock_session
        mock_session.__enter__.return_value = mock_session

        # 记录调用顺序
        call_order = []

        def track_add(obj):
            call_order.append(('add', obj.__class__.__name__))

        def track_flush():
            call_order.append(('flush', None))

        mock_session.add.side_effect = track_add
        mock_session.flush.side_effect = track_flush

        # 执行保存
        result = mock_database_service.save_search_session(
            session_id="test_session_order",
            search_result=search_result,
            reddit_data=reddit_data
        )

        assert result is True

        # 验证调用顺序：SearchSession应该先被添加并flush，然后才是RedditPost和RedditComment
        assert len(call_order) >= 4  # 至少有SearchSession add, flush, RedditPost add, RedditComment add

        # 找到SearchSession添加的位置
        search_session_add_idx = None
        first_flush_idx = None
        reddit_post_add_idx = None

        for i, (action, obj_type) in enumerate(call_order):
            if action == 'add' and obj_type == 'SearchSession' and search_session_add_idx is None:
                search_session_add_idx = i
            elif action == 'flush' and first_flush_idx is None:
                first_flush_idx = i
            elif action == 'add' and obj_type == 'RedditPost' and reddit_post_add_idx is None:
                reddit_post_add_idx = i

        # 验证顺序：SearchSession add -> flush -> RedditPost add
        assert search_session_add_idx is not None, "SearchSession should be added"
        assert first_flush_idx is not None, "flush should be called"
        assert reddit_post_add_idx is not None, "RedditPost should be added"

        assert search_session_add_idx < first_flush_idx, "SearchSession should be added before first flush"
        assert first_flush_idx < reddit_post_add_idx, "flush should happen before RedditPost is added"

    def test_comment_without_post_creates_minimal_post(self, mock_database_service):
        """测试当评论引用不存在的帖子时，会创建最小帖子记录"""
        from models.search_models import SearchQuery, SearchResult
        
        # 创建搜索结果
        query = SearchQuery(
            query="test query",
            timestamp=datetime.now(),
            search_type="reddit"
        )
        search_result = SearchResult(query=query, success=True)
        
        # 创建只有评论没有帖子的Reddit数据
        reddit_data = {
            "reddit_posts": [],  # 空的帖子列表
            "reddit_comments": [
                {
                    "id": "comment1",
                    "post_id": "missing_post_1",  # 引用不存在的帖子
                    "body": "Test comment 1",
                    "author": "user1",
                    "score": 10,
                    "created_utc": 1234567890,
                    "subreddit": "test",
                    "permalink": "/r/test/comments/comment1",
                    "relevance_score": 0.9
                },
                {
                    "id": "comment2",
                    "post_id": "missing_post_2",  # 引用另一个不存在的帖子
                    "body": "Test comment 2",
                    "author": "user2",
                    "score": 5,
                    "created_utc": 1234567891,
                    "subreddit": "test",
                    "permalink": "/r/test/comments/comment2",
                    "relevance_score": 0.8
                }
            ]
        }
        
        # 模拟数据库会话
        mock_session = MagicMock()
        mock_database_service.SessionLocal.return_value = mock_session
        mock_session.__enter__.return_value = mock_session
        
        # 记录所有添加的对象
        added_objects = []
        def track_add(obj):
            added_objects.append(obj)
        mock_session.add.side_effect = track_add
        
        # 执行保存
        result = mock_database_service.save_search_session(
            session_id="test_session_fk",
            search_result=search_result,
            reddit_data=reddit_data
        )
        
        assert result is True
        
        # 验证创建了最小帖子记录
        from models.database_models import RedditPost, RedditComment
        
        # 找出所有添加的帖子和评论
        added_posts = [obj for obj in added_objects if hasattr(obj, '__class__') and obj.__class__.__name__ == 'RedditPost']
        added_comments = [obj for obj in added_objects if hasattr(obj, '__class__') and obj.__class__.__name__ == 'RedditComment']
        
        # 应该创建了2个最小帖子记录
        assert len(added_posts) == 2
        assert len(added_comments) == 2
        
        # 验证最小帖子的内容
        post_ids = {post.id for post in added_posts}
        assert "missing_post_1" in post_ids
        assert "missing_post_2" in post_ids
        
        for post in added_posts:
            assert post.title == '[Post data not available]'
            assert post.author == '[deleted]'
            assert post.score == 0
        
        # 验证调用了flush()以确保帖子先被保存
        assert mock_session.flush.call_count >= 1


if __name__ == "__main__":
    pytest.main([__file__, "-v"])