

/* Tailwind CSS的基础样式导入 */
/* @tailwind base: 导入Tailwind的基础样式，重置浏览器默认样式 */
@tailwind base;
/* @tailwind components: 导入Tailwind的组件样式 */
@tailwind components;
/* @tailwind utilities: 导入Tailwind的工具类样式 */
@tailwind utilities;

/* 自定义基础样式层 */
@layer base {
  /* 为html和body元素设置字体 */
  html, body {
    /* 使用系统字体 */
    font-family: system-ui, -apple-system, sans-serif;
    overflow-x: hidden;
    /* 防止iOS Safari的弹性滚动溢出 */
    position: relative;
  }

  /* 动态视口高度支持 */
  :root {
    --vh: 1vh;
  }

  /* 避免图片在小屏溢出 */
  img, video, canvas, svg {
    max-width: 100%;
    height: auto;
  }

  /* 改善移动端触摸目标大小 */
  button, a {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  /* 防止移动端横向滚动 */
  * {
    max-width: 100vw;
  }

  /* 改善移动端输入框体验 */
  input, textarea, select {
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 移动端安全区域适配 */
  @supports (padding-top: env(safe-area-inset-top)) {
    .safe-top {
      padding-top: env(safe-area-inset-top);
    }
    .safe-bottom {
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
}

/* 自定义工具类 */
@layer utilities {
  /* 文本截断样式 */
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 长单词/URL自动断行，避免溢出 */
  .break-words {
    overflow-wrap: break-word;
    word-break: break-word;
  }

  /* 移动端优化的滚动 */
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 防止文本选择（用于按钮等） */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* 优化移动端卡片阴影 */
  @media (max-width: 640px) {
    .mobile-card-shadow {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  /* 移动端优化的点击区域 */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}
