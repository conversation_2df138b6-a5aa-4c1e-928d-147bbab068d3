[pytest]
testpaths = tests
addopts =
    -ra
    --disable-warnings
    --maxfail=3
    --tb=short
    --strict-markers
    --strict-config
    -v
asyncio_mode = auto

markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    e2e: End-to-end tests for complete workflows
    api: API endpoint tests
    database: Tests requiring database connection
    slow: Tests that take longer than 5 seconds
    network: Tests requiring network access
    auth: Authentication and authorization tests
    billing: Payment and points system tests
    search: Search functionality tests
    reddit: Reddit API integration tests
    llm: LLM service tests
    mock: Tests using mocked services
    asyncio: Async tests using event loop
filterwarnings =
    ignore::DeprecationWarning
    ignore::UserWarning
    ignore::pytest.PytestUnraisableExceptionWarning
    ignore::ResourceWarning
