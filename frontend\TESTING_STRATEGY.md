# Testing Strategy

## 测试类型

### 1. 单元测试 (Unit Tests)
- **命令**: `npm run test`
- **框架**: Vitest
- **范围**: 测试单个组件和函数的逻辑
- **特点**: 快速、隔离、不依赖外部服务

### 2. 模拟端到端测试 (Mocked E2E Tests)
- **命令**: `npm run test:e2e`
- **框架**: Playwright
- **特点**: 
  - 使用 `page.route()` 拦截API调用
  - 返回模拟数据
  - 不依赖后端服务
  - 适合CI/CD环境

### 3. 真实端到端测试 (Real E2E Tests)
- **命令**: `npm run test:e2e:real`
- **框架**: Playwright
- **特点**:
  - 真正调用后端API
  - 需要后端服务运行在端口5000
  - 适合本地开发和集成测试

## 为什么之前的测试"没有反应"

之前的端到端测试使用了网络拦截（`page.route()`），这意味着：

1. **API调用被拦截**: 所有对 `/api/*` 的请求都被拦截
2. **返回模拟数据**: 测试返回预定义的JSON响应
3. **后端无感知**: 后端服务器不会收到任何请求
4. **这是设计上的隔离**: 避免测试依赖外部服务

## 运行真实端到端测试

### 前置条件
1. 后端API服务器运行在 `http://127.0.0.1:5000`
2. 前端开发服务器运行在 `http://127.0.0.1:5173`

### 启动服务
```bash
# 终端1: 启动后端API
cd ../
python api_server.py

# 终端2: 启动前端开发服务器
npm run dev
```

### 运行测试
```bash
# 运行真实端到端测试
npm run test:e2e:real

# 运行所有测试
npm run test:all
```

## 测试文件说明

- `smoke.spec.ts` - 基础冒烟测试（模拟）
- `happy_path.spec.ts` - 完整流程测试（模拟）
- `real-e2e.spec.ts` - 真实后端集成测试
- `paypal_integration.spec.ts` - PayPal集成测试（模拟）
- `commenter_card_history.spec.ts` - 评论者历史测试（模拟）
- `data_structure.spec.ts` - 数据结构测试（模拟）

## 最佳实践

1. **开发阶段**: 使用模拟测试快速验证前端逻辑
2. **集成测试**: 使用真实端到端测试验证前后端协作
3. **CI/CD**: 使用模拟测试确保构建稳定性
4. **本地调试**: 使用真实端到端测试排查集成问题
