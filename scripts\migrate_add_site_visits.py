#!/usr/bin/env python3
"""
Add SiteVisit table and indexes (idempotent) and validate migration.

Usage:
  python scripts/migrate_add_site_visits.py
"""

import sys
from pathlib import Path

ROOT = Path(__file__).resolve().parents[1]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

from utils.logger_utils import get_logger


def main():
    logger = get_logger(__name__)
    try:
        from services.database_service import DatabaseService
        from services import storage_service as ss
        from sqlalchemy import text
        from models.database_models import Base, SiteVisit
    except Exception as e:
        print(f"Import failure: {e}")
        sys.exit(1)

    db = None
    try:
        logger.info("Connecting database...")
        db = DatabaseService()

        # Create tables via SQLAlchemy metadata (idempotent)
        logger.info("Ensuring tables exist via metadata.create_all ...")
        Base.metadata.create_all(bind=db.engine)

        # Ensure helpful indexes exist (IF NOT EXISTS for Postgres)
        try:
            with db.engine.connect() as conn:
                conn.execute(ss.text("CREATE INDEX IF NOT EXISTS ix_site_visits_cookie_created ON site_visits (anonymous_cookie_id, created_at);"))
                conn.execute(ss.text("CREATE INDEX IF NOT EXISTS ix_site_visits_created ON site_visits (created_at);"))
        except Exception as e:
            logger.warning(f"Index creation skipped: {e}")

        # Validate presence
        try:
            with db.engine.connect() as conn:
                res = conn.execute(text("SELECT COUNT(*) FROM site_visits;"))
                count = int(list(res)[0][0])
                logger.info(f"Migration OK: site_visits present (rows={count}).")
        except Exception as e:
            logger.error(f"Validation failed: {e}")
            sys.exit(2)

        logger.info("Done.")
    finally:
        try:
            if db:
                db.cleanup()
        except Exception:
            pass


if __name__ == "__main__":
    main()


