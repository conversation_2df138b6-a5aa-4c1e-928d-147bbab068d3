"""
CogBridges Search - Grok Reddit搜索服务
使用X.AI Grok API进行Reddit搜索和数据获取，支持结构化输出
"""

import os
import time
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


from config import config
from utils.logger_utils import get_logger


class RedditComment(BaseModel):
    """Reddit评论结构"""
    author: str = Field(description="评论作者用户名")
    author_url: str = Field(description="评论作者主页URL")
    body: str = Field(description="评论完整内容")
    score: int = Field(description="评论得分", default=0)
    created_utc: Optional[str] = Field(description="评论创建时间", default=None)
    permalink: Optional[str] = Field(description="评论永久链接", default=None)


class RedditPost(BaseModel):
    """Reddit帖子结构"""
    title: str = Field(description="帖子标题")
    selftext: str = Field(description="帖子正文内容")
    url: str = Field(description="帖子URL")
    permalink: Optional[str] = Field(description="帖子永久链接路径", default=None)
    author: str = Field(description="帖子作者用户名")
    subreddit: str = Field(description="所属子版块")
    score: int = Field(description="帖子得分", default=0)
    created_utc: Optional[str] = Field(description="帖子创建时间", default=None)
    num_comments: int = Field(description="评论数量", default=0)


class RedditSearchResult(BaseModel):
    """Reddit搜索结果结构"""
    posts: List[RedditPost] = Field(description="相关帖子列表")
    comments: List[RedditComment] = Field(description="相关顶级评论列表")
    total_posts_found: int = Field(description="找到的帖子总数", default=0)
    total_comments_found: int = Field(description="找到的评论总数", default=0)
    search_query: str = Field(description="搜索查询")
    search_time: float = Field(description="搜索耗时（秒）", default=0.0)


class GrokRedditService:
    """Grok Reddit搜索服务"""
    
    def __init__(self):
        """初始化Grok Reddit服务"""
        self.logger = get_logger(__name__)
        
        # 检查API密钥
        self.api_key = config.XAI_API_KEY
        if not self.api_key:
            self.logger.error("未找到XAI_API_KEY环境变量")
            self.configured = False
        else:
            self.configured = True
            self.logger.info("Grok Reddit服务初始化成功")
        
        # 获取模型配置
        self.model = config.GROK_MODEL
        self.logger.info(f"使用Grok模型: {self.model}")
        
        # API配置
        self.api_url = "https://api.x.ai/v1/chat/completions"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        # 统计信息
        self.request_count = 0
        self.total_request_time = 0.0
    
    async def search_reddit(self, query: str, max_comments: int = None, search_only: bool = False, cancel_event=None) -> Dict[str, Any]:
        """
        使用Grok API搜索Reddit内容
        
        Args:
            query: 搜索查询
            max_comments: 最大评论数量，如果为None则使用配置中的默认值
            
        Returns:
            搜索结果字典
        """
        # 使用配置中的默认值
        if max_comments is None:
            max_comments = config.GROK_MAX_COMMENTS
        if not self.configured:
            return {
                "success": False,
                "error": "Grok API not configured",
                "results": None,
                "search_time": 0.0,
                "raw_response": None
            }
        
        self.logger.info(f"开始 Grok 搜索(仅搜索={bool(search_only)}): {query}")
        start_time = time.time()
        
        try:
            # 取消检查（尽早返回）
            if cancel_event is not None and getattr(cancel_event, 'is_set', lambda: False)():
                return {"success": False, "error": "cancelled", "results": None, "search_time": 0.0, "raw_response": None, "citations": []}

            # 构建搜索提示词
            search_prompt = self._build_search_prompt(query, max_comments)

            # 构建API请求 - 使用requests直接调用以支持搜索引用
            import requests
            import json

            # 构建完整的提示词
            if search_only:
                # 仅执行搜索，强制使用英文检索关键词
                full_prompt = (
                    f"请在Reddit上搜索与\"{query}\"相关的讨论，启用Web搜索并返回搜索引用（citations）。\n"
                    "重要：无论用户输入什么语言，都请先将查询转为地道的英文关键词，再以英文进行检索。\n"
                    "Important: Always translate the user query into idiomatic English and perform the web search in English only.\n"
                    "只需在消息内容中返回一个极小的JSON，例如：{\"search_query\": \"<ENGLISH_QUERY>\"}。\n"
                    "不要在消息中返回帖子或评论的任何内容，我们将从引用链接中抓取真实数据。"
                )
            else:
                full_prompt = search_prompt + f"""

请严格按照以下JSON格式返回结果：

{{
  "search_query": "{query}",
  "posts": [
    {{
      "title": "帖子标题",
      "selftext": "帖子正文内容",
      "url": "完整的Reddit URL",
      "permalink": "永久链接路径",
      "author": "作者用户名",
      "subreddit": "子版块名称",
      "score": 评分数字,
      "num_comments": 评论数字,
      "created_utc": "2024-XX-XX"
    }}
  ],
  "comments": [
    {{
      "author": "评论作者",
      "author_url": "作者主页URL",
      "body": "完整评论内容",
      "score": 评分数字,
      "created_utc": "时间字符串",
      "permalink": "评论永久链接"
    }}
  ],
  "total_posts_found": 帖子总数,
  "total_comments_found": 评论总数
}}

重要：只返回有效的JSON，不要添加任何解释文字。"""

            # 使用requests直接调用API以支持search_parameters
            payload = {
                "messages": [
                    {
                        "role": "system", 
                        "content": "你是一个专业的Reddit搜索助手。请根据用户的查询返回相关的Reddit帖子和评论数据。必须严格返回有效的JSON格式。所有搜索必须使用英文关键词，即便用户输入不是英文。"
                    },
                    {
                        "role": "user",
                        "content": full_prompt
                    }
                ],
                "search_parameters": {
                    "mode": "on",
                    "return_citations": True,  # 启用搜索引用返回
                    "max_search_results": 29,
                    "sources": [{ "type": "web", "allowed_websites": ["reddit.com"] }]
                },
                "model": self.model,
                "max_tokens": 600 if search_only else 8000,  # 搜索模式降低token
                "temperature": 0.3
            }

            # 直接使用 Session 进行请求（不使用任何网络代理配置）
            session = requests.Session()
            if cancel_event is not None and cancel_event.is_set():
                return {"success": False, "error": "cancelled", "results": None, "search_time": 0.0, "raw_response": None, "citations": []}

            response = session.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                timeout=120,
                # 不传递 proxies 参数，避免走代理
            )
            
            if response.status_code != 200:
                raise Exception(f"API请求失败: {response.status_code} - {response.text}")
            
            # 解析响应
            response_data = response.json()
            
            # 创建一个模拟的completion对象以保持兼容性
            class MockCompletion:
                def __init__(self, data):
                    self.choices = [type('Choice', (), {
                        'finish_reason': data['choices'][0]['finish_reason'],
                        'message': type('Message', (), {
                            'content': data['choices'][0]['message']['content']
                        })()
                    })()]
                    self.citations = data.get('citations', [])
                    self._raw_data = data
                
                def model_dump(self):
                    return self._raw_data
            
            completion = MockCompletion(response_data)
            
            # 获取原始API响应
            raw_response = completion.model_dump() if hasattr(completion, 'model_dump') else str(completion)
            
            # 提取搜索引用并去重（按帖子ID归一化）
            citations = self._extract_citations(completion)
            citations = self._dedupe_citations(citations)
            if citations:
                self.logger.info(f"获取到 {len(citations)} 个搜索引用")
                for i, citation in enumerate(citations, 1):
                    if isinstance(citation, dict):
                        self.logger.info(f"  引用 {i}: {citation.get('url', 'N/A')} - {citation.get('title', 'N/A')}")
                    else:
                        self.logger.info(f"  引用 {i}: {citation}")
            else:
                self.logger.info("未获取到搜索引用")
            
            # 解析JSON响应（search_only 模式下跳过结构化解析，仅依赖 citations）
            json_content = completion.choices[0].message.content
            parsed_result = None

            if not search_only and json_content:
                try:
                    import json

                    # 检查是否因token限制被截断
                    if completion.choices[0].finish_reason == 'length':
                        self.logger.warning("响应因token限制被截断，尝试修复JSON")
                        # 尝试修复截断的JSON
                        json_content = self._fix_truncated_json(json_content)

                    json_data = json.loads(json_content)
                    # 直接使用JSON数据创建RedditSearchResult对象
                    parsed_result = RedditSearchResult.model_validate(json_data)
                    self.logger.info("结构化输出解析成功")
                except (json.JSONDecodeError, Exception) as e:
                    self.logger.error(f"JSON解析失败: {e}")
                    self.logger.error(f"finish_reason: {completion.choices[0].finish_reason}")
                    self.logger.error(f"响应长度: {len(json_content) if json_content else 0}")
                    # 只显示前500个字符，避免日志过长
                    preview = json_content[:500] + "..." if len(json_content) > 500 else json_content
                    self.logger.error(f"响应内容预览: {preview}")
                    parsed_result = None
            
            # 如果解析失败，仅在非 search_only 模式记录错误
            if not parsed_result and not search_only:
                self.logger.error("结构化输出解析失败，无法获取Reddit数据")

            search_time = time.time() - start_time
            self.request_count += 1
            self.total_request_time += search_time

            # 始终保存原始API响应，无论成功还是失败
            self._save_raw_response(query, raw_response, search_time, citations)
            
            if parsed_result:
                self.logger.info(f"Grok Reddit搜索成功，耗时: {search_time:.2f}秒")
                self.logger.info(f"找到 {len(parsed_result.posts)} 个帖子，{len(parsed_result.comments)} 条评论")
 
                # 将帖子url与搜索引用中的准确url对齐
                try:
                    self._align_post_urls_with_citations(parsed_result, citations)
                except Exception as align_error:
                    self.logger.warning(f"对齐帖子URL与引用失败: {align_error}")

                return {
                    "success": True,
                    "results": parsed_result,
                    "search_time": search_time,
                    "error": None,
                    "raw_response": raw_response,
                    "grok_raw_response": raw_response,
                    "citations": citations  # 添加搜索引用
                }
            elif search_only:
                # 搜索模式：不要求结构化结果，只需要 citations
                self.logger.info(f"Grok Reddit搜索（仅搜索）成功，耗时: {search_time:.2f}秒，引用数: {len(citations) if citations else 0}")
                return {
                    "success": True,
                    "results": None,
                    "search_time": search_time,
                    "error": None,
                    "raw_response": raw_response,
                    "grok_raw_response": raw_response,
                    "citations": citations
                }
            else:
                error_msg = "Grok API返回空结果"
                self.logger.error(error_msg)
                self.logger.error(f"原始响应类型: {type(raw_response)}")
                self.logger.error(f"completion对象: {completion}")
                return {
                    "success": False,
                    "error": error_msg,
                    "results": None,
                    "search_time": search_time,
                    "raw_response": raw_response,
                    "grok_raw_response": raw_response,
                    "citations": citations  # 即使失败也包含搜索引用
                }
                
        except Exception as timeout_error:
            search_time = time.time() - start_time
            err_str = str(timeout_error)
            if "timeout" in err_str.lower():
                error_msg = "Grok API request timeout"
            else:
                # Normalize to English and include keyword for tests
                error_msg = f"Grok API call error: {err_str}"
            self.logger.error(error_msg)
            self.logger.error(f"异常详情: {timeout_error}", exc_info=True)
            
            # 尝试保存错误信息
            try:
                error_response = {
                    "error": error_msg,
                    "exception": str(timeout_error),
                    "exception_type": type(timeout_error).__name__
                }
                self._save_raw_response(query, error_response, search_time, [])
            except Exception as save_error:
                self.logger.error(f"保存错误响应失败: {save_error}")
            
            return {
                "success": False,
                "error": error_msg,
                "results": None,
                "search_time": search_time,
                "raw_response": None
            }

    
    def _extract_citations(self, completion) -> List[Dict[str, Any]]:
        """提取搜索引用"""
        try:
            # 检查completion对象是否包含citations字段
            if hasattr(completion, 'citations') and completion.citations:
                citations = completion.citations
                self.logger.debug(f"从completion.citations获取: {type(citations)}")
                # 确保是列表格式
                if isinstance(citations, list):
                    return citations
                else:
                    self.logger.warning(f"citations不是列表格式: {type(citations)}")
                    return []
            
            # 检查model_dump中是否包含citations
            raw_data = completion.model_dump() if hasattr(completion, 'model_dump') else {}
            if 'citations' in raw_data and raw_data['citations']:
                citations = raw_data['citations']
                self.logger.debug(f"从raw_data获取citations: {type(citations)}")
                # 确保是列表格式
                if isinstance(citations, list):
                    return citations
                else:
                    self.logger.warning(f"raw_data中citations不是列表格式: {type(citations)}")
                    return []
            
            # 检查其他可能的位置
            if hasattr(completion, 'choices') and completion.choices:
                choice = completion.choices[0]
                if hasattr(choice, 'citations') and choice.citations:
                    return choice.citations
            
            self.logger.debug("未在任何位置找到citations")
            return []
            
        except Exception as e:
            self.logger.warning(f"提取搜索引用失败: {e}")
            import traceback
            self.logger.debug(f"详细错误: {traceback.format_exc()}")
            return []
    
    def _dedupe_citations(self, citations: List[Any]) -> List[Dict[str, Any]]:
        """按帖子ID归一化去重 citations。
        - 接受 dict 或 str；输出统一为 dict，至少包含 url 字段
        - 仅保留 reddit.com 且包含 /comments/{post_id}
        - 忽略语言/utm等 query 与 fragment
        """
        if not citations:
            return []
        from urllib.parse import urlparse, urlunparse
        import re
        post_pattern = re.compile(r"/r/([^/]+)/comments/([^/]+)", re.IGNORECASE)
        seen = set()  # canonical path key: /r/{sub}/comments/{post_id}
        deduped: List[Dict[str, Any]] = []
        for c in citations:
            try:
                if isinstance(c, dict):
                    url = c.get("url") or c.get("uri")
                    title = c.get("title")
                elif isinstance(c, str):
                    url = c
                    title = None
                else:
                    continue
                if not isinstance(url, str) or not url.strip():
                    continue
                parsed = urlparse(url)
                host = (parsed.netloc or '').lower()
                if 'reddit.com' not in host:
                    continue
                path_lower = (parsed.path or '').strip().lower()
                if len(path_lower) > 1 and path_lower.endswith('/'):
                    path_lower = path_lower[:-1]
                m = post_pattern.search(path_lower)
                if not m:
                    continue
                subreddit = m.group(1)
                post_id = m.group(2)
                canonical_path = f"/r/{subreddit}/comments/{post_id}"
                if canonical_path in seen:
                    continue
                seen.add(canonical_path)
                clean_url = urlunparse(('https','reddit.com',canonical_path,'','',''))
                deduped.append({"url": clean_url, "title": title} if title else {"url": clean_url})
            except Exception:
                continue
        return deduped

    def _fix_truncated_json(self, json_content: str) -> str:
        """修复因token限制而截断的JSON"""
        try:
            # 找到最后一个完整的评论或帖子
            last_complete_brace = json_content.rfind('}')
            if last_complete_brace > 0:
                # 截取到最后一个完整的对象
                truncated_at_object = json_content[:last_complete_brace + 1]

                # 检查是否在comments数组中
                if '"comments":' in truncated_at_object:
                    # 确保comments数组正确闭合
                    if not truncated_at_object.rstrip().endswith(']}'):
                        truncated_at_object = truncated_at_object.rstrip().rstrip(',') + ']'

                # 确保整个JSON对象正确闭合
                if not truncated_at_object.rstrip().endswith('}'):
                    truncated_at_object += '}'

                # 添加缺失的字段
                if '"total_posts_found"' not in truncated_at_object:
                    truncated_at_object = truncated_at_object.rstrip('}') + ',"total_posts_found":5,"total_comments_found":25}'

                self.logger.info(f"修复截断JSON成功，原长度: {len(json_content)}, 修复后长度: {len(truncated_at_object)}")
                return truncated_at_object

        except Exception as e:
            self.logger.warning(f"修复截断JSON失败: {e}")

        return json_content

    def _align_post_urls_with_citations(self, parsed_result: RedditSearchResult, citations: List[Dict[str, Any]]):
        """将帖子url与准确的引用url对齐
        - 优先根据permalink与citation的path精确匹配
        - 次选根据post.url的path匹配
        - 兜底根据title精确或包含匹配
        仅使用reddit域名的citation
        """
        if not parsed_result or not citations:
            return

        from urllib.parse import urlparse

        def normalize_path(path: str) -> str:
            if not path:
                return ""
            # 去除尾部斜杠，统一小写
            p = path.strip()
            if len(p) > 1 and p.endswith('/'):
                p = p[:-1]
            return p.lower()

        # 仅保留reddit域名的引用，并建立多种索引
        reddit_citations: List[Dict[str, Any]] = []
        citation_path_map: Dict[str, Dict[str, Any]] = {}
        citation_title_map: Dict[str, Dict[str, Any]] = {}

        for c in citations:
            try:
                if isinstance(c, dict):
                    url = c.get('url') or c.get('uri')
                    title = c.get('title')
                elif isinstance(c, str):
                    url = c
                    title = None
                else:
                    url = None
                    title = None
                if not url:
                    continue
                parsed = urlparse(url)
                host = (parsed.netloc or '').lower()
                if 'reddit.com' not in host:
                    continue
                reddit_citations.append(c)
                path_key = normalize_path(parsed.path)
                if path_key:
                    citation_path_map[path_key] = c if isinstance(c, dict) else {"url": url, "title": title}
                if isinstance(title, str) and title.strip():
                    citation_title_map[title.strip().lower()] = c
            except Exception:
                continue

        if not reddit_citations:
            return

        # 对每个post进行对齐
        for post in parsed_result.posts:
            matched_citation = None

            # 1) 尝试用permalink的path匹配
            if getattr(post, 'permalink', None):
                permalink_path = normalize_path(post.permalink)
                if permalink_path in citation_path_map:
                    matched_citation = citation_path_map[permalink_path]

            # 2) 尝试用post.url的path匹配
            if not matched_citation and getattr(post, 'url', None):
                try:
                    path = urlparse(post.url).path
                    path_key = normalize_path(path)
                    if path_key in citation_path_map:
                        matched_citation = citation_path_map[path_key]
                except Exception:
                    pass

            # 3) 尝试用title匹配（精确或包含）
            if not matched_citation and getattr(post, 'title', None):
                title_key = post.title.strip().lower()
                if title_key in citation_title_map:
                    matched_citation = citation_title_map[title_key]
                else:
                    # 包含匹配：找第一个citation标题包含帖子标题或相反
                    for ct_title, ct in citation_title_map.items():
                        if title_key and (title_key in ct_title or ct_title in title_key):
                            matched_citation = ct
                            break

            if matched_citation:
                citation_url = None
                if isinstance(matched_citation, dict):
                    citation_url = matched_citation.get('url') or matched_citation.get('uri')
                elif isinstance(matched_citation, str):
                    citation_url = matched_citation
                if isinstance(citation_url, str) and citation_url.strip():
                    post.url = citation_url.strip()
                    # 若permalink缺失，尝试从citation补足
                    try:
                        parsed = urlparse(citation_url)
                        if (not getattr(post, 'permalink', None)) and parsed.path:
                            # 确保以'/'开头
                            post.permalink = parsed.path if parsed.path.startswith('/') else '/' + parsed.path
                    except Exception:
                        pass

    def _build_search_prompt(self, query: str, max_comments: int) -> str:
        """构建搜索提示词"""
        return f"""请在Reddit上搜索与"{query}"相关的帖子和评论，并返回实时数据。

搜索要求：
1. 搜索最相关的Reddit帖子（优先最近1年内的帖子）
2. 获取总共{max_comments}条最相关的顶级评论
3. 每个帖子需要包含：
   - title: 帖子标题
   - body: 帖子正文内容（如果有）
   - url: 完整的Reddit URL
   - permalink: 永久链接路径（格式：/r/subreddit/comments/post_id/title/）
   - author: 作者用户名
   - subreddit: 子版块名称
   - score: 帖子得分
   - num_comments: 评论数量

4. 每条评论需要包含：
   - author: 评论作者用户名
   - author_url: 作者主页URL（格式：https://reddit.com/user/[用户名]）
   - body: 完整的评论内容
   - score: 评论得分
   - created_utc: 评论创建时间
   - permalink: 评论永久链接

格式要求：
- 返回真实的、未删除的帖子和评论
- 评论内容必须完整，不能截断
- 确保数据结构完整且符合JSON格式
- 优先选择有实质内容的评论
- 评论应来自不同的帖子（避免所有评论来自同一个帖子）
- 确保至少返回3-5个不同的帖子"""

    def get_statistics(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        return {
            "api_configured": self.configured,
            "service_type": "grok_reddit_service",
            "model": self.model,
            "max_comments": config.GROK_MAX_COMMENTS,
            "request_count": self.request_count,
            "total_request_time": self.total_request_time,
            "average_request_time": self.total_request_time / max(self.request_count, 1)
        }

    async def close(self):
        """关闭服务"""
        self.logger.info("Grok Reddit服务已关闭")

    def _save_raw_response(self, query: str, raw_response: Any, search_time: float, citations: List[Dict[str, Any]] = None):
        """已移除文件保存，避免非数据库持久化。保留方法以兼容调用。"""
        try:
            self.logger.debug("Skip saving Grok raw response (DB-only policy)")
        except Exception:
            pass
