"""
匿名用户搜索限制服务（数据库存储）
使用 Cookie + IP 双重策略限制未注册用户的搜索次数，计数持久化在数据库。
"""

import uuid
import time
from typing import Dict, Optional, Tuple
from flask import request
from utils.logger_utils import get_logger
from config import config as cfg
from services.database_service import DatabaseService

logger = get_logger(__name__)


class AnonymousLimitService:
    """匿名用户搜索限制服务"""
    
    def __init__(self):
        # 数据库存储服务
        self.db = DatabaseService()
    
    # 文件持久化逻辑已移除，统一改为数据库存储
    
    def _get_client_ip(self) -> str:
        """获取客户端真实IP地址"""
        # 优先从代理头获取真实IP
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            # X-Forwarded-For 可能包含多个IP，取第一个
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip.strip()
        
        # 回退到直接连接IP
        return request.remote_addr or 'unknown'
    
    def _get_anonymous_cookie_id(self) -> Optional[str]:
        """从请求中获取匿名用户cookie ID"""
        return request.cookies.get(cfg.ANONYMOUS_COOKIE_NAME)
    
    def _generate_anonymous_cookie_id(self) -> str:
        """生成新的匿名用户cookie ID"""
        return f"anon_{uuid.uuid4().hex[:16]}_{int(time.time())}"
    
    def check_anonymous_limit(self) -> Tuple[bool, Optional[str], Dict]:
        """
        检查匿名用户是否已达到搜索限制
        
        Returns:
            Tuple[bool, Optional[str], Dict]:
            - bool: 是否允许搜索 (True=允许, False=已达限制)
            - Optional[str]: 如果是新用户，返回新生成的cookie ID
            - Dict: 额外信息 (用于调试和日志)
        """
        client_ip = self._get_client_ip()
        cookie_id = self._get_anonymous_cookie_id()
        current_time = time.time()
        
        info = {
            "client_ip": client_ip,
            "cookie_id": cookie_id,
            "timestamp": current_time
        }
        
        # 从数据库读取计数
        ip_count = 0
        cookie_count = 0
        try:
            rec_cookie = self.db.get_anonymous_usage(cookie_id=cookie_id, ip_address=None) if cookie_id else None
            rec_ip = self.db.get_anonymous_usage(cookie_id=None, ip_address=client_ip) if client_ip else None
            rec_both = self.db.get_anonymous_usage(cookie_id=cookie_id, ip_address=client_ip) if cookie_id and client_ip else None
            if rec_ip:
                ip_count = int(getattr(rec_ip, 'usage_count', 0) or 0)
            if rec_cookie:
                cookie_count = int(getattr(rec_cookie, 'usage_count', 0) or 0)
            if rec_both:
                cookie_count = max(cookie_count, int(getattr(rec_both, 'usage_count', 0) or 0))
                ip_count = max(ip_count, int(getattr(rec_both, 'usage_count', 0) or 0))
        except Exception as e:
            logger.warning(f"Failed to query anonymous usage counts from DB: {e}")
        
        info.update({
            "ip_count": ip_count,
            "cookie_count": cookie_count,
            "limit": cfg.ANONYMOUS_TRIAL_LIMIT
        })
        
        # 如果IP或Cookie任一已达限制，则拒绝
        if ip_count >= cfg.ANONYMOUS_TRIAL_LIMIT or cookie_count >= cfg.ANONYMOUS_TRIAL_LIMIT:
            logger.info(f"Anonymous search limit reached - IP: {client_ip} (count: {ip_count}), Cookie: {cookie_id} (count: {cookie_count})")
            return False, None, info
        
        # 如果是新用户（没有cookie），生成新的cookie ID
        new_cookie_id = None
        if not cookie_id:
            new_cookie_id = self._generate_anonymous_cookie_id()
            info["new_cookie_id"] = new_cookie_id
        
        logger.info(f"Anonymous search allowed - IP: {client_ip} (count: {ip_count}), Cookie: {cookie_id or 'new'} (count: {cookie_count})")
        return True, new_cookie_id, info
    
    def record_anonymous_search(self, cookie_id: Optional[str] = None, client_ip: Optional[str] = None):
        """
        记录匿名用户搜索

        Args:
            cookie_id: 如果提供，使用此cookie ID；否则从请求中获取
            client_ip: 如果提供，使用此IP；否则从请求中获取
        """
        # 尝试获取客户端IP，如果没有请求上下文则使用传入的IP
        if client_ip:
            actual_ip = client_ip
        else:
            try:
                actual_ip = self._get_client_ip()
            except RuntimeError:
                # 没有请求上下文时，使用传入的IP或跳过IP记录
                actual_ip = 'unknown'
                logger.warning("No request context available for IP detection in record_anonymous_search")

        # 尝试获取cookie ID，如果没有请求上下文则使用传入的cookie_id
        if not cookie_id:
            try:
                cookie_id = self._get_anonymous_cookie_id()
            except RuntimeError:
                # 没有请求上下文时，无法获取cookie
                logger.warning("No request context available for cookie detection in record_anonymous_search")

        current_time = time.time()

        # 直接写入数据库计数
        try:
            if (cookie_id or actual_ip) and (actual_ip != 'unknown'):
                self.db.increment_anonymous_usage(cookie_id=cookie_id, ip_address=actual_ip)
            elif cookie_id:
                self.db.increment_anonymous_usage(cookie_id=cookie_id, ip_address=None)
            elif actual_ip and actual_ip != 'unknown':
                self.db.increment_anonymous_usage(cookie_id=None, ip_address=actual_ip)
        except Exception as e:
            logger.warning(f"Failed to increment anonymous usage in DB: {e}")

        logger.info(f"Recorded anonymous search - IP: {actual_ip}, Cookie: {cookie_id}")
    
    def cleanup_old_records(self, max_age_days: int = 30):
        """
        保留方法以兼容旧调用；数据库层清理应通过定期作业完成。
        """


# 全局实例
anonymous_limit_service = AnonymousLimitService()
