"""
CogBridges Search - 数据库模型
使用SQLAlchemy定义数据库表结构，对应现有的JSON数据结构
"""

from sqlalchemy import Column, String, Integer, Float, Text, DateTime, Boolean, JSON, ForeignKey, Index, PrimaryKeyConstraint
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime, timezone
from typing import Dict, Any, Optional

Base = declarative_base()


class SearchSession(Base):
    """搜索会话表 - 对应完整会话数据"""
    __tablename__ = 'search_sessions'
    __table_args__ = (
        # 历史查询：按用户筛选并按创建时间倒序
        Index('ix_search_sessions_owner_created', 'owner_user_id', 'created_at'),
        # 匿名用户查询：按匿名标识和时间倒序
        Index('ix_search_sessions_anonymous_created', 'anonymous_cookie_id', 'created_at'),
    )
    
    id = Column(String(50), primary_key=True)  # session_id
    query = Column(Text, nullable=False)  # 搜索查询
    timestamp = Column(DateTime, default=func.now())  # 创建时间
    search_type = Column(String(20), default='reddit')  # 搜索类型
    max_results = Column(Integer, default=5)  # 最大结果数
    site_filter = Column(String(100), default='site:reddit.com')  # 站点过滤
    
    # 所属用户（可为空，历史数据可能无归属）
    owner_user_id = Column(Integer, ForeignKey('user_accounts.id'), nullable=True, index=True)
    
    # 匿名用户标识（新增字段）
    anonymous_cookie_id = Column(String(100), nullable=True, index=True)
    is_anonymous = Column(Boolean, default=False, index=True)
    
    # 搜索结果统计
    reddit_posts_count = Column(Integer, default=0)
    reddit_comments_count = Column(Integer, default=0)
    
    # 状态信息
    success = Column(Boolean, default=True)
    error_message = Column(Text, nullable=True)
    
    # LLM分析结果
    llm_analysis_success = Column(Boolean, default=False)
    llm_analysis_time = Column(Float, default=0.0)
    llm_analysis_error = Column(Text, nullable=True)

    # 会话级度量（用于定位线上问题与看板）
    total_time_ms = Column(Integer, default=0)
    grok_time_ms = Column(Integer, default=0)
    reddit_time_ms = Column(Integer, default=0)
    history_time_ms = Column(Integer, default=0)
    llm_time_ms = Column(Integer, default=0)
    cancelled = Column(Boolean, default=False)
    step_failed = Column(String(50), nullable=True)
    ext_failures = Column(JSON, nullable=True)

    # JSON备份数据
    raw_data = Column(JSON, nullable=True)  # 完整的原始数据

    # 关联关系
    reddit_posts = relationship("RedditPost", back_populates="session", cascade="all, delete-orphan")
    reddit_comments = relationship("RedditComment", back_populates="session", cascade="all, delete-orphan")
    user_histories = relationship("UserHistory", back_populates="session", cascade="all, delete-orphan")
    subreddit_similarities = relationship("SubredditSimilarity", back_populates="session", cascade="all, delete-orphan")
    owner = relationship("UserAccount")

    created_at = Column(DateTime, default=func.now(), index=True)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "query": self.query,
            "timestamp": (self.timestamp.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.timestamp else None),
            "search_type": self.search_type,
            "max_results": self.max_results,
            "site_filter": self.site_filter,
            "owner_user_id": self.owner_user_id,
            "anonymous_cookie_id": self.anonymous_cookie_id,
            "is_anonymous": self.is_anonymous,

            "reddit_posts_count": self.reddit_posts_count,
            "reddit_comments_count": self.reddit_comments_count,
            "success": self.success,
            "error_message": self.error_message,
            "llm_analysis_success": self.llm_analysis_success,
            "llm_analysis_time": self.llm_analysis_time,
            "llm_analysis_error": self.llm_analysis_error,
            "raw_data": self.raw_data,

            # 会话级度量
            "total_time_ms": self.total_time_ms,
            "grok_time_ms": self.grok_time_ms,
            "reddit_time_ms": self.reddit_time_ms,
            "history_time_ms": self.history_time_ms,
            "llm_time_ms": self.llm_time_ms,
            "cancelled": self.cancelled,
            "step_failed": self.step_failed,
            "ext_failures": self.ext_failures,

            "created_at": (self.created_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.created_at else None),
            "updated_at": (self.updated_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.updated_at else None),
        }






class RedditPost(Base):
    """Reddit帖子表"""
    __tablename__ = 'reddit_posts'
    __table_args__ = (
        # 复合主键：Reddit post ID + session_id
        PrimaryKeyConstraint('id', 'session_id'),
        # 常见查询：按会话与子版块/时间聚合或过滤
        Index('ix_reddit_posts_session_sub', 'session_id', 'subreddit'),
    )
    
    id = Column(String(20), nullable=False)  # Reddit post ID
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False, index=True)
    
    title = Column(Text, nullable=False)
    selftext = Column(Text, nullable=True)
    author = Column(String(100), nullable=True)
    score = Column(Integer, default=0)
    num_comments = Column(Integer, default=0)
    created_utc = Column(Float, nullable=False)
    subreddit = Column(String(100), nullable=True)
    permalink = Column(Text, nullable=True)
    url = Column(Text, nullable=True)
    
    # 分析结果（已弃用：不再单独存储到帖子表，统一在完整会话 JSON 中保存）
    
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    session = relationship("SearchSession", back_populates="reddit_posts")
    comments = relationship(
        "RedditComment",
        back_populates="post",
        primaryjoin="and_(RedditPost.session_id==foreign(RedditComment.session_id), RedditPost.id==foreign(RedditComment.post_id))",
        viewonly=True,
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "title": self.title,
            "selftext": self.selftext,
            "author": self.author,
            "score": self.score,
            "num_comments": self.num_comments,
            "created_utc": self.created_utc,
            "subreddit": self.subreddit,
            "permalink": self.permalink,
            "url": self.url,
            "created_at": (self.created_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.created_at else None)
        }


class RedditComment(Base):
    """Reddit评论表"""
    __tablename__ = 'reddit_comments'
    __table_args__ = (
        # 复合主键：Reddit comment ID + session_id
        PrimaryKeyConstraint('id', 'session_id'),
        # 常见查询：按会话读取所有评论，或按帖子ID过滤
        Index('ix_reddit_comments_session_post', 'session_id', 'post_id'),
    )
    
    id = Column(String(20), nullable=False)  # Reddit comment ID
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False, index=True)
    post_id = Column(String(20), nullable=True, index=True)  # 不设置外键约束以兼容复合主键
    
    body = Column(Text, nullable=False)
    author = Column(String(100), nullable=True)
    score = Column(Integer, default=0)
    created_utc = Column(Float, nullable=False)
    parent_id = Column(String(20), nullable=True)
    subreddit = Column(String(100), nullable=True)
    permalink = Column(Text, nullable=True)
    
    # 相关性分数（由LLM内容筛选服务计算）
    relevance_score = Column(Float, nullable=True, default=0.0)
    
    # 分析结果（已弃用：不再单独存储到评论表，统一在完整会话 JSON 中保存）
    
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    session = relationship("SearchSession", back_populates="reddit_comments")
    post = relationship(
        "RedditPost",
        back_populates="comments",
        primaryjoin="and_(foreign(RedditComment.session_id)==RedditPost.session_id, foreign(RedditComment.post_id)==RedditPost.id)",
        viewonly=True,
        uselist=False,
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "post_id": self.post_id,
            "body": self.body,
            "author": self.author,
            "score": self.score,
            "created_utc": self.created_utc,
            "parent_id": self.parent_id,
            "subreddit": self.subreddit,
            "permalink": self.permalink,
            "relevance_score": self.relevance_score,
            "created_at": (self.created_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.created_at else None)
        }


class UserHistory(Base):
    """用户历史数据表"""
    __tablename__ = 'user_histories'
    __table_args__ = (
        # 常见查询：按会话与用户名聚合/过滤
        Index('ix_user_histories_session_username', 'session_id', 'username'),
    )
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False, index=True)
    username = Column(String(100), nullable=False)
    
    # 用户统计信息
    total_comments = Column(Integer, default=0)
    total_posts = Column(Integer, default=0)
    account_created_utc = Column(Float, nullable=True)
    
    # 历史数据（JSON格式存储）
    comments_data = Column(JSON, nullable=True)
    posts_data = Column(JSON, nullable=True)
    
    created_at = Column(DateTime, default=func.now())

    # 关联关系
    session = relationship("SearchSession", back_populates="user_histories")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "username": self.username,
            "total_comments": self.total_comments,
            "total_posts": self.total_posts,
            "account_created_utc": self.account_created_utc,
            "comments_data": self.comments_data,
            "posts_data": self.posts_data,
            "created_at": (self.created_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.created_at else None)
        }


class SubredditSimilarity(Base):
    """子版块相似性分析表 - 存储LLM筛选子版块的结果"""
    __tablename__ = 'subreddit_similarities'

    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False, index=True)
    username = Column(String(100), nullable=False)

    # 目标子版块信息
    target_subreddits = Column(JSON, nullable=False)  # 目标子版块列表
    user_subreddits = Column(JSON, nullable=False)    # 用户关注的子版块列表

    # 相似性分析结果
    similarity_results = Column(JSON, nullable=False)  # 完整的相似性分析结果
    all_similar_subreddits = Column(JSON, nullable=True)  # 所有相似的子版块

    # 分析元数据
    analysis_success = Column(Boolean, default=True)
    analysis_error = Column(Text, nullable=True)
    llm_response_raw = Column(Text, nullable=True)  # LLM原始响应

    created_at = Column(DateTime, default=func.now())

    # 关联关系
    session = relationship("SearchSession", back_populates="subreddit_similarities")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "username": self.username,
            "target_subreddits": self.target_subreddits,
            "user_subreddits": self.user_subreddits,
            "similarity_results": self.similarity_results,
            "all_similar_subreddits": self.all_similar_subreddits,
            "analysis_success": self.analysis_success,
            "analysis_error": self.analysis_error,
            "llm_response_raw": self.llm_response_raw,
            "created_at": (self.created_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.created_at else None)
        }


class CommentMotivationAnalysis(Base):
    """评论动机分析表 - 存储LLM分析用户画像的结果"""
    __tablename__ = 'comment_motivation_analyses'

    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), ForeignKey('search_sessions.id'), nullable=False, index=True)
    username = Column(String(100), nullable=False)

    # 关联的评论信息
    comment_id = Column(String(20), ForeignKey('reddit_comments.id'), nullable=True)
    target_subreddit = Column(String(100), nullable=False)

    # 分析结果
    professional_background = Column(Text, nullable=True)    # 专业背景分析
    participation_motivation = Column(Text, nullable=True)   # 参与动机分析
    interest_areas = Column(Text, nullable=True)             # 兴趣领域分析
    user_profile = Column(Text, nullable=True)               # 用户画像推断
    matching_value = Column(Text, nullable=True)             # 潜在匹配价值
    overall_assessment = Column(Text, nullable=True)         # 总体评价

    # 分析元数据
    analysis_success = Column(Boolean, default=True)
    analysis_error = Column(Text, nullable=True)
    llm_response_raw = Column(Text, nullable=True)  # LLM原始响应

    # 输入数据（用于复现分析）
    target_post_data = Column(JSON, nullable=True)           # 目标帖子数据
    user_comment_data = Column(JSON, nullable=True)          # 用户评论数据
    similar_subreddits_data = Column(JSON, nullable=True)    # 相似子版块数据
    user_overview_data = Column(JSON, nullable=True)         # 用户概览数据

    created_at = Column(DateTime, default=func.now())

    # 关联关系
    session = relationship("SearchSession")
    comment = relationship("RedditComment")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "username": self.username,
            "comment_id": self.comment_id,
            "target_subreddit": self.target_subreddit,
            "professional_background": self.professional_background,
            "participation_motivation": self.participation_motivation,
            "interest_areas": self.interest_areas,
            "user_profile": self.user_profile,
            "matching_value": self.matching_value,
            "overall_assessment": self.overall_assessment,
            "analysis_success": self.analysis_success,
            "analysis_error": self.analysis_error,
            "llm_response_raw": self.llm_response_raw,
            "target_post_data": self.target_post_data,
            "user_comment_data": self.user_comment_data,
            "similar_subreddits_data": self.similar_subreddits_data,
            "user_overview_data": self.user_overview_data,
            "created_at": (self.created_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.created_at else None)
        }


# ------------------------------
# 新增：站点访问记录（用于漏斗：访问 -> 匿名搜索 -> 注册）
# ------------------------------
class SiteVisit(Base):
    """站点访问记录 - 记录进入站点但未必发起搜索的访问。"""
    __tablename__ = 'site_visits'
    __table_args__ = (
        Index('ix_site_visits_cookie_created', 'anonymous_cookie_id', 'created_at'),
        Index('ix_site_visits_created', 'created_at'),
    )

    id = Column(Integer, primary_key=True, autoincrement=True)
    # 匿名 cookie（若有）
    anonymous_cookie_id = Column(String(100), nullable=True, index=True)
    # 隐私：IP 哈希（使用服务端盐）
    ip_hash = Column(String(128), nullable=True)
    # 客户端环境
    user_agent = Column(Text, nullable=True)
    referrer = Column(Text, nullable=True)
    landing_path = Column(Text, nullable=True)
    utm_source = Column(String(100), nullable=True)
    utm_medium = Column(String(100), nullable=True)
    utm_campaign = Column(String(100), nullable=True)

    created_at = Column(DateTime, default=func.now(), index=True)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "anonymous_cookie_id": self.anonymous_cookie_id,
            "ip_hash": self.ip_hash,
            "user_agent": self.user_agent,
            "referrer": self.referrer,
            "landing_path": self.landing_path,
            "utm_source": self.utm_source,
            "utm_medium": self.utm_medium,
            "utm_campaign": self.utm_campaign,
            "created_at": (self.created_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.created_at else None),
        }

# ------------------------------
# 新增：用户账户表（用于邮箱注册/登录）
# ------------------------------
class UserAccount(Base):
    """用户账户表 - 简洁的邮箱/密码登录"""
    __tablename__ = 'user_accounts'

    id = Column(Integer, primary_key=True, autoincrement=True)
    email = Column(String(255), nullable=False, unique=True, index=True)
    password_hash = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True)
    email_verified = Column(Boolean, default=False)
    
    # 点数相关字段
    points_balance = Column(Integer, default=0, nullable=False)
    trial_flags = Column(JSON, default={}, nullable=False)  # {"granted20": bool, "granted40": bool, "trial_last_used_at": timestamp}

    last_login = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def to_safe_dict(self) -> Dict[str, Any]:
        """安全的用户信息字典（不包含敏感字段）"""
        return {
            "id": self.id,
            "email": self.email,
            "is_active": self.is_active,
            "email_verified": self.email_verified,
            "points_balance": self.points_balance,
            "trial_flags": self.trial_flags,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "created_at": (self.created_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.created_at else None),
            "updated_at": (self.updated_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.updated_at else None),
        }


class PointsLedger(Base):
    """点数账本表 - 记录所有点数变动"""
    __tablename__ = 'points_ledger'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('user_accounts.id'), nullable=False, index=True)
    delta = Column(Integer, nullable=False)  # 正数为增加，负数为扣除
    reason = Column(String(50), nullable=False)  # purchase/search/trial/refund
    ref = Column(String(255), nullable=True)  # 关联ID，如stripe_event_id, session_id等
    meta = Column('metadata', JSON, nullable=True)  # 额外元数据（DB列名仍为metadata）
    
    created_at = Column(DateTime, default=func.now())
    
    # 关联关系
    user = relationship("UserAccount")
    __table_args__ = (
        # 快速按用户与时间查询账本明细
        Index('ix_points_ledger_user_created', 'user_id', 'created_at'),
        # 按用户+原因聚合明细（如统计消费、试用、购买）
        Index('ix_points_ledger_user_reason_created', 'user_id', 'reason', 'created_at'),
        # 便于通过业务引用去重/检索（例如 Stripe 事件ID、session_id）
        Index('ix_points_ledger_ref', 'ref'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "delta": self.delta,
            "reason": self.reason,
            "ref": self.ref,
            "metadata": self.meta,
            "created_at": (self.created_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.created_at else None)
        }


# ------------------------------
# 新增：匿名用户使用次数限制
# ------------------------------
class AnonymousUsageLimit(Base):
    """匿名用户使用次数限制（Cookie + IP 组合计数）"""
    __tablename__ = 'anonymous_usage_limits'
    __table_args__ = (
        # 按 cookie 查询与时间倒序
        Index('ix_anonymous_usage_cookie_created', 'cookie_id', 'updated_at'),
        # 按 IP 查询与时间倒序
        Index('ix_anonymous_usage_ip_created', 'ip_address', 'updated_at'),
        # 便于查重
        Index('ix_anonymous_usage_cookie_ip', 'cookie_id', 'ip_address'),
    )

    id = Column(Integer, primary_key=True, autoincrement=True)
    # 可为空（首次请求只有 IP 或只有 Cookie 的情况）
    cookie_id = Column(String(100), nullable=True, index=True)
    ip_address = Column(String(64), nullable=True, index=True)

    usage_count = Column(Integer, default=0, nullable=False)
    last_used_at = Column(DateTime, default=func.now())
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "cookie_id": self.cookie_id,
            "ip_address": self.ip_address,
            "usage_count": self.usage_count,
            "last_used_at": (self.last_used_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.last_used_at else None),
            "created_at": (self.created_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.created_at else None),
            "updated_at": (self.updated_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.updated_at else None),
        }


# ------------------------------
# 新增：邮箱验证码（数据库持久化）
# ------------------------------
class EmailVerificationCode(Base):
    """邮箱验证码存储，用于注册/安全操作验证（DB-only）。"""
    __tablename__ = 'email_verification_codes'
    __table_args__ = (
        Index('ix_email_codes_email_created', 'email', 'created_at'),
    )

    id = Column(Integer, primary_key=True, autoincrement=True)
    email = Column(String(255), nullable=False, index=True)
    code = Column(String(20), nullable=False)
    expires_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=func.now())

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "email": self.email,
            "code": self.code,
            "expires_at": (self.expires_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.expires_at else None),
            "created_at": (self.created_at.replace(tzinfo=timezone.utc).isoformat().replace('+00:00', 'Z') if self.created_at else None),
        }