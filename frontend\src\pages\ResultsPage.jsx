import { useState, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import ResultCard from '../components/ResultCard'
import SearchInput from '../components/SearchInput'
import InsufficientPointsModal from '../components/InsufficientPointsModal'
import PayPalPurchaseModal from '../components/PayPalPurchaseModal'
import { ArrowLeft, RefreshCw, AlertCircle } from 'lucide-react'
import { api, getUserPoints, authStore } from '../services/api'
import logger from '../utils/logger'
import { setActiveSearch, clearActiveSearch } from '../services/searchSession'
import { recordPageVisit } from '../services/analytics'

const ResultsPage = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const [histSessionId, setHistSessionId] = useState(null)
  const [results, setResults] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const sessionId = location.state?.sessionId
  const effectiveSessionId = sessionId || histSessionId
  
  const query = location.state?.query || ''
  const searchResult = location.state?.searchResult
  const searchTime = location.state?.searchTime || 0

  const [displayQuery, setDisplayQuery] = useState(query)
  const [displaySearchTime, setDisplaySearchTime] = useState(searchTime)
  
  // 积分相关状态
  const [showInsufficientPointsModal, setShowInsufficientPointsModal] = useState(false)
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)
  const [userPoints, setUserPoints] = useState(null)

  // Convert backend data to UI list
  const convertBackendDataToResults = (backendData) => {
    logger.debug('results_convert_backend_data_start')
    
    if (!backendData) {
      logger.debug('results_no_backend_data')
      return []
    }

    // Check all possible locations for reddit posts data
    let postsData = null
    if (Array.isArray(backendData.reddit_posts) && backendData.reddit_posts.length > 0) {
      postsData = backendData.reddit_posts
      logger.debug('results_posts_top_level')
    } else if (Array.isArray(backendData.search_result?.reddit_posts) && backendData.search_result.reddit_posts.length > 0) {
      postsData = backendData.search_result.reddit_posts
      logger.debug('results_posts_in_search_result')
    } else if (Array.isArray(backendData.data?.reddit_posts) && backendData.data.reddit_posts.length > 0) {
      postsData = backendData.data.reddit_posts
      logger.debug('results_posts_in_data')
    } else if (Array.isArray(backendData.reddit_data?.posts) && backendData.reddit_data.posts.length > 0) {
      postsData = backendData.reddit_data.posts
      logger.debug('results_posts_in_reddit_data')
    } else if (Array.isArray(backendData.results) && backendData.results.length > 0) {
      postsData = backendData.results
      logger.debug('results_posts_in_results')
    }

    // If still no posts data, check if the entire backendData is an array
    if (!postsData && Array.isArray(backendData)) {
      postsData = backendData
      logger.debug('results_backend_data_is_array')
    }

    logger.debug('results_posts_data_found')

    // Fast path: if comments exist in top-level reddit_data, directly build results
    if (Array.isArray(backendData.reddit_data?.comments) && backendData.reddit_data.comments.length > 0 && Array.isArray(postsData) && postsData.length > 0) {
      const postsById = {}
      for (const p of postsData) {
        if (p && (p.id || p.post_id)) {
          postsById[p.id || p.post_id] = p
        }
      }
      const commentersHistoryAll = backendData.commenters_history || backendData.reddit_data?.user_histories || {}
      const credibilityAll = backendData?.llm_analysis?.credibility_analysis || {}
      const resultsFromComments = []
      for (const comment of backendData.reddit_data.comments) {
        if (!comment || !comment.body || !comment.author || comment.author === '[deleted]') continue
        const pid = comment.post_id || comment.postId
        const post = postsById[pid] || {}
        const commenterHistory = commentersHistoryAll?.[comment.author] || {}
        const commentKey = comment.permalink || comment.id
        const credibilityAnalysis = credibilityAll?.[comment.author] || null
        const commentAlignment = credibilityAnalysis?.per_comment?.[commentKey] || null
        resultsFromComments.push({
          id: `${pid}-${comment.id || Math.random().toString(36).slice(2,8)}`,
          author: comment.author,
          subreddit: post.subreddit || 'unknown',
          comment: comment.body,
          fullComment: comment.body,
          score: comment.score || 0,
          karma: commenterHistory.comment_karma || commenterHistory.karma || 0,
          url: post.url || `https://reddit.com${post.permalink || ''}`,
          _rawData: {
            post,
            comment,
            commenterHistory,
            backgroundAnalysis: {
              credibility: credibilityAnalysis,
              alignment: commentAlignment
            }
          },
          insights: commentAlignment?.insights || credibilityAnalysis?.overall_insights || null,
          recommendation: commentAlignment?.recommendation || null,
          summary: comment.body ? comment.body.substring(0, 200) + (comment.body.length > 200 ? '...' : '') : '',
          tags: []
        })
      }
      if (resultsFromComments.length > 0) {
        return resultsFromComments
      }
    }

    if (!postsData || postsData.length === 0) {
      logger.debug('results_no_posts_data')
      return []
    }

    // Normalize backend shape: if reddit_posts are plain posts and comments are separate,
    // merge them into the { post, comments } structure expected by the UI.
    let normalizedPosts = []
    try {
      const rawPosts = postsData
      logger.debug('results_raw_posts_loaded')
      
      // If already in expected shape (array items contain `post`), keep as is
      if (rawPosts.length > 0 && typeof rawPosts[0] === 'object' && rawPosts[0] && 'post' in rawPosts[0]) {
        logger.debug('results_posts_expected_shape')
        normalizedPosts = rawPosts
      } else {
        logger.debug('results_normalizing_posts_with_comments')
        const comments = Array.isArray(backendData.reddit_comments)
          ? backendData.reddit_comments
          : (Array.isArray(backendData.reddit_data?.comments) ? backendData.reddit_data.comments : [])
        logger.debug('results_comments_loaded')
        
        const commentsByPostId = {}
        for (const c of comments) {
          const pid = c.post_id || c.postId || null
          if (!pid) {
            logger.debug('results_skip_comment_without_post_id')
            continue
          }
          if (!commentsByPostId[pid]) commentsByPostId[pid] = []
          commentsByPostId[pid].push(c)
        }
        logger.debug('results_comments_grouped_by_post_id')
        
        normalizedPosts = rawPosts.map((p) => ({
          success: true,
          post: p,
          comments: commentsByPostId[p.id] || [],
          commenters: []
        }))
      }
    } catch (e) {
      logger.error('results_error_normalizing_posts')
      // Fallback: ensure an array
      normalizedPosts = []
    }

    logger.debug('results_normalized_posts_ready')

    if (!normalizedPosts || normalizedPosts.length === 0) {
      logger.debug('results_no_normalized_posts')
      return []
    }

    const convertedResults = []
    const commentersHistoryAll = backendData.commenters_history || backendData.reddit_data?.user_histories || {}
    
    // Reddit posts and comments
    normalizedPosts.forEach((postData, postIndex) => {
      const post = postData.post || postData
      const comments = postData.comments || []
      
      logger.debug('results_processing_post')
      
      // 如果有评论，为每条有效评论创建一个结果
      let hasValidComments = false
      
      comments.forEach((comment, commentIndex) => {
        logger.debug('results_processing_comment')
        if (comment.author && comment.body && comment.author !== '[deleted]') {
          hasValidComments = true
          const resultId = `${postIndex}-${commentIndex}`
          
          // Commenter history
          const commenterHistory = commentersHistoryAll?.[comment.author] || {}
          
      // Background analysis data
      const credibilityAnalysis = backendData?.llm_analysis?.credibility_analysis?.[comment.author] || null
      // Align per-comment key with backend
      const commentKey = comment.permalink || comment.id
      const commentAlignment = credibilityAnalysis?.per_comment?.[commentKey] || null

          convertedResults.push({
            id: resultId,
            author: comment.author,
            subreddit: post.subreddit || 'unknown',
            comment: comment.body, // full comment text
            fullComment: comment.body, // duplicate for clarity
            score: comment.score || 0,
            karma: commenterHistory.comment_karma || commenterHistory.karma || 0,
            url: post.url || `https://reddit.com${post.permalink || ''}`,
            // Preserve raw data for detail components
            _rawData: {
              post,
              comment,
              commenterHistory,
              backgroundAnalysis: {
                credibility: credibilityAnalysis,
                alignment: commentAlignment
              }
            },
            
            // Fields from LLM analysis
            insights: commentAlignment?.insights || credibilityAnalysis?.overall_insights || null,
            
            recommendation: commentAlignment?.recommendation || null,
            
            // Create summary from beginning of comment if needed
            summary: comment.body ? comment.body.substring(0, 200) + (comment.body.length > 200 ? '...' : '') : '',
            
            // Tags from backend or default
            tags: []
          })
        }
      })
      
      // 如果帖子没有有效评论，创建一个帖子条目
      if (!hasValidComments && post.title) {
        logger.debug('results_post_no_valid_comments')
        convertedResults.push({
          id: `post-${postIndex}`,
          author: post.author || 'Unknown',
          subreddit: post.subreddit || 'unknown',
          comment: post.selftext ? (post.selftext.length > 200 ? `${post.selftext.substring(0, 200)}...` : post.selftext) : 'Original post (no comments extracted)',
          fullComment: post.selftext || '',
          score: post.score || 0,
          karma: 0,
          url: post.url || `https://reddit.com${post.permalink || ''}`,
          isPost: true, // 标记这是一个帖子而不是评论
          _rawData: {
            post,
            comment: null,
            commenterHistory: {},
            backgroundAnalysis: {}
          },
          insights: null,
          recommendation: null,
          summary: post.title || '',
          tags: ['post']
        })
      }
    })

    return convertedResults
  }




  // Load/process data
  useEffect(() => {
    // 记录页面访问（用于分析漏斗）
    recordPageVisit('/results')

    // 结果页挂载后清理 active search 状态，避免下次误恢复
    try { clearActiveSearch() } catch {}
    // Listen to popstate to support tests that inject history.state
    const handlePopState = () => {
      try {
        const s = window.history?.state?.sessionId
        if (s) setHistSessionId(s)
      } catch {}
    }
    window.addEventListener('popstate', handlePopState)
    // Initialize once on mount
    handlePopState()
    return () => window.removeEventListener('popstate', handlePopState)
  }, [])

  useEffect(() => {
    const processAndSet = (backendData, qText, timeMs) => {
      logger.debug('results_processing_search_result')
      if (backendData && backendData.success === false) {
        setError(backendData.error_message || 'Search failed')
        setLoading(false)
        return
      }

      const convertedResults = convertBackendDataToResults(backendData)
      logger.debug('results_converted_ready')

      try {
        const simplifiedResults = convertedResults.map(result => ({
          ...result,
          _rawData: {
            post: { id: result._rawData?.post?.id },
            comment: { id: result._rawData?.comment?.id },
            commenterHistory: {
              comment_karma: result._rawData?.commenterHistory?.comment_karma,
              link_karma: result._rawData?.commenterHistory?.link_karma
            },
            backgroundAnalysis: result._rawData?.backgroundAnalysis || {}
          }
        }))
        localStorage.setItem('cogbridges_results', JSON.stringify(simplifiedResults))
      } catch (storageError) {
        logger.warn('results_localstorage_save_too_large')
        try {
          localStorage.removeItem('cogbridges_results')
          const basicResults = convertedResults.map(result => ({
            id: result.id,
            author: result.author,
            subreddit: result.subreddit,
            comment: result.comment,
            summary: result.summary,
            score: result.score,
            karma: result.karma,
            insights: result.insights,
            recommendation: result.recommendation,
            url: result.url,
            tags: result.tags
          }))
          localStorage.setItem('cogbridges_results', JSON.stringify(basicResults))
        } catch (fallbackError) {
          logger.error('results_localstorage_save_failed_even_simplified')
        }
      }

      setDisplayQuery(qText || '')
      setDisplaySearchTime(timeMs || 0)
      setResults(convertedResults)
      setError(null)
      setLoading(false)
    }

    const run = async () => {
      try {
        // If results not directly passed but we have sessionId, load from backend
        if (!searchResult && effectiveSessionId) {
          logger.debug('results_loading_session_from_backend')
          setLoading(true)
          const session = await api.getResultDetail(effectiveSessionId)
          logger.debug('results_session_data_received')
          
          if (!session) throw new Error('No session data')
          
          // Check different possible data structures
          logger.debug('results_checking_data_structure')
          
          // Check if the data might be in the raw_data field
          logger.debug('results_session_raw_data_present')
          if (session.raw_data) {
            logger.debug('results_session_raw_data_type')
            let rawDataObj = null

            // Handle both string and object raw_data
            if (typeof session.raw_data === 'string') {
              try {
                rawDataObj = JSON.parse(session.raw_data)
                logger.debug('results_parsed_raw_data_ok')
              } catch (e) {
                logger.error('results_parse_raw_data_failed')
              }
            } else if (typeof session.raw_data === 'object') {
              rawDataObj = session.raw_data
              logger.debug('results_raw_data_is_object')
            }

            // If raw_data contains the actual data, use it
            if (rawDataObj) {
              logger.debug('results_raw_data_reddit_posts_present')
              // 优先使用 search_result 中的数据，因为它包含完整的相关性分数
              if (rawDataObj.search_result?.reddit_posts && rawDataObj.search_result.reddit_posts.length > 0) {
                logger.debug('results_use_raw_data_search_result')
                session.reddit_posts = rawDataObj.search_result.reddit_posts
                session.commenters_history = rawDataObj.search_result.commenters_history || {}
                session.llm_analysis = rawDataObj.search_result.llm_analysis || session.llm_analysis || {}
              } else if (rawDataObj.reddit_posts && rawDataObj.reddit_posts.length > 0) {
                logger.debug('results_use_raw_data_top_level')
                session.reddit_posts = rawDataObj.reddit_posts
                session.reddit_comments = rawDataObj.reddit_comments || []
                session.user_histories = rawDataObj.user_histories || []
                session.commenters_history = rawDataObj.commenters_history || {}
                session.llm_analysis = rawDataObj.llm_analysis || rawDataObj.search_result?.llm_analysis || session.llm_analysis || {}
              } else if (rawDataObj.reddit_data?.posts && rawDataObj.reddit_data.posts.length > 0) {
                logger.debug('results_use_raw_data_reddit_data')
                session.reddit_posts = rawDataObj.reddit_data.posts
                session.reddit_comments = rawDataObj.reddit_data.comments || []
                session.user_histories = rawDataObj.reddit_data.user_histories || rawDataObj.user_histories || []
                session.commenters_history = rawDataObj.reddit_data.user_histories || rawDataObj.commenters_history || {}
                session.llm_analysis = rawDataObj.llm_analysis || rawDataObj.search_result?.llm_analysis || session.llm_analysis || {}
              } else {
                // Even if posts aren't present in raw_data, still merge llm_analysis/commenter history to enrich UI
                if (rawDataObj.llm_analysis || rawDataObj.search_result?.llm_analysis) {
                  session.llm_analysis = rawDataObj.llm_analysis || rawDataObj.search_result?.llm_analysis
                }
                if (rawDataObj.commenters_history) {
                  session.commenters_history = rawDataObj.commenters_history
                }
                if (rawDataObj.reddit_data?.user_histories) {
                  session.commenters_history = session.commenters_history || rawDataObj.reddit_data.user_histories
                }
              }
            }
          }

          const sr = (session?.search_result && session.search_result.reddit_posts)
            ? session.search_result
            : session
          
          logger.debug('results_selected_sr')
          
          // Handle different data structures from backend
          // JSON storage: reddit_posts at top level
          // Database: reddit_posts inside search_result
          let finalData = sr
          if (!finalData.reddit_posts && finalData.search_result?.reddit_posts) {
            logger.debug('results_use_reddit_posts_from_search_result')
            finalData = finalData.search_result
          }
          
          logger.debug('results_final_data_ready')
          
          // Ensure analysis and history fields are present even if nested structure lacks them
          if (!finalData.llm_analysis && session?.llm_analysis) {
            finalData = { ...finalData, llm_analysis: session.llm_analysis }
          }
          if (!finalData.commenters_history && session?.commenters_history) {
            finalData = { ...finalData, commenters_history: session.commenters_history }
          }
          if (
            !(finalData.reddit_data && finalData.reddit_data.user_histories) &&
            (session?.user_histories || session?.reddit_data?.user_histories)
          ) {
            finalData = {
              ...finalData,
              reddit_data: {
                ...(finalData.reddit_data || {}),
                user_histories: finalData.reddit_data?.user_histories || session?.reddit_data?.user_histories || session?.user_histories || {}
              }
            }
          }
          // Ensure comments are available for result construction
          if (!Array.isArray(finalData.reddit_comments) && Array.isArray(session?.reddit_comments)) {
            finalData = { ...finalData, reddit_comments: session.reddit_comments }
          }
          if (
            !(finalData.reddit_data && Array.isArray(finalData.reddit_data.comments)) &&
            Array.isArray(session?.reddit_data?.comments)
          ) {
            finalData = {
              ...finalData,
              reddit_data: {
                ...(finalData.reddit_data || {}),
                comments: finalData.reddit_data?.comments || session.reddit_data.comments
              }
            }
          }
          
          const qField = finalData?.query
          const qText = (typeof qField === 'string') ? qField : (qField?.query || '')
          const timeMs = Math.round(((session?.statistics?.total_time || session?.total_time || 0) * 1000))
          
          logger.debug('results_query_time_ready')
          
          // Also check for query in session level if not found
          const finalQuery = qText || session?.query || ''
          logger.debug('results_final_query_ready')
          
          processAndSet(finalData, finalQuery, timeMs)
          return
        }

        // Original path: both query and searchResult needed
        if (!query) {
          // No query provided; if no session/result either, show empty state
          setLoading(false)
          return
        }
        if (!searchResult) {
          setError('No search result data')
          setLoading(false)
          return
        }
        processAndSet(searchResult, query, searchTime)
      } catch (err) {
        logger.error('results_data_processing_failed')
        setError('Data processing failed: ' + err.message)
        setLoading(false)
      }
    }
    run()
  }, [query, searchResult, navigate, effectiveSessionId, searchTime])

  const handleNewSearch = async (newQuery) => {
    try {
      // 检查用户是否已登录
      if (authStore.authenticated) {
        // 检查用户积分是否充足
        try {
          const pointsData = await getUserPoints()
          if (pointsData.success) {
            setUserPoints(pointsData.balance)
            if (pointsData.balance < 20) {
              // 积分不足，显示提示弹窗
              setShowInsufficientPointsModal(true)
              return
            }
          }
        } catch (error) {
          logger.error('results_check_user_points_failed')
        }
      }

      setLoading(true)

      // Generate clientSessionId in the same format as HomePage
      const clientSessionId = `${Date.now()}_${Math.random().toString(36).slice(2, 8)}`
      
      // Set active search to track the session
      setActiveSearch({ query: newQuery, clientSessionId, status: 'starting', startedAt: Date.now() })
      
      // Navigate to LoadingPage to execute the new search
      navigate('/loading', {
        state: {
          query: newQuery,
          sessionId: clientSessionId  // Pass the correctly formatted sessionId
        }
      })
    } catch (error) {
      logger.error('results_new_search_failed')
    }
  }


  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin text-primary-500 mx-auto mb-4" />
          <p className="text-gray-600">Processing search results...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/')}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              
              <div>
                <h1 className="text-lg sm:text-xl font-semibold text-gray-800">Featured Comment</h1>
                <p className="text-xs sm:text-sm text-gray-500">{displayQuery}</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 py-6">
        {/* Error banner */}
        {error && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg flex items-center space-x-3">
            <AlertCircle className="w-5 h-5 text-yellow-500" />
            <div>
              <p className="text-yellow-800 font-medium">Notice</p>
              <p className="text-yellow-700 text-sm">{error}</p>
            </div>
          </div>
        )}
        {/* Controls */}
        <div className="mb-4">
          <SearchInput
            onSearch={handleNewSearch}
            placeholder=""
          />
          {displaySearchTime > 0 && (
            <p className="text-xs text-gray-500 mt-2">Search time: {Math.round(displaySearchTime / 1000)}s</p>
          )}
        </div>

        {/* Results */}
        {results.length > 0 ? (
          <div className="space-y-4">
            {results.map((result) => (
              <ResultCard key={result.id} result={result} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <AlertCircle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-800 mb-2">No results yet</h3>
            <p className="text-gray-500">Try another search</p>
          </div>
        )}
      </main>

      {/* Modals */}
      <InsufficientPointsModal
        isOpen={showInsufficientPointsModal}
        onClose={() => setShowInsufficientPointsModal(false)}
        onPurchaseClick={() => setShowPurchaseModal(true)}
        currentPoints={userPoints || 0}
      />
      <PayPalPurchaseModal
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
        onSuccess={() => {
          setShowPurchaseModal(false);
          // Refresh points after successful payment
          getUserPoints().then(data => {
            if (data?.success) {
              setUserPoints(data.balance);
            }
          }).catch(console.error);
        }}
      />
    </div>
  )
}

export default ResultsPage 