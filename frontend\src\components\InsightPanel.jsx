import { Brain } from 'lucide-react'

const InsightPanel = ({ userInsights, className = "" }) => {
  if (!userInsights) return null

  const {
    username,
    personalityAnalysis,
    motivationAnalysis,
    whyRecommended
  } = userInsights

  return (
    <div className={`bg-white rounded-lg shadow-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="bg-gradient-to-r from-primary-500 to-primary-600 text-white p-4 rounded-t-lg">
        <div className="flex items-center">
          <Brain className="w-6 h-6 mr-2" />
          <h3 className="text-lg font-semibold">Commenter insights</h3>
        </div>
        <p className="text-primary-100 text-sm mt-1">AI insights based on posting history</p>
      </div>

      <div className="p-6 space-y-6">
        {/* User basic */}
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div>
            <h4 className="font-semibold text-gray-800">
              <a
                href={`https://www.reddit.com/user/${encodeURIComponent(username || '')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 hover:underline"
                title={`View u/${username} on Reddit`}
              >
                u/{username}
              </a>
            </h4>
            <p className="text-gray-600 text-sm">Reddit user</p>
          </div>

        </div>



        {/* Personality */}
        <div>
          <h4 className="font-semibold text-gray-800 mb-3">Personality traits</h4>
          <div className="p-4 bg-green-50 rounded-lg">
            <p className="text-gray-700 leading-relaxed">
              {personalityAnalysis || "Shows signs of rational thinking, tends to analyze from multiple angles, and writes in a calm, objective tone."}
            </p>
          </div>
        </div>

        {/* Motivation */}
        <div>
          <h4 className="font-semibold text-gray-800 mb-3">Motivation</h4>
          <div className="p-4 bg-purple-50 rounded-lg">
            <p className="text-gray-700 leading-relaxed">
              {motivationAnalysis || "Often values work-life balance and autonomy; appears comfortable with reasonable risk."}
            </p>
          </div>
        </div>

        {/* Why recommended */}
        <div>
          <h4 className="font-semibold text-gray-800 mb-3">Why we recommend this commenter</h4>
          <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <p className="text-gray-700 leading-relaxed">
              {whyRecommended || "Has substantial relevant experience and shares grounded, practical advice from real situations—worth considering."}
            </p>
          </div>
        </div>


      </div>
    </div>
  )
}

export default InsightPanel 