import { HashRouter as Router, Routes, Route, useLocation, useNavigate } from 'react-router-dom'
import HomePage from './pages/HomePage'
import LoadingPage from './pages/LoadingPage'
import ResultsPage from './pages/ResultsPage'
import HistoryPage from './pages/HistoryPage'
import TestPage from './pages/TestPage'
import LoginPage from './pages/LoginPage'
import RegisterPage from './pages/RegisterPage'
import PayPalSuccessPage from './pages/PayPalSuccessPage'
import PaymentCancelPage from './pages/PaymentCancelPage'
import { useEffect } from 'react'
import { getActiveSearch, isSearchInProgress, clearActiveSearch } from './services/searchSession'

function SearchRecovery() {
  const location = useLocation()
  const navigate = useNavigate()
  useEffect(() => {
    try {
      const active = getActiveSearch()
      if (active) {
        // 如果搜索正在进行中，导航到加载页
        if (isSearchInProgress(active.status)) {
          if (location.pathname !== '/loading') {
            navigate('/loading', { state: { query: active.query, sessionId: active.clientSessionId }, replace: true })
          }
        } 
        // 如果搜索已完成且有结果，导航到结果页
        else if (active.status === 'completed' && active.result) {
          // 搜索已完成：无论当前是否在 /loading，都应跳转到结果页
          if (location.pathname !== '/results') {
            navigate('/results', {
              state: {
                query: active.query,
                searchResult: active.result,
                searchTime: active.searchTime || active.result.search_time || 0
              },
              replace: true
            })
            // 清除已完成的搜索记录
            clearActiveSearch()
          }
        }
      }
    } catch (_) {}
  }, [location.pathname, navigate])
  return null
}

function App() {
  return (
    <Router>
      <div>
        <SearchRecovery />
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/loading" element={<LoadingPage />} />
          <Route path="/results" element={<ResultsPage />} />
          <Route path="/history" element={<HistoryPage />} />
          <Route path="/test" element={<TestPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          <Route path="/payment/success" element={<PayPalSuccessPage />} />
          <Route path="/payment/cancel" element={<PaymentCancelPage />} />
        </Routes>
      </div>
    </Router>
  )
}

// 导出App组件，供其他文件使用
export default App
