from flask import Blueprint, jsonify, request
from utils.logger_utils import get_logger
from .app import get_cogbridges_service

logger = get_logger(__name__)

bp_sessions = Blueprint("sessions", __name__)


@bp_sessions.route('/api/sessions/<session_id>', methods=['GET'])
def get_session_detail(session_id):
    try:
        service = get_cogbridges_service()
        if not service:
            return jsonify({"success": False, "error": "CogBridges service not initialized"}), 503
        session_data = service.data_service.load_session_data(session_id)
        if not session_data:
            return jsonify({"success": False, "error": "Session not found"}), 404
        return jsonify({"success": True, "data": session_data})
    except Exception as e:
        logger.error(f"Failed to get session detail: {e}")
        return jsonify({"success": False, "error": f"Failed to get session detail: {str(e)}"}), 500


@bp_sessions.route('/api/sessions/<session_id>', methods=['DELETE'])
def delete_session(session_id):
    try:
        service = get_cogbridges_service()
        if not service:
            return jsonify({"success": False, "error": "CogBridges service not initialized"}), 503
        success = service.data_service.delete_session(session_id)
        if success:
            return jsonify({"success": True, "message": "Session deleted"})
        else:
            return jsonify({"success": False, "error": "Session not found or deletion failed"}), 404
    except Exception as e:
        logger.error(f"Failed to delete session: {e}")
        return jsonify({"success": False, "error": f"Failed to delete session: {str(e)}"}), 500


@bp_sessions.route('/api/storage/statistics', methods=['GET'])
def get_storage_statistics():
    try:
        service = get_cogbridges_service()
        if not service:
            return jsonify({"success": False, "error": "CogBridges service not initialized"}), 503
        stats = service.data_service.get_storage_statistics()
        return jsonify({"success": True, "data": stats})
    except Exception as e:
        logger.error(f"Failed to get storage statistics: {e}")
        return jsonify({"success": False, "error": f"Failed to get storage statistics: {str(e)}"}), 500


# 迁移端点已移除（PostgreSQL-only）


@bp_sessions.route('/api/sessions/<session_id>/llm-analysis', methods=['GET'])
def get_session_llm_analysis(session_id):
    try:
        service = get_cogbridges_service()
        if not service:
            return jsonify({"success": False, "error": "CogBridges service not initialized"}), 503
        session_data = service.data_service.load_session_data(session_id)
        if not session_data:
            return jsonify({"success": False, "error": "Session not found"}), 404
        llm_analysis = session_data.get('llm_analysis', {})
        if not llm_analysis:
            return jsonify({"success": False, "error": "This session has no LLM analysis data"}), 404
        response_data = {
            "session_id": session_id,
            "llm_analysis": llm_analysis,
            "summary": {
                "analysis_success": llm_analysis.get('success', False),
                "analysis_time": llm_analysis.get('analysis_time', 0.0),
                "similarity_users_count": len(llm_analysis.get('similarity_analysis', {})),
                "motivation_users_count": len(llm_analysis.get('motivation_analysis', {})),
                "total_motivation_analyses": sum(len(analyses) for analyses in llm_analysis.get('motivation_analysis', {}).values()),
            },
        }
        return jsonify({"success": True, "data": response_data})
    except Exception as e:
        logger.error(f"Failed to get LLM analysis data: {e}")
        return jsonify({"success": False, "error": f"Failed to get LLM analysis data: {str(e)}"}), 500