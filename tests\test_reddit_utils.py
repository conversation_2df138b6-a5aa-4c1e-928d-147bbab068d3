

def test_prune_posts_to_top_n_comments_with_relevance_score():
    """测试 prune_posts_to_top_n_comments 优先使用相关性分数，但保持帖子内的原始顺序"""
    from utils.reddit_utils import prune_posts_to_top_n_comments
    
    posts = [
        {
            "success": True,
            "post": {"id": "p1", "title": "Post 1"},
            "comments": [
                {"id": "c1", "body": "low score but high relevance", "score": 5, "_relevance_score": 0.9, "author": "u1", "permalink": "link1"},
                {"id": "c2", "body": "high score but low relevance", "score": 100, "_relevance_score": 0.3, "author": "u2", "permalink": "link2"},
                {"id": "c3", "body": "medium both", "score": 50, "_relevance_score": 0.6, "author": "u3", "permalink": "link3"},
            ],
            "commenters": ["u1", "u2", "u3"]
        },
        {
            "success": True,
            "post": {"id": "p2", "title": "Post 2"},
            "comments": [
                {"id": "c4", "body": "highest relevance", "score": 10, "_relevance_score": 0.95, "author": "u4", "permalink": "link4"},
                {"id": "c5", "body": "lowest relevance", "score": 90, "_relevance_score": 0.1, "author": "u5", "permalink": "link5"},
            ],
            "commenters": ["u4", "u5"]
        }
    ]
    
    # 测试选择前3条评论
    result = prune_posts_to_top_n_comments(posts, 3)
    
    # 应该选择相关性最高的3条：c4 (0.95), c1 (0.9), c3 (0.6)
    all_comments = []
    for post in result:
        all_comments.extend(post["comments"])
    
    assert len(all_comments) == 3
    comment_ids = [c["id"] for c in all_comments]
    assert set(comment_ids) == {"c4", "c1", "c3"}
    
    # 验证每个帖子内的评论保持原始顺序
    # Post 1中，c1在c3之前（原始顺序）
    post1_comments = [p for p in result if p["post"]["id"] == "p1"]
    if post1_comments:
        post1_comment_ids = [c["id"] for c in post1_comments[0]["comments"]]
        # c1应该在c3之前，因为这是它们的原始顺序
        assert post1_comment_ids == ["c1", "c3"]