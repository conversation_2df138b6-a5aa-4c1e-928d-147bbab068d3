import time
from typing import Any, Dict, Optional
from pathlib import Path

search_status: Dict[str, Dict[str, Any]] = {}
active_search_tasks: Dict[str, Dict[str, Any]] = {}

_PROGRESS_DIR = None  # Removed file-based persistence


def _progress_file_path(session_id: str):
    return None


def _atomic_write_json(path, data: Dict[str, Any]) -> None:
    return


def get_search_status(session_id: str) -> Optional[Dict[str, Any]]:
    """Get search status from memory or file.

    Returns None if not found.
    """
    info = search_status.get(session_id)
    if info is not None:
        return info
    # Removed file fallback; in multi-worker deployments use a shared external store (DB/Redis)
    return None


def update_search_status(session_id: str, status: str, progress: int = 0, error: str | None = None, result: dict | None = None, start_time: float | None = None):
    payload: Dict[str, Any] = {
        'status': status,
        'progress': progress,
        'error': error,
        'result': result,
        'timestamp': time.time(),
    }
    
    # If this is the first update (running status) and start_time is provided, store it
    if status == 'running' and start_time is not None:
        payload['start_time'] = start_time
    # Otherwise, preserve existing start_time if it exists
    elif session_id in search_status and 'start_time' in search_status[session_id]:
        payload['start_time'] = search_status[session_id]['start_time']
    
    search_status[session_id] = payload
    # Removed file write; keep in-memory only


def clear_search_status(session_id: str) -> None:
    """Optional helper to remove status from memory and file."""
    try:
        search_status.pop(session_id, None)
    except Exception:
        pass
    # No-op for file cleanup