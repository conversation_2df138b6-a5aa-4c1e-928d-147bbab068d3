import os
import importlib


def test_health_route_returns_ok(monkeypatch):
    # 确保允许的域名包含线上域（配置解析在创建app时读取）
    monkeypatch.setenv("ALLOWED_ORIGINS", "https://cogbridges.com,https://www.cogbridges.com,https://cogbridges-api.onrender.com")
    app_module = importlib.import_module("api.app")
    app = app_module.create_app()
    client = app.test_client()
    resp = client.get("/api/health")
    assert resp.status_code == 200
    data = resp.get_json()
    assert data.get("status") == "healthy"