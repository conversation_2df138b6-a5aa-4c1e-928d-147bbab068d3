#!/usr/bin/env python3
"""
CogBridges Frontend Server Startup Script
Standalone React frontend development server launcher
"""

import sys
import os
import subprocess
import time
import socket
import webbrowser
import psutil
from pathlib import Path
import signal
import atexit

# Add project path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from utils.logger_utils import get_logger


class FrontendServer:
    """Frontend server launcher"""
    
    def __init__(self):
        """Initialize frontend server"""
        self.logger = get_logger(__name__)
        self.process = None
        self.running = False
        
        # Frontend directory
        self.frontend_dir = project_root / "frontend"
        
    def _get_local_ipv4(self) -> str:
        """Get local LAN IPv4 address (for mobile access)
        Priority: env var LAN_IP → RFC1918 private ranges (192.168/10/172.16-31) → default route inference → hostname resolution.
        Avoid returning 127.* / 169.254.* / 198.18.* and other addresses not suitable for mobile access.
        """
        # Environment variable override
        env_ip = os.getenv("LAN_IP")
        if env_ip:
            return env_ip
        
        # Enumerate all network interfaces
        try:
            import psutil
            import ipaddress
            candidates = []
            for _name, addrs in psutil.net_if_addrs().items():
                for addr in addrs:
                    if getattr(socket, 'AF_INET', 2) == addr.family:
                        ip = addr.address
                        try:
                            ip_obj = ipaddress.ip_address(ip)
                            if ip_obj.is_private and not ip_obj.is_loopback:
                                # Exclude 169.254.* and 198.18.*
                                if not ip.startswith("169.254.") and not ip.startswith("198.18."):
                                    candidates.append(ip)
                        except Exception:
                            continue
            if candidates:
                # Priority: 192.168.* > 10.* > 172.16-31.*
                def priority(ip: str):
                    if ip.startswith("192.168."):
                        return (0, ip)
                    if ip.startswith("10."):
                        return (1, ip)
                    if ip.startswith("172."):
                        try:
                            second = int(ip.split('.')[1])
                            if 16 <= second <= 31:
                                return (2, ip)
                        except Exception:
                            pass
                    return (9, ip)
                candidates.sort(key=priority)
                return candidates[0]
        except Exception:
            pass

        # Default route inference
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            try:
                sock.connect(("*******", 80))
                ip = sock.getsockname()[0]
            finally:
                sock.close()
            # If not private network or is test/link-local range, then fallback
            import ipaddress
            try:
                ip_obj = ipaddress.ip_address(ip)
                if ip_obj.is_private and not ip.startswith("169.254.") and not ip.startswith("198.18."):
                    return ip
            except Exception:
                pass
        except Exception:
            pass

        # Hostname resolution fallback
        try:
            return socket.gethostbyname(socket.gethostname())
        except Exception:
            return "127.0.0.1"
    
    def check_port(self, port, host="localhost"):
        """Check port occupation"""
        print(f"🔍 Checking frontend port {host}:{port} status...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"⚠️ Frontend port {port} is already in use")
                return False
            else:
                print(f"✅ Frontend port {port} is available")
                return True
                
        except Exception as e:
            self.logger.error(f"Error checking frontend port {port}: {e}")
            print(f"⚠️ Error checking frontend port {port}: {e}")
            return False
    
    def cleanup_port(self, port, host="localhost"):
        """Clean up port occupation"""
        print(f"🧹 Cleaning up frontend port {host}:{port}...")
        
        try:
            # Find processes using the port
            processes_using_port = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    connections = proc.connections()
                    for conn in connections:
                        if conn.laddr.port == port:
                            processes_using_port.append({
                                'pid': proc.pid,
                                'name': proc.name()
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            if processes_using_port:
                print(f"📋 Found {len(processes_using_port)} process(es) using frontend port {port}:")
                for proc_info in processes_using_port:
                    print(f"   - PID: {proc_info['pid']}, Name: {proc_info['name']}")
                
                # Ask whether to clean port
                try:
                    response = input(f"\n❓ Terminate processes using frontend port {port}? (y/N): ").strip().lower()
                    if response in ['y', 'yes']:
                        killed_count = 0
                        for proc_info in processes_using_port:
                            try:
                                proc = psutil.Process(proc_info['pid'])
                                proc.terminate()
                                proc.wait(timeout=5)
                                print(f"✅ Terminated process PID {proc_info['pid']} ({proc_info['name']})")
                                killed_count += 1
                            except (psutil.NoSuchProcess, psutil.TimeoutExpired, psutil.AccessDenied) as e:
                                print(f"⚠️ Unable to terminate process PID {proc_info['pid']}: {e}")
                        
                        if killed_count > 0:
                            print(f"✅ Cleaned {killed_count} process(es) using the frontend port")
                            time.sleep(2)
                            return True
                        else:
                            print("❌ Failed to clean any processes")
                            return False
                    else:
                        print("⏭️ Skipping port cleanup")
                        return False
                except KeyboardInterrupt:
                    print("\n⏭️ User cancelled, skipping port cleanup")
                    return False
            else:
                print(f"⚠️ Frontend port {port} is occupied but unable to identify the process")
                return False
                
        except Exception as e:
            self.logger.error(f"Error cleaning frontend port {port}: {e}")
            print(f"⚠️ Error cleaning frontend port {port}: {e}")
            return False
    
    def check_dependencies(self):
        """Check frontend dependencies"""
        print("🔍 Checking frontend dependencies...")
        
        # Check if frontend directory exists
        if not self.frontend_dir.exists():
            print(f"❌ React frontend directory does not exist: {self.frontend_dir}")
            print("💡 Please first run 'npm create vite@latest frontend -- --template react' to create frontend project")
            return False
        
        # Check if package.json exists
        package_json = self.frontend_dir / "package.json"
        if not package_json.exists():
            print(f"❌ Frontend project not initialized: {package_json}")
            print("💡 Please first run 'npm install' in frontend directory to install dependencies")
            return False
        
        # Check if node_modules exists
        node_modules = self.frontend_dir / "node_modules"
        if not node_modules.exists():
            print("📦 Detected frontend dependencies not installed, installing...")
            try:
                result = subprocess.run(
                    "npm install",
                    cwd=str(self.frontend_dir),
                    capture_output=True,
                    text=True,
                    timeout=120,
                    shell=True
                )
                if result.returncode != 0:
                    print(f"❌ Frontend dependency installation failed: {result.stderr}")
                    return False
                print("✅ Frontend dependencies installed")
            except subprocess.TimeoutExpired:
                print("❌ Frontend dependency installation timeout")
                return False
            except Exception as e:
                print(f"❌ Frontend dependency installation failed: {e}")
                return False
        else:
            print("✅ Frontend dependencies already installed")
        
        return True
    
    def check_configuration(self):
        """Check configuration"""
        print("🔧 Checking frontend configuration...")
        
        # Check if API server is accessible
        api_url = f"http://{config.HOST}:{config.PORT}"
        print(f"📡 Backend API URL: {api_url}")
        
        return True
    
    def start_server(self):
        """Start frontend server"""
        print("🚀 Starting React frontend development server...")
        
        # Frontend port is API port + 1
        frontend_port = config.PORT + 1
        
        # Check port
        if not self.check_port(frontend_port, "localhost"):
            if not self.cleanup_port(frontend_port, "localhost"):
                print(f"⚠️ Frontend port {frontend_port} unavailable, trying alternative port")
                frontend_port = frontend_port + 1
                print(f"🔄 Using frontend port: {frontend_port}")
                # Check new port again
                if not self.check_port(frontend_port, "localhost"):
                    print(f"❌ Frontend port {frontend_port} also unavailable, startup failed")
                    return False
        
        try:
            # Set environment variables
            env = os.environ.copy()
            local_ip = self._get_local_ipv4()
            env['VITE_API_URL'] = f"http://{local_ip}:{config.PORT}"
            
            # Start Vite development server
            cmd = f"npm run dev -- --port {frontend_port} --host"
            self.process = subprocess.Popen(
                cmd,
                cwd=str(self.frontend_dir),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                shell=True
            )
            
            self.running = True
            
            print(f"📍 Starting frontend server: localhost:{frontend_port}")
            print("=" * 60)
            print("🎉 React frontend development server started successfully!")
            print("=" * 60)
            print(f"🌐 Local access: http://localhost:{frontend_port}")
            print(f"📱 LAN access (mobile/other devices): http://{local_ip}:{frontend_port}")
            print(f"🔗 Backend API: {env['VITE_API_URL']}")
            print("-" * 60)
            print("💡 Usage instructions:")
            print("  1. Open browser to access the web interface")
            print("  2. Enter search query")
            print("  3. View search results and analysis")
            print("-" * 60)
            print("🛑 Press Ctrl+C to stop service")
            print("=" * 60)
            
            # Open browser
            time.sleep(3)
            self.open_browser(frontend_port, local_ip)
            
            # Wait for process
            while self.running and self.process.poll() is None:
                line = self.process.stdout.readline()
                if line:
                    print(line.strip())
            
            return True
            
        except Exception as e:
            self.logger.error(f"Frontend server startup failed: {e}")
            print(f"❌ Frontend server startup failed: {e}")
            return False
    
    def open_browser(self, port, local_ip):
        """Open browser"""
        url = f"http://localhost:{port}"
        
        print(f"🌐 Opening browser: {url}")
        
        try:
            # Check if frontend service is available
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(("localhost", port))
            sock.close()
            
            if result == 0:
                webbrowser.open(url)
                print("✅ Browser opened")
            else:
                print(f"⚠️ Frontend server not yet ready")
        except Exception as e:
            print(f"⚠️ Unable to automatically open browser: {e}")
            print(f"💡 Please manually visit: {url}")
        
        print(f"📱 Mobile/other devices can access: http://{local_ip}:{port}")
    
    def run(self):
        """Run frontend server"""
        print("🌟 CogBridges Frontend Server")
        print("=" * 60)
        
        # Check dependencies and configuration
        if not self.check_dependencies():
            return False
        
        if not self.check_configuration():
            return False
        
        # Start server
        return self.start_server()
    
    def cleanup(self):
        """Clean up resources"""
        print("\n🧹 Cleaning up frontend server...")
        self.running = False
        
        if self.process:
            try:
                self.process.terminate()
                self.process.wait(timeout=5)
                print("✅ Frontend server stopped")
            except Exception as e:
                print(f"⚠️ Error stopping frontend server: {e}")
    
    def signal_handler(self, signum, frame):
        """Signal handler"""
        print(f"\n📡 Received signal {signum}, stopping frontend server...")
        self.cleanup()
        sys.exit(0)


def main():
    """Main function"""
    server = FrontendServer()
    
    # Register signal handlers
    signal.signal(signal.SIGINT, server.signal_handler)
    signal.signal(signal.SIGTERM, server.signal_handler)
    atexit.register(server.cleanup)
    
    try:
        success = server.run()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 User interrupted, stopping frontend server...")
        server.cleanup()
    except Exception as e:
        print(f"❌ Frontend server startup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 