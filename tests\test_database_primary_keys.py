#!/usr/bin/env python3
"""
测试数据库主键修复的单元测试
"""

import pytest
from sqlalchemy import create_engine, text
from models.database_models import RedditPost, RedditComment
from config import config


class TestDatabasePrimaryKeys:
    """测试数据库主键修复"""
    
    @pytest.fixture(scope="class")
    def engine(self):
        """创建测试数据库引擎"""
        if not config.database_configured or not config.ENABLE_DATABASE:
            pytest.skip("数据库未配置或未启用")
        return create_engine(config.database_url)
    
    @pytest.fixture(scope="class")
    def connection(self, engine):
        """创建数据库连接"""
        with engine.connect() as conn:
            yield conn
    
    def test_reddit_posts_composite_primary_key(self, connection):
        """测试reddit_posts表的复合主键约束"""
        # 检查主键约束
        result = connection.execute(text('''
            SELECT 
                tc.constraint_name,
                tc.constraint_type,
                kcu.column_name,
                kcu.ordinal_position
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            WHERE tc.table_name = 'reddit_posts' 
                AND tc.constraint_type = 'PRIMARY KEY'
            ORDER BY kcu.ordinal_position
        '''))
        
        primary_key_columns = [row[2] for row in result]  # 使用列名而不是位置
        
        # 验证是复合主键
        assert len(primary_key_columns) >= 2, "reddit_posts表应该是复合主键"
        assert 'id' in primary_key_columns, "主键应包含id列"
        assert 'session_id' in primary_key_columns, "主键应包含session_id列"
        
        print(f"✅ reddit_posts表复合主键: {primary_key_columns}")
    
    def test_reddit_comments_composite_primary_key(self, connection):
        """测试reddit_comments表的复合主键约束"""
        # 检查主键约束
        result = connection.execute(text('''
            SELECT 
                tc.constraint_name,
                tc.constraint_type,
                kcu.column_name,
                kcu.ordinal_position
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            WHERE tc.table_name = 'reddit_comments' 
                AND tc.constraint_type = 'PRIMARY KEY'
            ORDER BY kcu.ordinal_position
        '''))
        
        primary_key_columns = [row[2] for row in result]  # 使用列名而不是位置
        
        # 验证是复合主键
        assert len(primary_key_columns) >= 2, "reddit_comments表应该是复合主键"
        assert 'id' in primary_key_columns, "主键应包含id列"
        assert 'session_id' in primary_key_columns, "主键应包含session_id列"
        
        print(f"✅ reddit_comments表复合主键: {primary_key_columns}")
    
    def test_models_primary_key_columns(self):
        """测试SQLAlchemy模型的主键列定义"""
        # 检查RedditPost模型的主键列
        post_table = RedditPost.__table__
        primary_key_columns = [col.name for col in post_table.columns if col.primary_key]
        
        assert len(primary_key_columns) >= 2, "RedditPost应该有复合主键"
        assert 'id' in primary_key_columns, "RedditPost主键应包含id列"
        assert 'session_id' in primary_key_columns, "RedditPost主键应包含session_id列"
        
        print(f"✅ RedditPost模型主键列: {primary_key_columns}")
        
        # 检查RedditComment模型的主键列
        comment_table = RedditComment.__table__
        primary_key_columns = [col.name for col in comment_table.columns if col.primary_key]
        
        assert len(primary_key_columns) >= 2, "RedditComment应该有复合主键"
        assert 'id' in primary_key_columns, "RedditComment主键应包含id列"
        assert 'session_id' in primary_key_columns, "RedditComment主键应包含session_id列"
        
        print(f"✅ RedditComment模型主键列: {primary_key_columns}")
    
    def test_primary_key_constraint_names(self, connection):
        """测试主键约束名称"""
        # 检查reddit_posts表的主键约束名称
        result = connection.execute(text('''
            SELECT constraint_name
            FROM information_schema.table_constraints
            WHERE table_name = 'reddit_posts' 
                AND constraint_type = 'PRIMARY KEY'
        '''))
        
        constraint_names = [row[0] for row in result]
        assert len(constraint_names) > 0, "reddit_posts应该有主键约束"
        assert any('pkey' in name for name in constraint_names), "主键约束名称应包含'pkey'"
        
        print(f"✅ reddit_posts主键约束名称: {constraint_names}")
        
        # 检查reddit_comments表的主键约束名称
        result = connection.execute(text('''
            SELECT constraint_name
            FROM information_schema.table_constraints
            WHERE table_name = 'reddit_comments' 
                AND constraint_type = 'PRIMARY KEY'
        '''))
        
        constraint_names = [row[0] for row in result]
        assert len(constraint_names) > 0, "reddit_comments应该有主键约束"
        assert any('pkey' in name for name in constraint_names), "主键约束名称应包含'pkey'"
        
        print(f"✅ reddit_comments主键约束名称: {constraint_names}")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
