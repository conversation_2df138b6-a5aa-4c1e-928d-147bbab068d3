import React from 'react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import SearchInput from './SearchInput'

describe('SearchInput Component', () => {
  const mockOnSearch = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders search input', () => {
    render(<SearchInput onSearch={mockOnSearch} />)

    const input = screen.getByLabelText('Search questions from Reddit')
    expect(input).toBeInTheDocument()
  })

  it('calls onSearch when form is submitted with valid query', async () => {
    const user = userEvent.setup()
    render(<SearchInput onSearch={mockOnSearch} />)

    const input = screen.getByLabelText('Search questions from Reddit')

    await user.type(input, 'test query')
    await user.keyboard('{Enter}')

    expect(mockOnSearch).toHaveBeenCalledWith('test query')
  })

  it('does not call onSearch with empty query', async () => {
    const user = userEvent.setup()
    render(<SearchInput onSearch={mockOnSearch} />)

    const input = screen.getByLabelText('Search questions from Reddit')

    await user.keyboard('{Enter}')

    expect(mockOnSearch).not.toHaveBeenCalled()
  })

  it('trims whitespace from query', async () => {
    const user = userEvent.setup()
    render(<SearchInput onSearch={mockOnSearch} />)

    const input = screen.getByLabelText('Search questions from Reddit')

    await user.type(input, '  test query  ')
    await user.keyboard('{Enter}')

    expect(mockOnSearch).toHaveBeenCalledWith('test query')
  })

  it('shows clear button when there is text', async () => {
    const user = userEvent.setup()
    render(<SearchInput onSearch={mockOnSearch} />)

    const input = screen.getByLabelText('Search questions from Reddit')

    await user.type(input, 'test')

    const clearButton = screen.getByRole('button', { name: /clear/i })
    expect(clearButton).toBeInTheDocument()
  })

  it('clears input when clear button is clicked', async () => {
    const user = userEvent.setup()
    render(<SearchInput onSearch={mockOnSearch} />)

    const input = screen.getByLabelText('Search questions from Reddit')

    await user.type(input, 'test')
    const clearButton = screen.getByRole('button', { name: /clear/i })
    await user.click(clearButton)

    expect(input).toHaveValue('')
  })

  it('uses custom placeholder when provided', () => {
    render(<SearchInput onSearch={mockOnSearch} placeholder="Custom placeholder" />)

    const input = screen.getByLabelText('Search questions from Reddit')
    expect(input).toHaveAttribute('placeholder', 'Custom placeholder')
  })
})
