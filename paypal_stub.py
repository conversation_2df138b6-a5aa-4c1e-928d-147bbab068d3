# Minimal PayPal stub for tests
# Provides just enough structure for unit tests to patch:
# - paypalrestsdk.configure
# - paypalrestsdk.Payment.create
# - paypalrestsdk.Payment.find
# - paypalrestsdk.WebhookEvent.verify

class _Payment:
    def __init__(self, data=None):
        self.id = None
        self.state = None
        self.links = []
        self.payer = {}
        self.transactions = []
        self.error = None
        if data:
            self.__dict__.update(data)
    
    @classmethod
    def create(cls, payment_data):  # pragma: no cover - stub only
        raise NotImplementedError("PayPal stub: Payment.create not implemented")
    
    @classmethod
    def find(cls, payment_id):  # pragma: no cover - stub only
        raise NotImplementedError("PayPal stub: Payment.find not implemented")
    
    def execute(self, execution_data):  # pragma: no cover - stub only
        raise NotImplementedError("PayPal stub: Payment.execute not implemented")

class _WebhookEvent:
    @staticmethod
    def verify(transmission_id, cert_id, auth_algo, transmission_sig, webhook_id, event_body):  # pragma: no cover - stub only
        raise NotImplementedError("PayPal stub: WebhookEvent.verify not implemented")

# Configuration function
def configure(config_dict):  # pragma: no cover - stub only
    pass

# Export classes and functions
Payment = _Payment
WebhookEvent = _WebhookEvent

__all__ = [
    "Payment",
    "WebhookEvent", 
    "configure",
]
