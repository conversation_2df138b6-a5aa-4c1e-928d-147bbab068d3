"""
CogBridges Analytics Blueprint
提供匿名用户使用分析和转化分析功能
"""

from flask import Blueprint, jsonify, request
from utils.logger_utils import get_logger
from .app import get_cogbridges_service

logger = get_logger(__name__)

bp_analytics = Blueprint("analytics", __name__)


@bp_analytics.route('/api/analytics/visit', methods=['POST'])
def record_site_visit():
    """记录站点访问（用于访问->搜索->注册漏斗）。"""
    try:
        service = get_cogbridges_service()
        if not service or not service.data_service or not service.data_service.storage_service:
            return jsonify({"success": False, "error": "Data service not available"}), 503

        payload = {}
        try:
            payload = request.get_json(silent=True) or {}
        except Exception:
            payload = {}

        # 获取匿名 cookie
        anon_cookie = payload.get('anonymous_cookie_id') or request.cookies.get('cogbridges_anon_id') or request.cookies.get('anon_user_id')
        # 获取客户端 IP（优先 X-Forwarded-For）
        ip_addr = (request.headers.get('X-Forwarded-For') or request.remote_addr or '').split(',')[0].strip()
        user_agent = request.headers.get('User-Agent')
        referrer = request.headers.get('Referer') or request.headers.get('Referrer') or payload.get('referrer')
        landing_path = payload.get('landing_path') or request.args.get('path')
        utm_source = payload.get('utm_source') or request.args.get('utm_source')
        utm_medium = payload.get('utm_medium') or request.args.get('utm_medium')
        utm_campaign = payload.get('utm_campaign') or request.args.get('utm_campaign')

        ok = service.data_service.storage_service.save_site_visit(
            anonymous_cookie_id=anon_cookie,
            ip_address=ip_addr,
            user_agent=user_agent,
            referrer=referrer,
            landing_path=landing_path,
            utm_source=utm_source,
            utm_medium=utm_medium,
            utm_campaign=utm_campaign,
        )
        if not ok:
            return jsonify({"success": False, "error": "Failed to save visit"}), 500
        return jsonify({"success": True})
    except Exception as e:
        logger.error(f"Failed to record site visit: {e}")
        return jsonify({"success": False, "error": f"Internal server error: {str(e)}"}), 500

@bp_analytics.route('/api/analytics/anonymous', methods=['GET'])
def get_anonymous_analytics():
    """获取匿名用户使用分析数据"""
    try:
        service = get_cogbridges_service()
        if not service or not service.data_service or not service.data_service.storage_service:
            return jsonify({"success": False, "error": "Data service not available"}), 503

        # 获取查询参数
        days = request.args.get('days', 30, type=int)
        limit = request.args.get('limit', 100, type=int)
        
        if days <= 0 or days > 365:
            return jsonify({"success": False, "error": "Days must be between 1 and 365"}), 400
        
        if limit <= 0 or limit > 1000:
            return jsonify({"success": False, "error": "Limit must be between 1 and 1000"}), 400

        try:
            with service.data_service.storage_service.get_session() as db_session:
                from models.database_models import SearchSession
                from sqlalchemy import func, desc
                from datetime import datetime, timedelta
                
                # 计算时间范围
                cutoff_date = datetime.now() - timedelta(days=days)
                
                # 查询匿名用户会话
                anonymous_sessions = db_session.query(SearchSession).filter(
                    SearchSession.is_anonymous == True,
                    SearchSession.created_at >= cutoff_date
                ).order_by(desc(SearchSession.created_at)).limit(limit).all()
                
                # 统计信息
                total_anonymous_searches = db_session.query(func.count(SearchSession.id)).filter(
                    SearchSession.is_anonymous == True,
                    SearchSession.created_at >= cutoff_date
                ).scalar() or 0
                
                successful_searches = db_session.query(func.count(SearchSession.id)).filter(
                    SearchSession.is_anonymous == True,
                    SearchSession.success == True,
                    SearchSession.created_at >= cutoff_date
                ).scalar() or 0
                
                failed_searches = db_session.query(func.count(SearchSession.id)).filter(
                    SearchSession.is_anonymous == True,
                    SearchSession.success == False,
                    SearchSession.created_at >= cutoff_date
                ).scalar() or 0
                
                # 按日期分组统计
                daily_stats = db_session.query(
                    func.date(SearchSession.created_at).label('date'),
                    func.count(SearchSession.id).label('count'),
                    func.avg(SearchSession.total_time_ms).label('avg_time'),
                    func.avg(SearchSession.reddit_posts_count).label('avg_posts'),
                    func.avg(SearchSession.reddit_comments_count).label('avg_comments')
                ).filter(
                    SearchSession.is_anonymous == True,
                    SearchSession.created_at >= cutoff_date
                ).group_by(func.date(SearchSession.created_at)).order_by(desc(func.date(SearchSession.created_at))).all()
                
                # 热门查询
                popular_queries = db_session.query(
                    SearchSession.query,
                    func.count(SearchSession.id).label('count')
                ).filter(
                    SearchSession.is_anonymous == True,
                    SearchSession.created_at >= cutoff_date
                ).group_by(SearchSession.query).order_by(desc(func.count(SearchSession.id))).limit(20).all()
                
                # 构建响应数据
                analytics_data = {
                    "success": True,
                    "period_days": days,
                    "summary": {
                        "total_searches": total_anonymous_searches,
                        "successful_searches": successful_searches,
                        "failed_searches": failed_searches,
                        "success_rate": (successful_searches / total_anonymous_searches * 100) if total_anonymous_searches > 0 else 0
                    },
                    "daily_stats": [
                        {
                            "date": stat.date.isoformat() if stat.date else None,
                            "search_count": stat.count,
                            "avg_time_ms": float(stat.avg_time) if stat.avg_time else 0,
                            "avg_posts": float(stat.avg_posts) if stat.avg_posts else 0,
                            "avg_comments": float(stat.avg_comments) if stat.avg_comments else 0
                        }
                        for stat in daily_stats
                    ],
                    "popular_queries": [
                        {
                            "query": stat.query,
                            "count": stat.count
                        }
                        for stat in popular_queries
                    ],
                    "recent_sessions": [
                        {
                            "session_id": session.id,
                            "query": session.query,
                            "created_at": session.created_at.isoformat() if session.created_at else None,
                            "success": session.success,
                            "total_time_ms": session.total_time_ms,
                            "reddit_posts_count": session.reddit_posts_count,
                            "reddit_comments_count": session.reddit_comments_count,
                            "anonymous_cookie_id": session.anonymous_cookie_id
                        }
                        for session in anonymous_sessions
                    ]
                }
                
                return jsonify(analytics_data)
                
        except Exception as e:
            logger.error(f"Failed to query anonymous analytics: {e}")
            return jsonify({"success": False, "error": f"Database query failed: {str(e)}"}), 500
            
    except Exception as e:
        logger.error(f"Failed to get anonymous analytics: {e}")
        return jsonify({"success": False, "error": f"Internal server error: {str(e)}"}), 500


@bp_analytics.route('/api/analytics/conversion', methods=['GET'])
def get_conversion_analytics():
    """获取匿名用户转化分析数据"""
    try:
        service = get_cogbridges_service()
        if not service or not service.data_service or not service.data_service.storage_service:
            return jsonify({"success": False, "error": "Data service not available"}), 503

        # 获取查询参数
        days = request.args.get('days', 30, type=int)
        
        if days <= 0 or days > 365:
            return jsonify({"success": False, "error": "Days must be between 1 and 365"}), 400

        try:
            with service.data_service.storage_service.get_session() as db_session:
                from models.database_models import SearchSession, UserAccount, SiteVisit
                from sqlalchemy import func, desc
                from datetime import datetime, timedelta
                
                # 计算时间范围
                cutoff_date = datetime.now() - timedelta(days=days)
                
                # 统计匿名用户搜索次数分布
                # 分布：每个匿名cookie一共搜索了多少次 -> 有多少用户属于该次数
                sub_counts = db_session.query(
                    SearchSession.anonymous_cookie_id.label('cookie'),
                    func.count(SearchSession.id).label('searches')
                ).filter(
                    SearchSession.is_anonymous == True,
                    SearchSession.anonymous_cookie_id.isnot(None),
                    SearchSession.created_at >= cutoff_date
                ).group_by(SearchSession.anonymous_cookie_id).subquery()

                search_count_distribution = db_session.query(
                    sub_counts.c.searches.label('search_count'),
                    func.count(sub_counts.c.cookie).label('user_count')
                ).group_by(sub_counts.c.searches).order_by(desc(sub_counts.c.searches)).all()
                
                # 统计新注册用户（在匿名搜索后注册的用户）
                # 这里需要根据时间逻辑来推断，暂时返回基础统计
                new_users_after_anonymous = db_session.query(func.count(UserAccount.id)).filter(
                    UserAccount.created_at >= cutoff_date
                ).scalar() or 0
                
                # 统计访问数据（UV 按匿名 cookie 去重）
                total_visits = db_session.query(func.count(SiteVisit.id)).filter(
                    SiteVisit.created_at >= cutoff_date
                ).scalar() or 0
                unique_visitors = db_session.query(func.count(func.distinct(SiteVisit.anonymous_cookie_id))).filter(
                    SiteVisit.created_at >= cutoff_date,
                    SiteVisit.anonymous_cookie_id.isnot(None)
                ).scalar() or 0

                # 构建转化分析数据
                conversion_data = {
                    "success": True,
                    "period_days": days,
                    "visits": {
                        "total_visits": total_visits,
                        "unique_visitors": unique_visitors,
                    },
                    "anonymous_usage": {
                        "total_anonymous_searches": db_session.query(func.count(SearchSession.id)).filter(
                            SearchSession.is_anonymous == True,
                            SearchSession.created_at >= cutoff_date
                        ).scalar() or 0,
                        "unique_anonymous_users": db_session.query(func.count(func.distinct(SearchSession.anonymous_cookie_id))).filter(
                            SearchSession.is_anonymous == True,
                            SearchSession.created_at >= cutoff_date
                        ).scalar() or 0
                    },
                    "search_count_distribution": [
                        {
                            "searches_per_user": stat.search_count,
                            "user_count": stat.user_count
                        }
                        for stat in search_count_distribution
                    ],
                    "conversion_metrics": {
                        "new_registered_users": new_users_after_anonymous,
                        # 访问->匿名搜索 转化率（粗略）：unique_anonymous_users / unique_visitors
                        "visit_to_search_rate": (
                            float(
                                (db_session.query(func.count(func.distinct(SearchSession.anonymous_cookie_id))).filter(
                                    SearchSession.is_anonymous == True,
                                    SearchSession.created_at >= cutoff_date
                                ).scalar() or 0)
                            ) / float(unique_visitors) * 100.0
                        ) if unique_visitors > 0 else 0.0,
                    },
                    "recommendations": [
                        "Monitor anonymous user search patterns to identify conversion opportunities",
                        "Analyze failed searches to improve user experience",
                        "Track popular queries to optimize content recommendations",
                        "Consider implementing progressive disclosure for anonymous users"
                    ]
                }
                
                return jsonify(conversion_data)
                
        except Exception as e:
            logger.error(f"Failed to query conversion analytics: {e}")
            return jsonify({"success": False, "error": f"Database query failed: {str(e)}"}), 500
            
    except Exception as e:
        logger.error(f"Failed to get conversion analytics: {e}")
        return jsonify({"success": False, "error": f"Internal server error: {str(e)}"}), 500


@bp_analytics.route('/api/analytics/retention', methods=['GET'])
def get_retention_analytics():
    """获取用户留存分析数据"""
    try:
        service = get_cogbridges_service()
        if not service or not service.data_service or not service.data_service.storage_service:
            return jsonify({"success": False, "error": "Data service not available"}), 503

        # 获取查询参数
        days = request.args.get('days', 30, type=int)
        
        if days <= 0 or days > 365:
            return jsonify({"success": False, "error": "Days must be between 1 and 365"}), 400

        try:
            with service.data_service.storage_service.get_session() as db_session:
                from models.database_models import SearchSession, UserAccount
                from sqlalchemy import func, desc
                from datetime import datetime, timedelta
                
                # 计算时间范围
                cutoff_date = datetime.now() - timedelta(days=days)
                
                # 统计匿名用户留存情况（先按cookie聚合再求均值）
                anon_user_counts = db_session.query(
                    SearchSession.anonymous_cookie_id.label('cookie'),
                    func.count(SearchSession.id).label('cnt')
                ).filter(
                    SearchSession.is_anonymous == True,
                    SearchSession.anonymous_cookie_id.isnot(None),
                    SearchSession.created_at >= cutoff_date
                ).group_by(SearchSession.anonymous_cookie_id).subquery()
                
                # 统计注册用户留存情况（先按用户聚合再求均值）
                reg_user_counts = db_session.query(
                    SearchSession.owner_user_id.label('uid'),
                    func.count(SearchSession.id).label('cnt')
                ).filter(
                    SearchSession.is_anonymous == False,
                    SearchSession.owner_user_id.isnot(None),
                    SearchSession.created_at >= cutoff_date
                ).group_by(SearchSession.owner_user_id).subquery()
                
                # 构建留存分析数据
                retention_data = {
                    "success": True,
                    "period_days": days,
                    "anonymous_users": {
                        "total_searches": db_session.query(func.count(SearchSession.id)).filter(
                            SearchSession.is_anonymous == True,
                            SearchSession.created_at >= cutoff_date
                        ).scalar() or 0,
                        "unique_users": db_session.query(func.count(func.distinct(SearchSession.anonymous_cookie_id))).filter(
                            SearchSession.is_anonymous == True,
                            SearchSession.created_at >= cutoff_date
                        ).scalar() or 0,
                        "avg_searches_per_user": db_session.query(func.avg(anon_user_counts.c.cnt)).scalar() or 0
                    },
                    "registered_users": {
                        "total_searches": db_session.query(func.count(SearchSession.id)).filter(
                            SearchSession.is_anonymous == False,
                            SearchSession.owner_user_id.isnot(None),
                            SearchSession.created_at >= cutoff_date
                        ).scalar() or 0,
                        "unique_users": db_session.query(func.count(func.distinct(SearchSession.owner_user_id))).filter(
                            SearchSession.is_anonymous == False,
                            SearchSession.owner_user_id.isnot(None),
                            SearchSession.created_at >= cutoff_date
                        ).scalar() or 0,
                        "avg_searches_per_user": db_session.query(func.avg(reg_user_counts.c.cnt)).scalar() or 0
                    },
                    "insights": [
                        "Compare anonymous vs registered user engagement patterns",
                        "Identify potential conversion opportunities from high-engagement anonymous users",
                        "Analyze search success rates to improve user experience",
                        "Monitor user behavior changes over time"
                    ]
                }
                
                return jsonify(retention_data)
                
        except Exception as e:
            logger.error(f"Failed to query retention analytics: {e}")
            return jsonify({"success": False, "error": f"Database query failed: {str(e)}"}), 500
            
    except Exception as e:
        logger.error(f"Failed to get retention analytics: {e}")
        return jsonify({"success": False, "error": f"Internal server error: {str(e)}"}), 500
