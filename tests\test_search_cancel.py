import time
import asyncio

import pytest


def test_cancel_search_marks_cancelled_and_skips_llm(monkeypatch):
    from api.app import create_app, cogbridges_service, search_status, active_search_tasks

    # Clean up global state
    search_status.clear()
    active_search_tasks.clear()

    # Flag: whether LLM was called
    llm_called = {"called": False}

    # Fake step1: immediately return a data structure containing 1 comment
    async def fake_step1(query: str):
        return {
            "posts": [
                {
                    "success": True,
                    "post": {
                        "id": "p1",
                        "title": "",
                        "selftext": "",
                        "url": "https://reddit.com/r/a/comments/abc/title",
                        "permalink": "/r/a/comments/abc/title",
                        "author": "",
                        "subreddit": "r/a",
                        "score": 0,
                        "num_comments": 0,
                        "created_utc": None,
                    },
                    "comments": [
                        {
                            "id": "c1",
                            "body": "hello",
                            "author": "u1",
                            "score": 1,
                            "created_utc": None,
                            "permalink": "https://reddit.com/r/a/comments/abc/comment/c1",
                        }
                    ],
                    "commenters": ["u1"],
                }
            ],
            "google_results": [],
            "search_time": 0.01,
            "citations": [],
        }

    # Fake step2: wait until cancel event is set, then return non-empty history to trigger pre-check for step3
    async def fake_step2(posts_data, cancel_event=None):
        # Simulate processing (give test side time to call cancel interface)
        for _ in range(20):
            if cancel_event is not None and getattr(cancel_event, "is_set", lambda: False)():
                break
            await asyncio.sleep(0.05)
        # Return non-empty history to ensure subsequent path checks cancel flag and skips LLM
        return {
            "history": {"u1": {"_metadata": {"subreddits": ["r/a"]}}},
            "processing_time": 0.2,
            "selected_usernames": ["u1"],
        }

    # Fake step3: mark flag if called
    async def fake_step3(result, cancel_event=None):
        llm_called["called"] = True
        await asyncio.sleep(0.01)
        return {"success": True, "analysis_summary": {}}

    # Apply stubs
    monkeypatch.setattr(cogbridges_service, "_step1_grok_reddit_search", fake_step1)
    monkeypatch.setattr(cogbridges_service, "_step2_get_commenters_history", fake_step2)
    monkeypatch.setattr(cogbridges_service, "_step3_llm_analysis", fake_step3)
    # Turn on LLM service switch (to cover pre-check before step3)
    try:
        cogbridges_service.llm_service.configured = True
    except Exception:
        pass

    app = create_app()
    client = app.test_client()

    # 1) Initiate search
    resp = client.post("/api/search", json={"query": "test query", "enhanced": True, "llm_analysis": {"analysis_summary": {}}})
    assert resp.status_code == 200
    payload = resp.get_json()
    assert payload.get("success") is True
    session_id = payload.get("session_id")
    assert session_id

    # 2) Immediately cancel
    time.sleep(0.2)
    cancel_resp = client.post(f"/api/search/cancel/{session_id}")
    assert cancel_resp.status_code == 200

    # 3) Poll until status is cancelled
    final_status = None
    for _ in range(30):  # max ~6s
        prog = client.get(f"/api/search/progress/{session_id}")
        if prog.status_code == 200:
            data = prog.get_json()
            final_status = data.get("status")
            if final_status in ("cancelled", "completed", "error"):
                break
        time.sleep(0.2)

    assert final_status == "cancelled"
    assert llm_called["called"] is False  # Cancel should take effect before entering LLM

    # 4) Background task registration cleanup
    for _ in range(10):
        if session_id not in active_search_tasks:
            break
        time.sleep(0.1)
    assert session_id not in active_search_tasks


