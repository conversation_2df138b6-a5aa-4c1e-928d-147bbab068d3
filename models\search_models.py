"""
CogBridges Search - 搜索相关数据模型
定义搜索查询、搜索结果等数据结构
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from datetime import datetime
import json


@dataclass
class SearchQuery:
    """搜索查询模型"""
    query: str
    timestamp: datetime = field(default_factory=datetime.now)
    search_type: str = "reddit"  # 搜索类型：reddit, general
    max_results: int = 5
    site_filter: str = "site:reddit.com"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "query": self.query,
            "timestamp": self.timestamp.isoformat(),
            "search_type": self.search_type,
            "max_results": self.max_results,
            "site_filter": self.site_filter
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchQuery':
        """从字典创建实例"""
        data = data.copy()
        if 'timestamp' in data:
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)





@dataclass
class SearchResult:
    """完整搜索结果"""
    query: SearchQuery
    results: List[Dict[str, Any]] = field(default_factory=list)
    total_results: int = 0
    search_time: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    success: bool = True
    error_message: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "query": self.query.to_dict(),
            "results": self.results,
            "total_results": self.total_results,
            "search_time": self.search_time,
            "timestamp": self.timestamp.isoformat(),
            "success": self.success,
            "error_message": self.error_message
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SearchResult':
        """从字典创建实例"""
        data = data.copy()
        
        # 转换嵌套对象
        if 'query' in data:
            data['query'] = SearchQuery.from_dict(data['query'])
        

        
        if 'timestamp' in data:
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        
        return cls(**data)
    
    def get_reddit_results(self) -> List[Dict[str, Any]]:
        """获取Reddit相关的搜索结果"""
        return [result for result in self.results if "reddit.com" in result.get("url", "").lower()]
    
    def get_post_urls(self) -> List[str]:
        """获取Reddit帖子URL列表"""
        post_urls = []
        for result in self.get_reddit_results():
            url = result.get("url", "")
            if "/comments/" in url or "/r/" in url:
                post_urls.append(url)
        return post_urls
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取搜索结果统计信息"""
        reddit_results = self.get_reddit_results()
        post_urls = self.get_post_urls()
        
        # 统计子版块分布
        subreddits = {}
        for result in reddit_results:
            url = result.get("url", "")
            if "/r/" in url:
                import re
                match = re.search(r'/r/([^/]+)', url)
                if match:
                    subreddit = match.group(1)
                    subreddits[subreddit] = subreddits.get(subreddit, 0) + 1
        
        return {
            "total_results": len(self.results),
            "reddit_results": len(reddit_results),
            "post_urls": len(post_urls),
            "subreddit_distribution": subreddits,
            "search_time": self.search_time,
            "success": self.success
        }
