#!/usr/bin/env python3
"""
CogBridges Search - 数据库初始化脚本
用于创建数据库表和迁移现有JSON数据
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import argparse
from typing import Dict, Any
from config import config
from services.database_service import DatabaseService
from services.data_service import DataService
from utils.logger_utils import get_logger
from sqlalchemy import inspect
from sqlalchemy.schema import CreateTable


def create_tables(database_service: DatabaseService) -> bool:
    """创建数据库表"""
    logger = get_logger(__name__)
    
    try:
        logger.info("开始创建数据库表...")
        database_service.create_tables()
        logger.info("✅ 数据库表创建成功")
        return True
    except Exception as e:
        logger.error(f"❌ 创建数据库表失败: {e}")
        return False


def migrate_json_data(data_service: DataService, session_id: str = None) -> Dict[str, Any]:
    """迁移JSON数据到数据库"""
    logger = get_logger(__name__)
    
    try:
        logger.info("开始迁移JSON数据到数据库...")
        results = data_service.migrate_json_to_database(session_id)
        
        if "error" in results:
            logger.error(f"❌ 迁移失败: {results['error']}")
        else:
            logger.info(f"✅ 迁移完成:")
            logger.info(f"   总文件数: {results['total_files']}")
            logger.info(f"   成功迁移: {results['migrated']}")
            logger.info(f"   跳过: {results['skipped']}")
            logger.info(f"   失败: {results['failed']}")
            
            if results['errors']:
                logger.warning("迁移错误:")
                for error in results['errors']:
                    logger.warning(f"   - {error}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ 迁移过程失败: {e}")
        return {"error": str(e)}


def check_database_status() -> bool:
    """检查数据库状态"""
    logger = get_logger(__name__)
    database_service = None
    
    logger.info("检查数据库配置...")
    
    # 检查配置
    if not config.ENABLE_DATABASE:
        logger.warning("⚠️ 数据库未启用 (ENABLE_DATABASE=False)")
        return False
    
    if not config.database_configured:
        logger.error("数据库未配置")
        logger.error("请设置以下环境变量:")
        logger.error("  - DATABASE_URL (Render部署)")
        logger.error("  或者:")
        logger.error("  - DB_HOST, DB_NAME, DB_USER, DB_PASSWORD (本地开发)")
        return False
    
    logger.info("✅ 数据库配置检查通过")
    
    # 测试连接
    try:
        logger.info("测试数据库连接...")
        database_service = DatabaseService()
        logger.info("✅ 数据库连接成功")
        return True
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False
    finally:
        if database_service:
            database_service.cleanup()


def _compile_sql_type(column, engine=None) -> str:
    """将 SQLAlchemy 列类型编译为当前方言的 SQL 文本（用于提示手工迁移）。"""
    try:
        eng = engine
        if eng is not None and getattr(eng, "dialect", None) is not None:
            return eng.dialect.type_compiler.process(column.type)
    except Exception:
        # 回退：使用类型类名
        try:
            return getattr(column.type, "__class__", type(column.type)).__name__
        except Exception:
            return "TEXT"


def validate_schema(database_service: DatabaseService) -> Dict[str, Any]:
    """校验数据库实际表/列是否覆盖模型定义（仅检测缺失表/列）。"""
    logger = get_logger(__name__)
    results: Dict[str, Any] = {
        "missing_tables": [],
        "missing_columns": {},
        "has_drift": False,
        "suggested_sql": [],
    }

    try:
        from models.database_models import Base
        engine = database_service.engine
        inspector = inspect(engine)

        # 现有表
        existing_tables = set(inspector.get_table_names())

        # 先记录所有缺失表
        for table_name, table in Base.metadata.tables.items():
            if table_name not in existing_tables:
                results["missing_tables"].append(table_name)
                continue

            # 现有列
            existing_cols = {c["name"] for c in inspector.get_columns(table_name)}
            model_cols = {c.name for c in table.columns}

            missing = sorted(list(model_cols - existing_cols))
            if missing:
                results["missing_columns"][table_name] = missing
                # 生成建议 SQL（仅供参考）
                for col_name in missing:
                    col = table.columns[col_name]
                    col_type_sql = _compile_sql_type(col, engine)
                    nullable_sql = " NOT NULL" if (getattr(col, "nullable", True) is False) else ""
                    results["suggested_sql"].append(
                        f"ALTER TABLE {table_name} ADD COLUMN IF NOT EXISTS {col_name} {col_type_sql}{nullable_sql};"
                    )

        # 为缺失表生成 CREATE TABLE 语句，并按外键依赖拓扑排序
        if results["missing_tables"]:
            # 构建依赖图：表 -> 其依赖的（缺失）表集合
            missing_set = set(results["missing_tables"])
            dependency_graph = {}
            for tname in missing_set:
                table = Base.metadata.tables[tname]
                deps = set()
                for fk in getattr(table, 'foreign_keys', set()):
                    ref_table = fk.column.table.name
                    if ref_table in missing_set:
                        deps.add(ref_table)
                dependency_graph[tname] = deps

            ordered: list = []
            pending = set(missing_set)
            # 简单拓扑排序
            while pending:
                progressed = False
                for tname in list(pending):
                    if not (dependency_graph[tname] & pending):
                        ordered.append(tname)
                        pending.remove(tname)
                        progressed = True
                if not progressed:
                    # 存在环或未知依赖，按任意顺序追加
                    ordered.extend(list(pending))
                    pending.clear()

            for tname in ordered:
                table = Base.metadata.tables[tname]
                try:
                    ddl = str(CreateTable(table).compile(engine))
                except Exception:
                    ddl = str(CreateTable(table))
                # 尽量加上 IF NOT EXISTS，提升幂等性（PostgreSQL支持）
                ddl_if = ddl.replace("CREATE TABLE ", "CREATE TABLE IF NOT EXISTS ")
                results["suggested_sql"].insert(0, ddl_if)

        results["has_drift"] = bool(results["missing_tables"] or results["missing_columns"])

        if results["has_drift"]:
            logger.error("❌ 检测到数据库 Schema 与模型不一致（缺失表/列）")
            if results["missing_tables"]:
                logger.error(f"  缺失表: {', '.join(results['missing_tables'])}")
            if results["missing_columns"]:
                for tn, cols in results["missing_columns"].items():
                    logger.error(f"  {tn} 缺失列: {', '.join(cols)}")

            # 提示手工迁移方案
            logger.error("\n👉 手工迁移步骤（生产环境建议）：")
            if results["suggested_sql"]:
                logger.error("  1) 在存储服务中执行以下 SQL（按需调整默认值/约束）：")
                for line in results["suggested_sql"]:
                    logger.error(f"     {line}")
            else:
                logger.error("  1) 为缺失的表/列手工执行 CREATE TABLE / ALTER TABLE 语句")
            logger.error("  2) 重新部署或再次执行 pre-deploy 校验")

            logger.error("\n💡 开发环境的快捷方案（会清空数据）：")
            logger.error("  python -c \"from services.database_service import DatabaseService as D; d=D(); d.drop_tables(); d.create_tables()\"")
            logger.error("  python scripts/init_database.py --migrate --force")

            # 生成可复制运行的 Python 迁移脚本
            try:
                script_text = build_python_migration_script(results["suggested_sql"])
                logger.error("\n===== 复制以下 Python 脚本到本地运行（将执行幂等迁移） =====")
                for line in script_text.splitlines():
                    logger.error(line)
                logger.error("===== 脚本结束 =====\n")
            except Exception as e:
                logger.error(f"生成 Python 迁移脚本失败: {e}")

        else:
            logger.info("✅ 数据库 Schema 与模型一致（未发现缺失表/列）")

        return results

    except Exception as e:
        logger.error(f"Schema 校验失败: {e}")
        return {"error": str(e), "has_drift": True}


def build_python_migration_script(sql_statements: list) -> str:
    """
    生成可复制的本地迁移 Python 脚本文本：
    - 读取环境变量 DATABASE_URL
    - 使用 psycopg2 连接并顺序执行 SQL（忽略已存在错误）
    - 打印每条 SQL 的执行结果
    """
    lines = [
        "#!/usr/bin/env python3",
        "import os",
        "import sys",
        "import psycopg2",
        "",
        "def main():",
        "    db_url = os.getenv('DATABASE_URL', '').strip()",
        "    if not db_url:",
        "        print('ERROR: 请先在环境变量中设置 DATABASE_URL')",
        "        sys.exit(1)",
        "",
        "    print('Connecting to database...')",
        "    conn = psycopg2.connect(db_url)",
        "    conn.autocommit = True",
        "    cur = conn.cursor()",
        "",
        "    statements = [",
    ]

    for s in sql_statements:
        safe = s.strip().rstrip(';') + ';'
        lines.append(f"        \"\"\"{safe}\"\"\",")

    lines += [
        "    ]",
        "",
        "    for idx, sql in enumerate(statements, 1):",
        "        print(f'[{idx}/{len(statements)}] Executing: {sql.strip()}')",
        "        try:",
        "            cur.execute(sql)",
        "            print('  -> OK')",
        "        except Exception as e:",
        "            print(f'  -> WARN: {e}')",
        "",
        "    cur.close()",
        "    conn.close()",
        "    print('All done.')",
        "",
        "if __name__ == '__main__':",
        "    main()",
    ]

    return "\n".join(lines)


def show_statistics():
    """显示存储统计信息"""
    logger = get_logger(__name__)
    
    try:
        data_service = DataService()
        stats = data_service.get_storage_statistics()
        
        logger.info("📊 存储统计信息:")
        logger.info(f"   存储类型: {stats.get('storage_type', 'unknown')}")
        logger.info(f"   数据库启用: {stats.get('database_enabled', False)}")
        logger.info(f"   JSON备份启用: {stats.get('json_backup_enabled', True)}")
        
        # 数据库统计
        if 'database' in stats:
            db_stats = stats['database']
            if 'error' not in db_stats:
                logger.info("   数据库统计:")
                logger.info(f"     会话数: {db_stats.get('sessions_count', 0)}")
                logger.info(f"     Google结果数: {db_stats.get('google_results_count', 0)}")
                logger.info(f"     Reddit帖子数: {db_stats.get('reddit_posts_count', 0)}")
                logger.info(f"     Reddit评论数: {db_stats.get('reddit_comments_count', 0)}")
                logger.info(f"     用户历史数: {db_stats.get('user_histories_count', 0)}")
            else:
                logger.warning(f"   数据库统计错误: {db_stats['error']}")
        
        # JSON文件统计
        if 'json_files' in stats:
            json_stats = stats['json_files']
            if 'error' not in json_stats:
                logger.info("   JSON文件统计:")
                logger.info(f"     总文件数: {json_stats.get('total_files', 0)}")
                logger.info(f"     会话文件数: {json_stats.get('session_count', 0)}")
                logger.info(f"     总大小: {json_stats.get('total_size_mb', 0)} MB")
            else:
                logger.warning(f"   JSON文件统计错误: {json_stats['error']}")
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='CogBridges Search - 数据库初始化工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  # 检查数据库连接
  python scripts/init_database.py --check
  
  # 创建数据库表
  python scripts/init_database.py --create-tables
  
  # 迁移所有数据
  python scripts/init_database.py --migrate
  
  # 迁移特定会话
  python scripts/init_database.py --migrate-session search_001
  
  # 查看统计信息  
  python scripts/init_database.py --stats
        """
    )
    
    parser.add_argument('--check', action='store_true',
                       help='检查数据库连接和配置')
    parser.add_argument('--create-tables', action='store_true',
                       help='创建数据库表')
    parser.add_argument('--validate-schema', action='store_true',
                       help='校验数据库表结构是否完整并输出漂移报告')
    parser.add_argument('--fail-on-drift', action='store_true',
                       help='当检测到 Schema 漂移时以非零状态退出，用于自动化部署')
    parser.add_argument('--migrate', action='store_true',
                       help='迁移JSON数据到数据库')
    parser.add_argument('--migrate-session', type=str,
                       help='迁移特定会话的数据')
    parser.add_argument('--stats', action='store_true',
                       help='显示数据库统计信息')
    parser.add_argument('--reset-all', action='store_true',
                       help='完全重置数据库：删除所有表，重新创建表和索引，然后校验')
    parser.add_argument('--force', action='store_true',
                       help='跳过确认提示')
    
    args = parser.parse_args()

    # Initialize logger
    logger = get_logger(__name__)

    logger.info("CogBridges Search - 数据库初始化工具")
    logger.info("=" * 50)
    
    # Initialize database_service to None
    database_service = None
    
    try:
        # 检查数据库连接
        if args.check or not any([args.create_tables, args.migrate, args.migrate_session, args.stats, args.validate_schema]):
            logger.info("检查数据库配置...")
            if not config.database_configured:
                logger.error("❌ 数据库未配置")
                logger.error("请设置 DATABASE_URL 环境变量或配置 DB_HOST, DB_NAME, DB_USER")
                sys.exit(1)
            
            logger.info("✅ 数据库配置检查通过")
            
            logger.info("测试数据库连接...")
            database_service = DatabaseService()
            logger.info("✅ 数据库连接成功")
            # Clean up after check
            database_service.cleanup()
            database_service = None
        
        # 创建数据库表
        if args.create_tables:
            if not args.force:
                confirm = input("\n确定要创建数据库表吗？这可能会覆盖现有表结构。(y/N): ")
                if confirm.lower() != 'y':
                    logger.info("操作已取消")
                    return
            
            database_service = DatabaseService()
            if not create_tables(database_service):
                sys.exit(1)
            # Clean up after creating tables
            database_service.cleanup()
            database_service = None
        
        # 校验数据库 Schema
        if args.validate_schema:
            database_service = DatabaseService()
            drift = validate_schema(database_service)
            # Clean up after validation
            database_service.cleanup()
            database_service = None
            
            if drift.get("has_drift") and args.fail_on_drift:
                logger.error("由于检测到 Schema 漂移，本次操作以失败结束以阻止上线。")
                sys.exit(2)

        # 迁移数据
        if args.migrate or args.migrate_session:
            if not args.force:
                session_info = f"会话 {args.migrate_session}" if args.migrate_session else "所有JSON文件"
                confirm = input(f"\n确定要迁移{session_info}到数据库吗？(y/N): ")
                if confirm.lower() != 'y':
                    logger.info("操作已取消")
                    return
            
            data_service = DataService()
            results = migrate_json_data(data_service, args.migrate_session)
            
            # Clean up data service's database connection
            if hasattr(data_service, 'database_service') and data_service.storage_service:
                data_service.storage_service.cleanup()
            
            if "error" in results:
                sys.exit(1)
        
        # 完全重置数据库
        if args.reset_all:
            if not args.force:
                confirm = input("\n⚠️  警告：这将删除所有数据库表和数据！确定要继续吗？(y/N): ")
                if confirm.lower() != 'y':
                    logger.info("操作已取消")
                    return

            logger.info("🔄 开始完全重置数据库...")
            database_service = DatabaseService()

            try:
                # 1. 删除所有表
                logger.info("1️⃣ 删除现有表...")
                database_service.drop_tables()
                logger.info("✅ 表删除完成")

                # 2. 重新创建表
                logger.info("2️⃣ 重新创建表...")
                if not create_tables(database_service):
                    logger.error("❌ 表创建失败")
                    sys.exit(1)
                logger.info("✅ 表创建完成")

                # 3. 校验 Schema
                logger.info("3️⃣ 校验数据库 Schema...")
                drift = validate_schema(database_service)
                if drift.get("has_drift"):
                    logger.error("❌ Schema 校验发现问题")
                    sys.exit(1)
                logger.info("✅ Schema 校验通过")

                logger.info("🎉 数据库完全重置成功！")

            except Exception as e:
                logger.error(f"❌ 重置过程失败: {e}")
                sys.exit(1)
            finally:
                database_service.cleanup()
                database_service = None

        # 显示统计信息
        if args.stats:
            show_statistics()

        logger.info("\n✅ 操作完成")
    
    finally:
        # Ensure cleanup on any exit
        if database_service:
            database_service.cleanup()


if __name__ == "__main__":
    main()
