services:
  - type: web
    name: cogbridges-api
    env: python
    plan: starter
    autoDeploy: true
    buildCommand: pip install -r requirements.txt
    startCommand: gunicorn -w 2 -k gthread --threads 8 -t 600 -b 0.0.0.0:$PORT wsgi:app
    healthCheckPath: /api/health
    envVars:
      - key: ENABLE_DATABASE
        value: "True"
      - key: DEBUG_MODE
        value: "False"
      - key: LOG_LEVEL
        value: "INFO"
      - key: RENDER
        value: "true"
      - key: FLASK_ENV
        value: "production"
      - key: SAVE_DETAILED_LOGS
        value: "False"
      - key: ENABLE_IO_SNAPSHOTS
        value: "False"
      - key: ALLOWED_ORIGINS
        value: "https://cogbridges.com,https://www.cogbridges.com,https://cogbridges-api.onrender.com"
      - key: FRONTEND_URL
        value: "https://cogbridges.com"
      # 创建后在 Web 上填写（Secrets）
      - key: REDDIT_CLIENT_ID
        sync: false
      - key: REDDIT_CLIENT_SECRET
        sync: false
      - key: XAI_API_KEY
        sync: false
      - key: REPLICATE_API_TOKEN
        sync: false

  - type: web
    name: cogbridges-web
    runtime: static
    rootDir: frontend
    buildCommand: npm ci && npm run build
    staticPublishPath: dist
    envVars:
      # 创建后在 Web 上设置为后端 URL（例：https://cogbridges-api.onrender.com）
      - key: VITE_API_URL
        sync: false
