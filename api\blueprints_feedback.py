#!/usr/bin/env python3
"""
Feedback API Blueprint
Handles user feedback submissions
"""

from flask import Blueprint, request, jsonify
from utils.logger_utils import get_logger
from services.email_service import EmailService
from api.tokens import verify_token

logger = get_logger(__name__)

bp_feedback = Blueprint('feedback', __name__, url_prefix='/api')

# Initialize email service
email_service = EmailService()

@bp_feedback.route('/feedback', methods=['POST'])
def send_feedback():
    """Send user feedback email"""
    try:
        # Get JSON data, handling cases where content-type is not JSON
        try:
            data = request.get_json()
        except Exception:
            data = None
            
        if data is None:
            return jsonify({
                'success': False,
                'message': 'Invalid request data'
            }), 400
        
        user_email = data.get('email', '').strip()
        content = data.get('content', '').strip()
        
        # Validate input
        if not user_email or not content:
            return jsonify({
                'success': False,
                'message': 'Email and feedback content are required'
            }), 400
        
        # Basic email validation
        import re
        if not re.match(r'^[^\s@]+@[^\s@]+\.[^\s@]+$', user_email):
            return jsonify({
                'success': False,
                'message': 'Invalid email address'
            }), 400
        
        # Check if user is authenticated (optional - for additional context)
        auth_header = request.headers.get('Authorization')
        authenticated_user = None
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ', 1)[1]
            try:
                user_data = verify_token(token)
                if user_data and 'email' in user_data:
                    authenticated_user = user_data['email']
            except:
                pass  # Authentication is optional for feedback
        
        # Prepare email content
        subject = f"CogBridges Feedback from {user_email}"
        
        # Build email body (plain text)
        email_body = f"""New Feedback Received

From: {user_email}
{f'Authenticated User: {authenticated_user}' if authenticated_user else ''}
Date: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}

Feedback Content:
{content}

---
This feedback was sent from the CogBridges platform."""
        
        # Send email to founder
        success = email_service.send_email(
            to_email='<EMAIL>',
            subject=subject,
            body=email_body
        )
        
        if success:
            logger.info(f"Feedback sent successfully from {user_email}")
            return jsonify({
                'success': True,
                'message': 'Feedback sent successfully'
            }), 200
        else:
            logger.error(f"Failed to send feedback from {user_email}")
            return jsonify({
                'success': False,
                'message': 'Failed to send feedback. Please try again later.'
            }), 500
            
    except Exception as e:
        logger.error(f"Error in send_feedback: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'An error occurred while sending feedback'
        }), 500