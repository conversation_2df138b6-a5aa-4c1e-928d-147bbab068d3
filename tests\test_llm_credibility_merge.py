import json
from datetime import datetime

from models.database_models import SearchSession
from api.app import get_cogbridges_service


def test_merge_credibility_from_raw_data_into_session_llm(db_session):
    """
    When a SearchSession has raw_data.llm_analysis.credibility_analysis but there are
    no structured LLM rows in DB, load_search_session should merge the credibility_analysis
    into the returned session_data['llm_analysis'] so that frontend can render commenter cards.
    """
    sid = "sess_merge_cred"

    # Insert a minimal SearchSession row with raw_data containing credibility_analysis
    raw_data = {
        "llm_analysis": {
            "credibility_analysis": {
                "credible_user": {
                    "username": "credible_user",
                    "expertise": {"summary": "Expert on testing and QA", "evidence_comments": ["e1"]},
                    "background_similarity": {"summary": "Similar background", "evidence_comments": ["e2"]},
                    "worldview": {"summary": "Pragmatic", "evidence_comments": []},
                    "per_comment": {}
                }
            }
        }
    }

    sess = SearchSession(
        id=sid,
        query="test query",
        timestamp=datetime.utcnow(),
        raw_data=raw_data,
        success=True,
    )
    db_session.add(sess)
    db_session.commit()

    # Use the same database service the app uses
    service = get_cogbridges_service()
    db_service = service.data_service.storage_service

    # Act
    loaded = db_service.load_search_session(sid)

    # Assert
    assert loaded is not None
    assert "llm_analysis" in loaded
    cred = loaded["llm_analysis"].get("credibility_analysis")
    assert isinstance(cred, dict)
    assert "credible_user" in cred
    assert cred["credible_user"]["expertise"]["summary"] == "Expert on testing and QA"

