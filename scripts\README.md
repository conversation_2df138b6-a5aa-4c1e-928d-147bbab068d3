# 数据维护脚本（PostgreSQL-only）

本目录包含与数据库初始化、校验以及辅助工具相关的脚本。

## 数据库脚本

### init_database.py
- 检查数据库连接与配置：
```bash
python scripts/init_database.py --check
```
- 创建数据库表：
```bash
python scripts/init_database.py --create-tables --force
```
- 校验 Schema 并输出漂移报告与建议 SQL：
```bash
python scripts/init_database.py --validate-schema
```
- 显示存储统计：
```bash
python scripts/init_database.py --stats
```

### migrate_add_points.py
- 为现有账户添加积分字段/初始化账本用途的迁移辅助。

### smtp_probe.py
- SMTP 邮件服务探测工具。

## 注意
- 已移除所有 JSON 存储维护脚本与说明；项目已切换为 PostgreSQL-only。
- 模型定义参见 `models/database_models.py`，数据库访问封装参见 `services/database_service.py`。