## CogBridges v2.0 — Reddit 智能搜索与评论者画像（内部）

- 基于 X.AI Grok 的“仅搜索”模式获取 citations → Reddit API 抓取真实帖子/评论
- LLM 精选最相关评论 → 拉取评论者历史 → 生成评论者背景与画像
- 前后端分离：Flask API + React/Vite 前端

> 仅供公司内部使用。请勿外传代码与文档、请勿将任何密钥写入仓库。敏感配置请使用 .env 或平台 Secret 管理。

- **导航**：快速开始 • 架构与目录 • 环境变量 • 运行与调试 • API • 前端 • 数据与存储 • 部署与 CI/CD • 安全与合规 • 测试 • 故障排查 • 变更记录

### 功能要点
- **Grok 搜索（仅搜索）**：返回 citations；严格以 reddit.com 为来源
- **Reddit 数据抓取**：并发拉取帖子详情与顶级评论（基于 asyncpraw）
- **评论内容筛选**：使用 LLM 进行相关性筛选，失败时按得分兜底裁剪
- **评论者历史与画像**：按展示评论的作者抓取 overview（帖子与评论），生成结构化画像（专业性/背景/世界观）

> 注意：已移除 Google Custom Search 实际依赖；代码保留兼容字段，但不会调用 Google。部分启动脚本仍检查占位变量，见“环境变量”的兼容提示。

---

## 快速开始

1) Python 依赖（Python 3.12）
```bash
pip install -r requirements.txt
```

2) 前置配置（.env 最小集合）
```ini
# X.AI Grok（必需，用于“仅搜索”获取 citations）
XAI_API_KEY=your_xai_api_key

# Reddit API（必需，用于抓取帖子/评论与用户历史）
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=CogBridges/2.0 by YourName

# Replicate（可选：启用 LLM 画像与内容筛选，否则跳过）
REPLICATE_API_TOKEN=your_replicate_api_token

# 端口（可选）
HOST=0.0.0.0
PORT=5000

# 数据库存储（PostgreSQL）
ENABLE_DATABASE=True
DATABASE_URL=********************************/db

# PayPal 支付（可选，启用付费功能）
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox  # sandbox 或 live
```

3) 一键启动（推荐）
```bash
python start_cogbridges.py
```
- 后端 API: http://127.0.0.1:5000
- 健康检查: http://127.0.0.1:5000/api/health
- 前端 UI:  http://localhost:5001

4) 手动启动（分终端）
```bash
# 终端1：后端
python api_server.py

# 终端2：前端
cd frontend
npm install
npm run dev -- --port 5001 --host
```
前端开发时可通过 `VITE_API_URL` 指定后端地址（启动器会自动注入）。

---

## 架构与目录结构

- **后端**：Flask 提供 REST API；服务层封装 Grok、Reddit、LLM、存储与数据库
- **前端**：React + Vite（开发端口默认为 `PORT+1`）
- **数据与日志**：PostgreSQL 存储；支持 I/O 快照与原始响应保存

```
CogBridges_v020/
├── api/                 # Flask 应用与路由（`api/app.py`）
├── services/            # 业务服务：Grok、Reddit、LLM、数据存储、数据库
├── models/              # Pydantic/Dataclass/SQLAlchemy 模型
├── frontend/            # React 前端
├── docs/                # 数据库与 LLM 存储文档
├── start_cogbridges.py  # 一键启动器（前后端）
├── api_server.py        # 仅后端启动器
├── frontend_server.py   # 仅前端启动器
├── render.yaml          # Render 平台部署配置
└── tests/               # 单元/集成测试
```

---

## 环境变量（要点）

> 完整可配置项见 `config.py`。以下为关键项（按模块分组）：

- **Grok / X.AI**
  - `XAI_API_KEY`（必填）：Grok API 密钥
  - `GROK_MODEL`（可选，默认 `grok-3`）
  - `GROK_MAX_COMMENTS`（默认 15）

- **Reddit**
  - `REDDIT_CLIENT_ID` / `REDDIT_CLIENT_SECRET` / `REDDIT_USER_AGENT`（必填）
  - `REDDIT_TOP_COMMENTS_COUNT`（默认 6）

- **LLM（Replicate，可选）**
  - `REPLICATE_API_TOKEN`（配置后启用画像/筛选）
  - `REPLICATE_MODEL`（默认 `openai/gpt-5-mini`）
  - 内容筛选：`CONTENT_SELECTION_MODEL`（默认 `openai/gpt-5-nano`）、`CONTENT_SELECTION_MAX_COMMENTS`（默认 10）、`CONTENT_SELECTION_MAX_PER_POST`（默认 5）、`CONTENT_SELECTION_REASONING_EFFORT`（默认 low）

- **应用与日志**
  - `HOST`（默认 `0.0.0.0`）/ `PORT`（默认 `5000`）
  - `LOG_LEVEL`（默认 `INFO`）、`SAVE_DETAILED_LOGS`（默认 True）、`ENABLE_JSON_LOGS`（默认 False）
  - `ENABLE_IO_SNAPSHOTS`（默认 True）、`IO_SNAPSHOTS_MAX_CHARS`（默认 4000）
  - CORS：`ENABLE_CORS`（默认 True）、`ALLOWED_ORIGINS`（默认允许本机与常见前端端口）

- **存储与数据库**
  - `ENABLE_DATABASE`（True）、`DATABASE_URL` 或 `DB_HOST/DB_NAME/DB_USER/...`

- **PayPal 支付（可选）**
  - `PAYPAL_CLIENT_ID` / `PAYPAL_CLIENT_SECRET`（PayPal 应用凭据）
  - `PAYPAL_MODE`（`sandbox` 测试环境或 `live` 生产环境）
  - `PAYPAL_WEBHOOK_ID`（可选，用于 webhook 验证）

- **兼容提示**
  - 个别启动脚本中仍保留对 Google 占位变量的检查（不实际调用）。未配置时可将占位键设为任意非空值以通过检查。

---

## 运行与调试

- 一键本地启动：`python start_cogbridges.py`
- 仅后端：`python api_server.py`（API 地址 `http://HOST:PORT`）
- 仅前端（在 `frontend/`）：`npm run dev`（默认端口 `PORT+1`）
- 前端会自动读取 `VITE_API_URL` 指向后端；一键启动器会注入局域网地址，便于手机联调

常用检查项：
- 健康检查：`GET /api/health`
- 状态页：`GET /api/status`
- 依赖概览：`GET /api/status` 响应中的 `features/services/statistics`

---

## API（核心）

- `GET /test`：简单健康探测
- `GET /api/health`：健康检查
- `GET /api/status`：特性与依赖配置状态
- `POST /api/search`：发起搜索；请求体：`{"query":"..."}`；立即返回 `session_id` 与 `poll_url`
- `GET /api/search/progress/{session_id}`：轮询进度；完成后包含 `result`
- `GET /api/sessions/{session_id}`：获取完整会话（数据库）
- `DELETE /api/sessions/{session_id}`：删除会话（数据库）
- `GET /api/history?limit=50`：最近会话列表（数据库）
- `GET /api/storage/statistics`：存储统计（数据库）

### 支付相关 API（需配置 PayPal）
- `POST /api/paypal/create-order`：创建 PayPal 支付订单
- `POST /api/paypal/execute-payment`：执行 PayPal 支付
- `POST /api/paypal/webhook`：PayPal webhook 处理
- `GET /api/user/points`：获取用户点数余额
- `GET /api/user/points/history`：获取点数变动历史
- `GET /api/sessions/{session_id}/llm-analysis`：会话的 LLM 分析数据

鉴权相关（如启用邮件与账户体系）：
- `POST /api/auth/register`、`POST /api/auth/login`、`POST /api/auth/logout`
- `POST /api/auth/send-code`、`POST /api/auth/verify`、`GET /api/auth/me`

进阶参数：`POST /api/search` 支持 `?top=10` 控制返回前 N 条精选评论（最终裁剪）。

---

## 前端

- 位置：`frontend/`（Vite + React）
- 本地开发：`npm run dev`（默认端口 `PORT+1`）
- 后端地址：运行器自动注入 `VITE_API_URL`；手动开发可自行设置环境变量
- E2E：`npm run test:e2e`（Playwright）

---

## 数据与存储（PostgreSQL-only）

- 根目录：`data/`（由 `config.py` 动态创建）
  - 日志：`data/logs/`（含 I/O 快照）
  - Grok 原始响应：`data/grok_raw_responses/`（citations + 原始响应；见 `GROK_RAW_RESPONSE_README.md`）
- 持久化：仅使用 PostgreSQL；参见 `docs/DATABASE_SETUP.md`
- LLM 分析存储与重放参见 `docs/LLM_ANALYSIS_STORAGE.md`

---

## 部署与 CI/CD

- Render 平台（参考 `render.yaml`）：
  - API 服务：`type: web, env: python`，启动命令：
    - `gunicorn -w 2 -k gthread --threads 8 -t 600 -b 0.0.0.0:$PORT api.app:create_app()`
    - 健康检查：`/api/health`
    - 挂载持久盘：`/app/data`
    - Secrets：`REDDIT_CLIENT_ID/SECRET`、`XAI_API_KEY`、`REPLICATE_API_TOKEN` 等在平台设置
  - 前端服务：`runtime: static`，`rootDir: frontend`，构建 `npm ci && npm run build`，发布 `dist/`
  - `VITE_API_URL` 在前端服务中配置为 API 外网 URL

- GitHub Actions（`.github/workflows/ci.yml`）：
  - 后端：安装依赖 → `pytest` 覆盖率（导出 `coverage.xml`）
  - 前端：Node 22，`npm ci` → 安装 Playwright 浏览器 → 运行 E2E

> 内部环境请关闭公共 Artifact 访问权限；若产物含敏感日志，务必在 CI 中脱敏或禁用上传。

---

## 安全与合规（内部）

- **密钥管理**：禁止将任何密钥写入代码库；本地使用 `.env`，云端使用平台 Secret（Render/其他）。
- **数据最小化**：原始响应与 I/O 快照可能包含敏感内容，保留时间与访问权限需受控；默认保存至 `data/`。
- **日志与排查**：`LOG_LEVEL` 默认 `INFO`；若开启 `ENABLE_JSON_LOGS`，确保日志汇聚平台权限受控。
- **CORS**：默认仅允许本机与局域网前端；生产请明确收敛 `ALLOWED_ORIGINS`。
- **隐私**：请勿上传含个人可识别信息（PII）的数据到公共存储或第三方服务。

---

## 测试

- 运行全部测试
```bash
pytest -q
```
- 主要用例
  - `tests/test_content_selection.py`：LLM 评论内容筛选
  - `tests/test_grok_citations.py`：Grok citations 归一化与去重
  - `tests/test_reddit_comments_all.py`：Reddit 顶级评论抓取
  - `tests/test_database_service.py`：数据库接口（使用 mock）
  - `tests/test_url_dedup.py`：按帖子 ID 归一化去重流程

---

## 故障排查与检查清单

- **依赖**：`pip install -r requirements.txt` 成功；前端 `npm install / npm ci` 成功
- **密钥**：`XAI_API_KEY`、`REDDIT_*` 已配置；`/api/status` 显示已就绪
- **端口**：`PORT`/`PORT+1` 未被占用；启动器可清理或更换端口
- **搜索无结果**：检查 `XAI_API_KEY` 与网络；`/api/status` 查看依赖状态
- **Reddit 抓取失败**：确认 Reddit 凭据、速率限制与网络；适当下调并发
- **LLM 画像为空**：未配置 `REPLICATE_API_TOKEN` 时会跳过 LLM 分析
- **前端 404/白屏**：确认 `VITE_API_URL` 指向可达后端

---

## 变更记录（v2.0 摘要）
- 移除 Google 搜索依赖，统一走 Grok（仅搜索）+ Reddit API 实抓
- 新增 LLM 驱动的评论内容筛选与背景画像
- 数据存储改为 PostgreSQL-only

---

## 维护信息（请补充）
- 负责人：<Owner/Team>
- 日常维护：<Maintainers>
- 值班/报警：<Oncall or channel>
- 文档反馈：提交 Issue 或联系维护人
