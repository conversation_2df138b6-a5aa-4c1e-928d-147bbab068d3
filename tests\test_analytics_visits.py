import os
import json


def test_record_site_visit_and_conversion_metrics(test_client, db_session):
    # Ensure DB is available for tests
    os.environ.setdefault("ENABLE_DATABASE", "True")

    # Record a site visit
    payload = {
        "anonymous_cookie_id": "test_cookie_123",
        "landing_path": "/",
        "utm_source": "test",
        "utm_medium": "qa",
        "utm_campaign": "unit"
    }
    resp = test_client.post(
        '/api/analytics/visit',
        data=json.dumps(payload),
        headers={'Content-Type': 'application/json', 'User-Agent': 'pytest-UA', 'X-Forwarded-For': '127.0.0.1'}
    )
    assert resp.status_code == 200
    data = resp.get_json()
    assert data and data.get('success') is True

    # Query conversion analytics
    resp2 = test_client.get('/api/analytics/conversion?days=7')
    assert resp2.status_code == 200
    conv = resp2.get_json()
    assert conv and conv.get('success') is True
    visits = conv.get('visits') or {}
    # At least 1 visit after recording
    assert (visits.get('total_visits') or 0) >= 1

