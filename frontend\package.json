{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npx vite", "build": "npx vite build", "lint": "eslint .", "preview": "npx vite preview", "preview:ci": "npx vite build && npx vite preview --port 4173 --host 127.0.0.1", "preview:pw": "npx vite build && npx vite preview --port 5173 --host 127.0.0.1 --strictPort", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "npm run build && npx playwright test", "test:e2e:real": "npm run build && npx playwright test --project=e2e-real-backend", "test:all": "npm run test && npm run test:e2e"}, "dependencies": {"@react-oauth/google": "^0.12.2", "axios": "^1.11.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.7.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@playwright/test": "^1.54.2", "@testing-library/jest-dom": "^6.7.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.3.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^2.1.9", "@vitest/ui": "^2.1.9", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^25.0.1", "playwright": "^1.54.2", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^7.0.4", "vitest": "^2.1.9"}}