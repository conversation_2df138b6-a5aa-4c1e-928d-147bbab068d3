import { test, expect } from '@playwright/test'

// Desktop smoke
test('home page renders and search input is visible', async ({ page }) => {
  await page.goto('/')
  await expect(page.getByRole('img', { name: 'CogBridges' })).toBeVisible()
  await expect(page.getByRole('textbox', { name: 'Search questions from Reddit' })).toBeVisible({ timeout: 5000 })
})

// Mobile smoke (iPhone 12 viewport)
test.use({ viewport: { width: 390, height: 844 } })

test('mobile layout shows hero and footer', async ({ page }) => {
  await page.goto('/')
  await expect(page.getByText('Find more trustworthy answers')).toBeVisible()
  await expect(page.getByText('© 2025 CogBridges')).toBeVisible()
})
