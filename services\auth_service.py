#!/usr/bin/env python3
"""
AuthService - 简洁的认证服务（JSON 本地存储后备）
当数据库不可用时，用于进行用户注册/登录。
"""

from __future__ import annotations

import json
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime

from werkzeug.security import generate_password_hash, check_password_hash

from config import config
from utils.logger_utils import get_logger


class AuthService:
    """基于本地 JSON 的简易认证服务（用于数据库不可用场景）"""

    def __init__(self):
        self.logger = get_logger(__name__)
        self.store_path = Path(config.DATA_DIR) / "auth_users.json"
        self.codes_path = Path(config.DATA_DIR) / "auth_email_codes.json"
        self._ensure_store()

    def _ensure_store(self):
        try:
            if not self.store_path.exists():
                default = {"last_id": 0, "users": []}
                self.store_path.parent.mkdir(parents=True, exist_ok=True)
                self.store_path.write_text(json.dumps(default, ensure_ascii=False, indent=2), encoding="utf-8")
            if not self.codes_path.exists():
                self.codes_path.parent.mkdir(parents=True, exist_ok=True)
                self.codes_path.write_text(json.dumps({"codes": {}}, ensure_ascii=False, indent=2), encoding="utf-8")
        except Exception as e:
            self.logger.error(f"初始化认证存储失败: {e}")
            raise

    def _load(self) -> Dict[str, Any]:
        try:
            return json.loads(self.store_path.read_text(encoding="utf-8") or "{}")
        except Exception as e:
            self.logger.error(f"读取认证存储失败: {e}")
            return {"last_id": 0, "users": []}

    def _save(self, data: Dict[str, Any]):
        try:
            self.store_path.write_text(json.dumps(data, ensure_ascii=False, indent=2), encoding="utf-8")
        except Exception as e:
            self.logger.error(f"写入认证存储失败: {e}")
            raise

    def _load_codes(self) -> Dict[str, Any]:
        try:
            return json.loads(self.codes_path.read_text(encoding="utf-8") or "{}")
        except Exception:
            return {"codes": {}}

    def _save_codes(self, data: Dict[str, Any]):
        try:
            self.codes_path.write_text(json.dumps(data, ensure_ascii=False, indent=2), encoding="utf-8")
        except Exception as e:
            self.logger.error(f"写入验证码存储失败: {e}")
            raise

    # 公开方法
    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        data = self._load()
        for u in data.get("users", []):
            if (u.get("email") or "").lower() == (email or "").lower():
                return u
        return None

    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        data = self._load()
        for u in data.get("users", []):
            if int(u.get("id")) == int(user_id):
                return u
        return None

    def create_user(self, email: str, raw_password: str) -> Optional[Dict[str, Any]]:
        data = self._load()
        if any((u.get("email") or "").lower() == (email or "").lower() for u in data.get("users", [])):
            return None
        user_id = int(data.get("last_id", 0)) + 1
        now = datetime.utcnow().isoformat()
        user = {
            "id": user_id,
            "email": email,
            "password_hash": generate_password_hash(raw_password),
            "is_active": False,
            "email_verified": False,
            "last_login": None,
            "created_at": now,
            "updated_at": now,
        }
        data["users"].append(user)
        data["last_id"] = user_id
        self._save(data)
        return user

    def update_user_last_login(self, user_id: int) -> bool:
        data = self._load()
        updated = False
        for u in data.get("users", []):
            if int(u.get("id")) == int(user_id):
                u["last_login"] = datetime.utcnow().isoformat()
                u["updated_at"] = datetime.utcnow().isoformat()
                updated = True
                break
        if updated:
            self._save(data)
        return updated

    @staticmethod
    def verify_password(password_hash: str, raw_password: str) -> bool:
        try:
            return check_password_hash(password_hash, raw_password)
        except Exception:
            return False

    @staticmethod
    def to_safe_user(user_like: Dict[str, Any]) -> Dict[str, Any]:
        return {
            "id": user_like.get("id"),
            "email": user_like.get("email"),
            "is_active": bool(user_like.get("is_active", True)),
            "email_verified": bool(user_like.get("email_verified", False)),
            "last_login": user_like.get("last_login"),
            "created_at": user_like.get("created_at"),
            "updated_at": user_like.get("updated_at"),
        }

    # ------------------------------
    # 验证码相关（JSON后备）
    # ------------------------------
    def generate_and_store_code(self, email: str, code: str, ttl_seconds: int = 10 * 60) -> bool:
        codes = self._load_codes()
        from time import time
        codes.setdefault("codes", {})[email.lower()] = {
            "code": code,
            "expires_at": int(time()) + int(ttl_seconds)
        }
        self._save_codes(codes)
        return True

    def verify_code(self, email: str, code: str) -> bool:
        codes = self._load_codes()
        from time import time
        entry = (codes.get("codes") or {}).get(email.lower())
        if not entry:
            return False
        if str(entry.get("code")) != str(code):
            return False
        if int(time()) > int(entry.get("expires_at", 0)):
            return False
        # 成功一次后移除
        try:
            del codes["codes"][email.lower()]
            self._save_codes(codes)
        except Exception:
            pass
        return True

    def mark_email_verified(self, email: str) -> bool:
        data = self._load()
        changed = False
        for u in data.get("users", []):
            if (u.get("email") or "").lower() == (email or "").lower():
                if not u.get("email_verified"):
                    u["email_verified"] = True
                    u["is_active"] = True
                    u["updated_at"] = datetime.utcnow().isoformat()
                    changed = True
                break
        if changed:
            self._save(data)
        return changed
