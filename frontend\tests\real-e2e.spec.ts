import { test, expect } from '@playwright/test'

// 真正的端到端测试 - 连接到真实后端API
test.describe('Real End-to-End Tests with Backend', () => {
  test.beforeEach(async ({ page }) => {
    // 验证后端API是否可用
    try {
      const response = await page.request.get('http://127.0.0.1:5000/api/health')
      expect(response.status()).toBe(200)
      console.log('✅ Backend API is accessible')
    } catch (error) {
      console.error('❌ Backend API is not accessible:', error)
      throw new Error('Backend API must be running on port 5000 for real E2E tests')
    }
  })

  test('real purchase flow opens PayPal with real backend', async ({ page }) => {
    // 需要后端运行在 127.0.0.1:5000，并且前端通过真实 API 发起请求

    // 在页面加载前，注入一个最小的 PayPal SDK 占位，避免 UI 处于“Loading PayPal...”状态
    await page.addInitScript(() => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      window.paypal = window.paypal || {}
    })

    // 先完成注册与邮箱验证，以便拿到真实 token（后端会在 DEBUG 下返回 dev_code）
    const unique = Date.now()
    const email = `e2e_paypal_${unique}@example.com`
    const password = 'Pw123456!'
    const reg = await page.request.post('http://127.0.0.1:5000/api/auth/register', {
      data: { email, password }
    })
    expect(reg.status()).toBeLessThan(500)
    const regJson = await reg.json()
    const code = regJson.dev_code || regJson.code || regJson.verification_code
    expect(code).toBeTruthy()

    const verify = await page.request.post('http://127.0.0.1:5000/api/auth/verify', {
      data: { email, code }
    })
    expect(verify.status()).toBeLessThan(500)
    const verifyJson = await verify.json()
    const token = verifyJson.token
    expect(token).toBeTruthy()

    // 将 token 写入 localStorage，并刷新让前端把它带入 Authorization 头
    await page.goto('/')
    await expect(page.getByRole('img', { name: 'CogBridges' })).toBeVisible()
    await page.evaluate(t => localStorage.setItem('auth_token', t), token)
    await page.reload()

    // 打开购买弹窗
    // 首先尝试点击 Top up；若不存在，则回退到页面上的其他入口
    const openBtn = page.getByText('Top up').first()
    if (await openBtn.isVisible()) {
      await openBtn.click()
    } else {
      // 通过不足积分弹窗触发
      const searchInput = page.getByRole('textbox', { name: 'Search questions from Reddit' })
      await searchInput.fill('trigger purchase modal')
      await page.getByRole('button', { name: 'Submit search' }).click()
      await page.getByText('Purchase Points').click()
    }

    // 看到弹窗与按钮
    await expect(page.getByText('Purchase Points')).toBeVisible()

    // 监听真实网络请求到 create-order（不拦截）
    const [request] = await Promise.all([
      page.waitForRequest(req => req.url().includes('/api/paypal/create-order') && req.method() === 'POST'),
      page.getByText('Pay with PayPal').first().click()
    ])

    console.log('create-order request url:', request.url())
    expect(request.url()).toContain('/api/paypal/create-order')

    // 允许 4xx/5xx，因为真实后端可能未配置沙盒密钥，但我们验证的是前端能触发请求
    const response = await request.response()
    expect(response).not.toBeNull()
    console.log('create-order response status:', await response?.status())

    // 关键断言：不应再出现之前的后端异常信息
    await expect(page.getByText(/int object has no attribute/i)).toHaveCount(0)
  })

  test('real search flow with backend API', async ({ page }) => {
    // 访问前端页面
    await page.goto('/')
    await expect(page.getByRole('img', { name: 'CogBridges' })).toBeVisible()
    
    // 验证搜索输入框可见
    const searchInput = page.getByRole('textbox', { name: 'Search questions from Reddit' })
    await expect(searchInput).toBeVisible()
    
    // 输入搜索查询
    await searchInput.fill('test query')
    await expect(searchInput).toHaveValue('test query')
    
    // 点击搜索按钮
    const searchButton = page.getByRole('button', { name: 'Submit search' })
    await expect(searchButton).toBeVisible()
    await searchButton.click()
    
    // 验证搜索开始（应该显示加载状态或错误信息）
    // 注意：由于这是真实测试，我们不知道后端会返回什么
    // 所以我们检查页面是否有相应的响应
    try {
      // 等待页面响应，可能是加载状态、错误信息或结果
      await page.waitForTimeout(2000) // 给后端一些响应时间
      
      // 检查页面是否有任何变化
      const currentUrl = page.url()
      console.log('Current URL after search:', currentUrl)
      
      // 检查页面内容是否有变化
      const pageContent = await page.content()
      if (pageContent.includes('Working on your answers')) {
        console.log('✅ Search started successfully - showing loading state')
      } else if (pageContent.includes('error') || pageContent.includes('Error')) {
        console.log('⚠️ Search encountered an error')
      } else if (pageContent.includes('Featured Comment')) {
        console.log('✅ Search completed successfully - showing results')
      } else {
        console.log('ℹ️ Search response received but content unclear')
      }
      
    } catch (error) {
      console.log('ℹ️ Search response handling:', error.message)
    }
  })

  test('backend health check', async ({ page }) => {
    // 直接测试后端健康检查端点
    const response = await page.request.get('http://127.0.0.1:5000/api/health')
    expect(response.status()).toBe(200)
    
    const data = await response.json()
    console.log('Backend health response:', data)
    
    // 验证响应格式
    expect(data).toHaveProperty('status')
    expect(data).toHaveProperty('service')
    expect(data.status).toBe('healthy')
  })

  test('frontend can reach backend API', async ({ page }) => {
    // 测试前端页面加载后是否能访问后端
    await page.goto('/')
    
    // 等待页面完全加载
    await page.waitForLoadState('networkidle')
    
    // 检查页面是否有任何网络错误
    const consoleErrors = []
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text())
      }
    })
    
    // 等待一段时间让页面稳定
    await page.waitForTimeout(3000)
    
    console.log('Console errors during page load:', consoleErrors)
    
    // 如果有网络相关的错误，记录下来
    const networkErrors = consoleErrors.filter(error => 
      error.includes('fetch') || error.includes('network') || error.includes('API')
    )
    
    if (networkErrors.length > 0) {
      console.log('⚠️ Network/API errors detected:', networkErrors)
    } else {
      console.log('✅ No network/API errors detected')
    }
  })
})
