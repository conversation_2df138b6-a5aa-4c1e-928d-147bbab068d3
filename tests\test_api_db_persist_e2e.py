import os
import time
import json
import pytest

import pytest
os.environ["ENABLE_DATABASE"] = "True"
os.environ.setdefault("TEST_MODE", "True")
if not str(os.environ.get("DATABASE_URL", "")).startswith("postgresql"):
    pytest.skip("PostgreSQL required for DB persistence E2E tests", allow_module_level=True)

from api.app import create_app
from api.tokens import issue_token


def _poll_until_done(c, sid, timeout=5.0):
    start = time.time()
    status = None
    while time.time() - start < timeout:
        r = c.get(f"/api/search/progress/{sid}")
        if r.status_code == 200:
            data = r.get_json()
            status = data.get("status")
            if status in ("completed", "error", "cancelled"):
                return status, data
        time.sleep(0.1)
    return status, None


def test_e2e_persist_and_history_with_database(monkeypatch):
    # Stub: avoid real external requests, directly return a structure with minimal data
    from services.cogbridges_service import CogBridgesService

    async def fake_step1(self, query: str, cancel_event=None):
        return {
            "posts": [
                {
                    "success": True,
                    "post": {
                        "id": "p1",
                        "title": "",
                        "selftext": "",
                        "url": "https://reddit.com/r/a/comments/abc/title",
                        "permalink": "/r/a/comments/abc/title",
                        "author": "",
                        "subreddit": "a",
                        "score": 0,
                        "num_comments": 0,
                        "created_utc": None,
                    },
                    "comments": [
                        {
                            "id": "c1",
                            "body": "hello",
                            "author": "u1",
                            "score": 1,
                            "created_utc": None,
                            "permalink": "https://reddit.com/r/a/comments/abc/comment/c1",
                        }
                    ],
                    "commenters": ["u1"],
                }
            ],
            "google_results": [],
            "search_time": 0.01,
            "citations": [],
        }

    async def fake_step2(self, posts_data, cancel_event=None):
        return {
            "history": {"u1": {"_metadata": {"subreddits": ["a"]}}},
            "processing_time": 0.02,
            "selected_usernames": ["u1"],
        }

    async def fake_step3(self, result, cancel_event=None):
        return {"success": True, "analysis_summary": {}}

    monkeypatch.setattr(CogBridgesService, "_step1_grok_reddit_search", fake_step1, raising=True)
    monkeypatch.setattr(CogBridgesService, "_step2_get_commenters_history", fake_step2, raising=True)
    monkeypatch.setattr(CogBridgesService, "_step3_llm_analysis", fake_step3, raising=True)

    app = create_app()
    app.config.update(TESTING=True)
    client = app.test_client()

    # Create a test user first
    from api.app import get_cogbridges_service
    service = get_cogbridges_service()
    
    if service and service.data_service and service.data_service.storage_service:
        from models.database_models import UserAccount
        from werkzeug.security import generate_password_hash
        
        with service.data_service.storage_service.get_session() as session:
            # Check if user exists
            user = session.query(UserAccount).filter_by(id=1001).first()
            if not user:
                # Create test user
                user = UserAccount(
                    id=1001,
                    email='<EMAIL>',
                    password_hash=generate_password_hash('testpass'),
                    is_active=True,
                    email_verified=True,
                    points_balance=100
                )
                session.add(user)
                session.commit()

    # Use login token to attribute to user, convenient for /api/history filtering
    token = issue_token(1001)

    r = client.post("/api/search", json={"query": "db e2e"}, headers={"Authorization": f"Bearer {token}"})
    assert r.status_code == 200
    sid = r.get_json()["session_id"]

    st, data = _poll_until_done(client, sid, timeout=15.0)
    assert st in ("completed", "error"), f"Expected completed or error status, but got: {st}"

    # Get history, confirm source includes database branch (if database connection is normal)
    h = client.get("/api/history", headers={"Authorization": f"Bearer {token}"})
    assert h.status_code == 200
    payload = h.get_json()
    assert payload.get("success") is True
    # Allow empty (if some paths downgrade to JSON), but prioritize verifying non-empty contains expected fields
    items = payload.get("data", [])
    for it in items:
        if it.get("source") == "database":
            assert "reddit_posts_count" in it
            # New metric fields should be readable (may be 0 or default but field exists)
            # Note: /api/history side only returns session overview fields, not mandatory all exist here
            break