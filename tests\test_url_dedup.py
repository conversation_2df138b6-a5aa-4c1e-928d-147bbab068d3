import pytest
from unittest.mock import patch, MagicMock
from services.cogbridges_service import CogBridgesService


def test_fetch_posts_from_citations_dedup_by_post_id():
    # Mock数据库服务以避免连接问题
    with patch('services.data_service.DataService') as mock_data_service:
        mock_data_service.return_value.storage_service = None
        service = CogBridgesService()

        # citations 含同一帖子不同变体：带/不带标题、含语言参数、www 子域、评论级链接
        citations = [
            {"url": "https://www.reddit.com/r/china/comments/1lxmxw0/whatever-title/"},
            {"url": "https://reddit.com/r/china/comments/1lxmxw0/?tl=zh-hans"},
            {"url": "https://reddit.com/r/china/comments/1lxmxw0/another-title/?utm=abc"},
            {"url": "https://reddit.com/r/china/comments/1lxmxw0/another-title/comment/xyz"},
        ]

        # 禁用真实 Reddit API 抓取，触发最小结构返回路径
        from config import config as app_config
        old_client_id = app_config.REDDIT_CLIENT_ID
        old_client_secret = app_config.REDDIT_CLIENT_SECRET
        try:
            app_config.REDDIT_CLIENT_ID = ""  # 强制未配置
            app_config.REDDIT_CLIENT_SECRET = ""

            import asyncio
            posts = asyncio.run(service._fetch_posts_from_citations(citations))
            # 期望只有一个唯一帖子URL
            urls = [p["post"]["url"] for p in posts]
            assert len(urls) == 1
            assert urls[0] == "https://reddit.com/r/china/comments/1lxmxw0"
        finally:
            # 恢复配置，避免污染其他测试
            app_config.REDDIT_CLIENT_ID = old_client_id
            app_config.REDDIT_CLIENT_SECRET = old_client_secret


def _mock_completion_with_citations(citations):
    class MockCompletion:
        def __init__(self):
            self.choices = [type('Choice', (), {
                'finish_reason': 'stop',
                'message': type('Message', (), {'content': '{}'})()
            })()]
            self.citations = citations
        def model_dump(self):
            return {"citations": citations}
    return MockCompletion()


def test_dedupe_citations_by_post_id():
    from services.grok_reddit_service import GrokRedditService
    svc = GrokRedditService()
    citations = [
        {"url": "https://www.reddit.com/r/China/comments/1LxMxW0/title/"},
        {"url": "https://reddit.com/r/china/comments/1lxmxw0/?tl=zh-hans"},
        {"url": "https://reddit.com/r/china/comments/1lxmxw0/title/comment/abc"},
        "https://reddit.com/r/china/comments/1lxmxw0/",
    ]
    deduped = svc._dedupe_citations(citations)
    assert isinstance(deduped, list)
    assert len(deduped) == 1
    assert deduped[0]["url"] == "https://reddit.com/r/china/comments/1lxmxw0"


