"""
CogBridges Search - LLM服务
实现Replicate API调用，支持参数和prompt管理
"""

import asyncio
import time
from typing import List, Dict, Any, Optional
try:
	import replicate  # Optional: allow tests to run without this dependency
except Exception:  # pragma: no cover - optional import for test environments
	replicate = None
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential

from config import config
from utils.logger_utils import get_logger, log_api_call
from utils.logger_utils import persist_io_record



class LLMService:
	"""LLM服务类，支持Replicate API调用"""
	
	def __init__(self):
		"""初始化LLM服务"""
		self.logger = get_logger(__name__)
		
		# 检查配置
		self.configured = config.replicate_configured and (replicate is not None)
		if not self.configured:
			self.logger.warning("Replicate API未配置或客户端不可用，LLM服务不可用")
			return
		
		
		# 初始化Replicate客户端（提升HTTP超时，避免长任务读超时）
		try:
			timeout = httpx.Timeout(
				5.0,
				read=getattr(config, "REPLICATE_HTTP_TIMEOUT_READ", 120.0),
				write=getattr(config, "REPLICATE_HTTP_TIMEOUT_WRITE", 60.0),
				connect=getattr(config, "REPLICATE_HTTP_TIMEOUT_CONNECT", 10.0),
				pool=getattr(config, "REPLICATE_HTTP_TIMEOUT_POOL", 30.0),
			)
		except Exception:
			# 回退到较大的读超时
			timeout = httpx.Timeout(5.0, read=120.0, write=60.0, connect=10.0, pool=30.0)

		self.client = replicate.Client(
			api_token=config.REPLICATE_API_TOKEN,
			timeout=timeout,
		)
		
		# 请求统计
		self.request_count = 0
		self.total_request_time = 0.0
		
		self.logger.info("LLM服务初始化成功")
	
	# 统一使用 Replicate 的通用参数：prompt/system_prompt/max_completion_tokens/reasoning_effort
	# 不再做模型差异映射，保证代码简单直观

	@retry(
		stop=stop_after_attempt(3),
		wait=wait_exponential(multiplier=1, min=4, max=10)
	)
	@log_api_call("Replicate", "run", "POST")
	async def generate_text(
		self,
		prompt: str,
		model: str = None,
		max_completion_tokens: int = 16000,
		reasoning_effort: str = "low",
		system_prompt: str = None,
		cancel_event: Optional[object] = None,
		**kwargs
	) -> Optional[str]:
		"""
		生成文本
		
		Args:
			prompt: 用户提示
			model: 模型名称，默认使用配置中的模型
			max_completion_tokens: 生成的最大 completion token 数
			reasoning_effort: 推理强度（minimal/low/medium/high）
			system_prompt: 系统提示
			**kwargs: 其他参数
			
		Returns:
			生成的文本
		"""
		if not self.configured:
			raise ValueError("Replicate API not configured")
		
		start_time = time.time()
		# 默认模型切换为 gpt-5 系列（config 中已设为 gpt-5-mini）。允许调用方覆盖。
		model = model or config.REPLICATE_MODEL
		
		try:
			# 构建输入参数（直接使用 Replicate 的通用字段）
			input_data: Dict[str, Any] = {
				"prompt": prompt,
				"max_completion_tokens": max_completion_tokens,
				"reasoning_effort": reasoning_effort,
				# 强制 JSON 输出（若模型支持）
				"response_format": kwargs.pop("response_format", {"type": "json_object"}) if kwargs.get("force_json", True) else kwargs.pop("response_format", None),
				**kwargs,
			}
			if system_prompt:
				input_data["system_prompt"] = system_prompt

			self.logger.debug(
				f"调用LLM模型: {model}, prompt长度: {len(prompt)}, keys: {sorted(list(input_data.keys()))}"
			)
			
			# 调用Replicate API，若遇到不支持的参数，做一次降级重试
			prediction = None
			try:
				# 首次请求使用 wait=False 以获得 prediction 对象，便于取消
				output = await asyncio.to_thread(
					self.client.run,
					model,
					input=input_data,
					wait=False,
				)
				# 兼容：wait=False 时通常返回 Prediction 对象
				prediction = output if hasattr(output, 'id') else None
			except Exception as run_err:
				err_msg = str(run_err) if run_err else ""
				downgraded = False
				safe_input = dict(input_data)
				# 若 response_format 不被模型支持，则去掉后重试一次
				if "response_format" in safe_input:
					safe_input.pop("response_format", None)
					downgraded = True
				if downgraded:
					self.logger.warning(f"模型参数不兼容，降级重试: {err_msg}")
					output = await asyncio.to_thread(
						self.client.run,
						model,
						input=safe_input,
						wait=False,
					)
					prediction = output if hasattr(output, 'id') else None
				else:
					raise

			# 如果拿到了 prediction，则轮询其状态并支持取消
			if prediction is not None and hasattr(self.client, 'predictions'):
				pred_id = getattr(prediction, 'id', None)
				last_status = None
				# 轮询直到完成或取消
				while True:
					# 取消检查
					if cancel_event is not None and getattr(cancel_event, 'is_set', lambda: False)():
						try:
							# 尝试取消远端预测
							await asyncio.to_thread(self.client.predictions.cancel, pred_id)
						except Exception:
							pass
						# 再取一次状态并跳出
						try:
							prediction = await asyncio.to_thread(self.client.predictions.get, pred_id)
						except Exception:
							break
						break
					# 获取状态
					try:
						prediction = await asyncio.to_thread(self.client.predictions.get, pred_id)
						last_status = getattr(prediction, 'status', None)
						if last_status in ("succeeded", "failed", "canceled"):
							break
					except Exception:
						# 轻微网络错误：稍后重试
						pass
					await asyncio.sleep(0.8)

				# 输出统一为字符串
				output = getattr(prediction, 'output', None)
				if isinstance(output, list):
					result = "".join(str(item) for item in output)
				else:
					result = str(output) if output is not None else ""
			else:
				# 旧路径：直接把 run(wait=False) 的返回当作结果（部分 SDK 会直接返回文本）
				if isinstance(output, list):
					result = "".join(str(item) for item in output)
				else:
					result = str(output)
			
			# 更新统计
			self.request_count += 1
			self.total_request_time += time.time() - start_time
			
			self.logger.info(f"LLM调用成功，输出长度: {len(result)}")
			# I/O 快照（可裁剪大文本）
			try:
				if config.ENABLE_IO_SNAPSHOTS:
					max_chars = int(getattr(config, 'IO_SNAPSHOTS_MAX_CHARS', 4000))
					req_preview = {
						"model": model,
						"max_completion_tokens": max_completion_tokens,
						"reasoning_effort": reasoning_effort,
						"has_system_prompt": bool(system_prompt),
						"prompt": (prompt[:max_chars] + "..." if prompt and len(prompt) > max_chars else prompt),
					}
					resp_preview = {
						"text": (result[:max_chars] + "..." if result and len(result) > max_chars else result)
					}
					persist_io_record(
						service="replicate",
						action="generate_text",
						request_payload=req_preview,
						response_payload=resp_preview,
					)
			except Exception:
				pass
			return result
			
		except Exception as e:
			self.logger.error(f"LLM调用失败: {e}")
			try:
				if config.ENABLE_IO_SNAPSHOTS:
					persist_io_record(
						service="replicate",
						action="generate_text",
						request_payload={
							"model": model,
							"max_completion_tokens": max_completion_tokens,
							"reasoning_effort": reasoning_effort,
							"has_system_prompt": bool(system_prompt),
							"prompt": (prompt[:1000] + "..." if prompt and len(prompt) > 1000 else prompt),
						},
						response_payload=None,
						error=str(e),
					)
			except Exception:
				pass
			raise
	

	
	def get_stats(self) -> Dict[str, Any]:
		"""获取服务统计信息"""
		return {
			"configured": self.configured,
			"request_count": self.request_count,
			"total_request_time": self.total_request_time,
			"average_request_time": (
				self.total_request_time / self.request_count 
				if self.request_count > 0 else 0
			)
		}
	
	async def test_connection(self) -> bool:
		"""测试Replicate API连接"""
		if not self.configured:
			return False

		try:
			# 简单的测试调用
			result = await self.generate_text(
				prompt="Hello",
				max_completion_tokens=16000,
				reasoning_effort="low",
			)
			if result:
				self.logger.info("Replicate API连接测试成功")
				return True
			else:
				self.logger.error("Replicate API连接测试失败: 无响应")
				return False
		except Exception as e:
			self.logger.error(f"Replicate API连接测试失败: {e}")
			return False

	async def analyze_commenter_credibility(
		self,
		username: str,
		high_score_content: List[Dict[str, Any]],
		focus_comments: Optional[List[Dict[str, Any]]] = None,
		query: Optional[str] = None,
		cancel_event: Optional[object] = None,
	) -> Dict[str, Any]:
		"""
		分析评论者背景

		Args:
			username: 用户名
			high_score_content: 用户的高赞内容列表

		Returns:
			评论者背景分析结果
		"""
		if not high_score_content:
			self.logger.warning(f"用户{username}没有高赞内容可供分析")
			return {
				"username": username,
				"error": "Insufficient high-score content for analysis"
			}

		# 记录分析开始
		self.logger.info(f"开始分析用户{username}的背景 - 高赞内容数量: {len(high_score_content)}")

		try:
			# 构建分析提示（加入焦点评论和查询上下文以提升对齐度）
			prompt = self._build_credibility_analysis_prompt(
				username,
				high_score_content,
				focus_comments=focus_comments or [],
				query=query or ""
			)

			# 调用LLM进行分析
			response = await self.generate_text(
				prompt=prompt,
				temperature=0.4,  # 适中的温度，平衡创造性和一致性
				max_completion_tokens=16000,
				reasoning_effort=str(getattr(config, 'LLM_CREDIBILITY_REASONING_EFFORT', 'low') or 'low'),
				force_json=True,
				cancel_event=cancel_event,
			)

			# 解析分析结果
			result = self._parse_credibility_analysis_result(response, username)

			# 记录分析完成
			self.logger.info(f"用户{username}背景分析完成")

			return result

		except Exception as e:
			self.logger.error(f"用户{username}背景分析失败: {e}")
			return {
				"username": username,
				"error": str(e)
			}


	# 批量分析接口已废弃





	def _build_credibility_analysis_prompt(
		self,
		username: str,
		high_score_content: List[Dict[str, Any]],
		focus_comments: Optional[List[Dict[str, Any]]] = None,
		query: Optional[str] = None
	) -> str:
		"""构建背景分析提示"""

		# 构建用户内容列表
		content_list = []
		for i, content in enumerate(high_score_content[:20], 1):  # 限制最多20条
			content_type = content.get("type", "unknown")
			content_text = content.get("content", "")
			score = content.get("score", 0)
			subreddit = content.get("subreddit", "unknown")

			content_list.append(f"""
内容 {i} ({content_type}, 得分: {score}, 来自: {subreddit}):
{content_text[:500]}{"..." if len(content_text) > 500 else ""}
""")

		content_text = "\n".join(content_list)

		# 焦点评论：当前结果页中该用户的具体评论，用于对齐分析
		focus_list = []
		safe_focus = focus_comments or []
		for i, fc in enumerate(safe_focus[:5], 1):  # 最多纳入5条
			key = fc.get("key") or fc.get("permalink") or f"focus_{i}"
			excerpt = (fc.get("excerpt") or fc.get("body") or "")[:500]
			subreddit = fc.get("subreddit", "unknown")
			post_title = fc.get("post_title", "")
			focus_list.append(f"""
焦点评论 {i} (comment_key: {key}, 来自: {subreddit}):
{excerpt}
对应帖子: {post_title}
""")
		focus_text = "\n".join(focus_list) if focus_list else "无"

		query_text = (query or "").strip()

		prefix = """You are analyzing the public Reddit comment history of a user.

Below are some this user's most upvoted comments, from various subreddits.

Your goal is to help us understand the *type of person* behind these comments by extracting 3 things:

1. Expertise — What areas does this person seem knowledgeable in?
2. Background Similarity — What do these comments reveal about the user's demographics, lifestyle, or personal context?
3. Worldview / Values / Stance — What values, perspectives, or recurring stances does this person seem to hold?

Please respond with a structured JSON object like this:

{
  "expertise": {
    "summary": "...",
    "evidence_comments": ["..."]
  },
  "background_similarity": {
    "summary": "...",
    "possible_tags": ["..."],
    "evidence_comments": ["..."]
  },
  "worldview": {
    "summary": "...",
    "value_tags": ["..."],
    "evidence_comments": ["..."]
  },
  "per_comment": {
    "<comment_key>": {
      "comment_excerpt": "...",
      "relevance_to_expertise": 0,
      "relevance_to_background": 0,
      "relevance_to_worldview": 0,
      "overall_relevance": 0,
      "comment_specific_background": {
        "summary": "...",
        "tags": ["..."]
      },
      "justification": "...",
      "evidence_spans": ["..."]
    }
  }
}

Make sure to:
- Use the exact phrasing or paraphrased version of user's comments in `evidence_comments`.
- Be concise and insightful in summaries.
- Avoid assumptions not grounded in the comments.
- For `per_comment`, only reference the provided focus comment(s). Use "comment_key" exactly as given below.
"""

		prompt = (
			prefix
			+ f"""

Here are the user's comments:
===
{content_text}
===

Current search query (for context; do NOT overfit):
{query_text}

Focus comment(s) to align with (return a `per_comment` entry for each, keyed by `comment_key` exactly):
===
{focus_text}
===

Output strict JSON only, no extra text or markdown fences."""
		)

		return prompt

	def _parse_credibility_analysis_result(
		self,
		response: str,
		username: str
	) -> Dict[str, Any]:
		"""解析背景分析结果"""
		try:
			import json
			import re

			# 清理响应文本
			response = response.strip()

			# 移除markdown代码块标记
			response = re.sub(r'```json\s*', '', response)
			response = re.sub(r'```\s*$', '', response)
			response = response.strip()

			# 尝试解析JSON
			try:
				result_dict = json.loads(response)
			except json.JSONDecodeError:
				self.logger.error(f"LLM响应不是有效的JSON格式: {response}")
				return {
					"username": username,
					"error": "JSON parsing failed",
					"raw_response": response
				}

			# 验证必需的字段
			required_fields = ["expertise", "background_similarity", "worldview"]
			for field in required_fields:
				if field not in result_dict:
					result_dict[field] = {
						"summary": "",
						"evidence_comments": []
					}

			# 添加用户名和活跃社区信息
			result_dict["username"] = username
			result_dict["profile_url"] = f"https://www.reddit.com/user/{username}"

			# 从分析结果中提取活跃社区（这里可以根据实际需要调整）
			result_dict["subreddits_active_in"] = []  # 这个字段需要从其他地方获取

			# 确保所有字段都有默认的evidence_comments
			for field in ["expertise", "background_similarity", "worldview"]:
				if field in result_dict and "evidence_comments" not in result_dict[field]:
					result_dict[field]["evidence_comments"] = []

				# 确保background_similarity有possible_tags字段
				if field == "background_similarity" and "possible_tags" not in result_dict[field]:
					result_dict[field]["possible_tags"] = []

				# 确保worldview有value_tags字段
				if field == "worldview" and "value_tags" not in result_dict[field]:
					result_dict[field]["value_tags"] = []

			# per_comment 结构（容错）
			if "per_comment" not in result_dict or not isinstance(result_dict.get("per_comment"), dict):
				result_dict["per_comment"] = {}

			return result_dict

		except Exception as e:
			self.logger.error(f"解析背景分析结果失败: {e}")
			return {
				"username": username,
				"error": str(e),
				"raw_response": response
			}













# 创建全局LLM服务实例
llm_service = LLMService()
