#!/usr/bin/env python3
"""
CogBridges Data Persistence Comprehensive Tests
全面测试数据持久化功能

这些测试确保数据的正确存储、检索和管理：
- 数据库操作（CRUD）
- 会话数据持久化
- 用户数据管理
- 积分系统
- 数据一致性
- 事务处理
- 数据迁移
"""

import os
import time
import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

# Configure test environment
os.environ.setdefault("ENABLE_DATABASE", "True")
os.environ.setdefault("TEST_MODE", "True")
if not str(os.environ.get("DATABASE_URL", "")).startswith("postgresql"):
    pytest.skip("PostgreSQL required for comprehensive DB persistence tests", allow_module_level=True)

from services.database_service import DatabaseService
from services.data_service import DataService
from models.database_models import (
    UserAccount, PointsLedger, SearchSession, 
    RedditPost, RedditComment, UserHistory, Base
)
from config import config


class TestDatabaseOperations:
    """数据库基础操作测试"""

    @pytest.fixture
    def db_service(self, db_session):
        """创建数据库服务实例"""
        service = DatabaseService()
        # 确保使用测试数据库会话
        return service

    def test_create_tables(self, db_service):
        """测试创建数据表"""
        # 删除所有表
        try:
            Base.metadata.drop_all(bind=db_service.engine)
        except:
            pass
        
        # 重新创建表
        db_service.create_tables()
        
        # 验证表是否创建成功
        inspector = db_service.engine.dialect.get_table_names(db_service.engine.connect())
        expected_tables = ['user_accounts', 'points_ledger', 'search_sessions', 
                          'reddit_posts', 'reddit_comments', 'user_histories']
        
        for table in expected_tables:
            assert table in inspector or any(table in t for t in inspector)

    def test_database_connection(self, db_service):
        """测试数据库连接"""
        assert db_service.is_available() is True
        
        # 测试执行简单查询
        with db_service.get_session() as session:
            from sqlalchemy import text
            result = session.execute(text("SELECT 1")).scalar()
            assert result == 1

    def test_session_management(self, db_service):
        """测试数据库会话管理"""
        # 测试会话创建和关闭
        session = db_service.get_session()
        assert session is not None
        
        # 测试会话可以执行查询
        from sqlalchemy import text
        result = session.execute(text("SELECT 1")).scalar()
        assert result == 1
        
        # 关闭会话
        session.close()

    def test_transaction_rollback(self, db_service, db_session):
        """测试事务回滚"""
        # 创建一个用户
        user = UserAccount(
            email="<EMAIL>",
            password_hash="hashed_password",
            email_verified=True,
            points_balance=100
        )
        
        try:
            with db_service.get_session() as session:
                session.add(user)
                session.flush()  # 获取ID但不提交
                user_id = user.id
                
                # 故意引发错误
                raise Exception("Test rollback")
                
        except Exception:
            pass
        
        # 验证用户没有被保存
        with db_service.get_session() as session:
            found_user = session.query(UserAccount).filter_by(email="<EMAIL>").first()
            assert found_user is None


class TestUserDataPersistence:
    """用户数据持久化测试"""

    def test_user_registration_persistence(self, db_session):
        """测试用户注册数据持久化"""
        # 创建用户
        user = UserAccount(
            email="<EMAIL>",
            password_hash="hashed_password",
            email_verified=False,
            points_balance=20,  # 初始积分
            created_at=datetime.utcnow()
        )
        
        db_session.add(user)
        db_session.commit()
        
        # 验证用户已保存
        saved_user = db_session.query(UserAccount).filter_by(email="<EMAIL>").first()
        assert saved_user is not None
        assert saved_user.email == "<EMAIL>"
        assert saved_user.points_balance == 20
        assert saved_user.email_verified is False

    def test_user_email_verification_update(self, db_session):
        """测试用户邮箱验证状态更新"""
        # 创建未验证用户
        user = UserAccount(
            email="<EMAIL>",
            password_hash="hashed_password",
            email_verified=False,
            points_balance=20
        )
        
        db_session.add(user)
        db_session.commit()
        user_id = user.id
        
        # 更新验证状态
        user.email_verified = True
        user.email_verified_at = datetime.utcnow()
        db_session.commit()
        
        # 验证更新
        updated_user = db_session.get(UserAccount, user_id)
        assert updated_user.email_verified is True
        assert updated_user.email_verified_at is not None

    def test_user_last_login_update(self, db_session):
        """测试用户最后登录时间更新"""
        user = UserAccount(
            email="<EMAIL>",
            password_hash="hashed_password",
            email_verified=True,
            points_balance=20
        )
        
        db_session.add(user)
        db_session.commit()
        
        # 更新最后登录时间
        login_time = datetime.utcnow()
        user.last_login_at = login_time
        db_session.commit()
        
        # 验证更新
        db_session.refresh(user)
        assert user.last_login_at is not None
        assert abs((user.last_login_at - login_time).total_seconds()) < 1


class TestPointsSystemPersistence:
    """积分系统持久化测试"""

    def test_points_ledger_creation(self, db_session):
        """测试积分账本记录创建"""
        # 创建用户
        user = UserAccount(
            email="<EMAIL>",
            password_hash="hashed_password",
            email_verified=True,
            points_balance=20
        )
        db_session.add(user)
        db_session.commit()
        
        # 创建积分记录
        ledger_entry = PointsLedger(
            user_id=user.id,
            delta=20,
            reason='trial',
            ref='registration',
            meta={'type': 'registration_bonus'},
            created_at=datetime.utcnow()
        )
        
        db_session.add(ledger_entry)
        db_session.commit()
        
        # 验证记录
        saved_entry = db_session.query(PointsLedger).filter_by(user_id=user.id).first()
        assert saved_entry is not None
        assert saved_entry.delta == 20
        assert saved_entry.reason == 'trial'

    def test_points_deduction_and_balance_update(self, db_session):
        """测试积分扣除和余额更新"""
        # 创建有积分的用户
        user = UserAccount(
            email="<EMAIL>",
            password_hash="hashed_password",
            email_verified=True,
            points_balance=50
        )
        db_session.add(user)
        db_session.commit()
        
        # 扣除积分
        search_cost = 10
        user.points_balance -= search_cost
        
        # 记录扣除
        ledger_entry = PointsLedger(
            user_id=user.id,
            delta=-search_cost,
            reason='search',
            ref='search_session_123',
            meta={'type': 'search_cost', 'session_id': 'search_session_123'}
        )
        
        db_session.add(ledger_entry)
        db_session.commit()
        
        # 验证余额和记录
        db_session.refresh(user)
        assert user.points_balance == 40
        
        deduction_entry = db_session.query(PointsLedger).filter_by(
            user_id=user.id, reason='search'
        ).first()
        assert deduction_entry is not None
        assert deduction_entry.delta == -10

    def test_points_purchase_transaction(self, db_session):
        """测试积分购买事务"""
        # 创建用户
        user = UserAccount(
            email="<EMAIL>",
            password_hash="hashed_password",
            email_verified=True,
            points_balance=10
        )
        db_session.add(user)
        db_session.commit()
        
        # 模拟Stripe支付成功，增加积分
        purchase_points = 250
        user.points_balance += purchase_points
        
        # 记录购买
        purchase_entry = PointsLedger(
            user_id=user.id,
            delta=purchase_points,
            reason='purchase',
            ref='stripe_session_abc123',
            meta={
                'type': 'stripe_purchase',
                'stripe_session_id': 'stripe_session_abc123',
                'amount_paid': 2500  # 25.00 USD in cents
            }
        )
        
        db_session.add(purchase_entry)
        db_session.commit()
        
        # 验证购买记录
        db_session.refresh(user)
        assert user.points_balance == 260
        
        purchase_record = db_session.query(PointsLedger).filter_by(
            user_id=user.id, reason='purchase'
        ).first()
        assert purchase_record is not None
        assert purchase_record.delta == 250


class TestSearchSessionPersistence:
    """搜索会话持久化测试"""

    def test_search_session_creation(self, db_session):
        """测试搜索会话创建"""
        # 创建用户
        user = UserAccount(
            email="<EMAIL>",
            password_hash="hashed_password",
            email_verified=True,
            points_balance=50
        )
        db_session.add(user)
        db_session.commit()
        
        # 创建搜索会话
        session = SearchSession(
            id="search_20240101_123456",
            query="Python async programming",
            owner_user_id=user.id,
            success=True,
            reddit_posts_count=3,
            reddit_comments_count=15,
            commenters_count=8,
            llm_analysis_success=True,
            total_time_ms=5200,
            created_at=datetime.utcnow()
        )
        
        db_session.add(session)
        db_session.commit()
        
        # 验证会话
        saved_session = db_session.query(SearchSession).filter_by(
            id="search_20240101_123456"
        ).first()
        assert saved_session is not None
        assert saved_session.query == "Python async programming"
        assert saved_session.owner_user_id == user.id
        assert saved_session.success is True

    def test_reddit_posts_persistence(self, db_session):
        """测试Reddit帖子数据持久化"""
        # 创建搜索会话
        session = SearchSession(
            id="reddit_test_session",
            query="test query",
            success=True,
            created_at=datetime.utcnow()
        )
        db_session.add(session)
        db_session.commit()
        
        # 创建Reddit帖子
        post = RedditPost(
            session_id="reddit_test_session",
            id="abc123",
            title="Test Reddit Post",
            selftext="This is a test post content",
            author="test_author",
            subreddit="Python",
            score=150,
            num_comments=25,
            created_utc=1700000000.0,
            url="https://reddit.com/r/Python/comments/abc123/",
            permalink="/r/Python/comments/abc123/"
        )
        
        db_session.add(post)
        db_session.commit()
        
        # 验证帖子
        saved_post = db_session.query(RedditPost).filter_by(id="abc123", session_id="reddit_test_session").first()
        assert saved_post is not None
        assert saved_post.title == "Test Reddit Post"
        assert saved_post.subreddit == "Python"

    def test_reddit_comments_persistence(self, db_session):
        """测试Reddit评论数据持久化"""
        # 创建搜索会话
        session = SearchSession(
            id="comment_test_session",
            query="test query",
            success=True,
            created_at=datetime.utcnow()
        )
        db_session.add(session)
        db_session.commit()
        
        # 创建Reddit评论
        comment = RedditComment(
            session_id="comment_test_session",
            id="comment123",
            post_id="abc123",
            body="This is a test comment",
            author="commenter",
            score=45,
            created_utc=1700001000.0,
            permalink="/r/Python/comments/abc123/comment123/"
        )
        
        db_session.add(comment)
        db_session.commit()
        
        # 验证评论
        saved_comment = db_session.query(RedditComment).filter_by(
            id="comment123", session_id="comment_test_session"
        ).first()
        assert saved_comment is not None
        assert saved_comment.body == "This is a test comment"
        assert saved_comment.author == "commenter"

    def test_user_history_persistence(self, db_session):
        """测试用户历史数据持久化"""
        # 创建搜索会话
        session = SearchSession(
            id="history_test_session",
            query="test query",
            success=True,
            created_at=datetime.utcnow()
        )
        db_session.add(session)
        db_session.commit()
        
        # 创建用户历史记录
        user_history = UserHistory(
            session_id="history_test_session",
            username="test_user",
            total_comments=150,
            total_posts=20,
            account_created_utc=datetime.utcfromtimestamp(**********),
            subreddits_active=['Python', 'programming', 'learnpython'],
            comments_data=[
                {
                    "body": "Great explanation!",
                    "score": 25,
                    "subreddit": "Python"
                }
            ],
            posts_data=[
                {
                    "title": "How to use async/await",
                    "score": 100,
                    "subreddit": "Python"
                }
            ]
        )
        
        db_session.add(user_history)
        db_session.commit()
        
        # 验证用户历史
        saved_history = db_session.query(UserHistory).filter_by(
            username="test_user"
        ).first()
        assert saved_history is not None
        assert saved_history.total_comments == 150
        assert 'Python' in saved_history.subreddits_active


class TestDataConsistency:
    """数据一致性测试"""

    def test_user_points_consistency(self, db_session):
        """测试用户积分一致性"""
        # 创建用户
        user = UserAccount(
            email="<EMAIL>",
            password_hash="hashed_password",
            email_verified=True,
            points_balance=0
        )
        db_session.add(user)
        db_session.commit()
        
        # 添加多个积分记录
        transactions = [
            (20, 'trial', 'registration'),
            (-5, 'search', 'search_1'),
            (-3, 'search', 'search_2'),
            (250, 'purchase', 'stripe_payment'),
            (-10, 'search', 'search_3')
        ]
        
        expected_balance = 0
        for delta, reason, ref in transactions:
            ledger_entry = PointsLedger(
                user_id=user.id,
                delta=delta,
                reason=reason,
                ref=ref,
                meta={'type': reason}
            )
            db_session.add(ledger_entry)
            expected_balance += delta
        
        # 更新用户余额
        user.points_balance = expected_balance
        db_session.commit()
        
        # 验证一致性
        total_from_ledger = db_session.query(
            db_session.query(PointsLedger.delta).filter_by(user_id=user.id).subquery().c.delta
        ).scalar() or 0
        
        # 计算账本总和
        ledger_sum = sum(
            entry.delta for entry in 
            db_session.query(PointsLedger).filter_by(user_id=user.id).all()
        )
        
        assert user.points_balance == ledger_sum
        assert user.points_balance == expected_balance

    def test_search_session_data_integrity(self, db_session):
        """测试搜索会话数据完整性"""
        # 创建完整的搜索会话数据
        session = SearchSession(
            id="integrity_test_session",
            query="data integrity test",
            success=True,
            reddit_posts_count=2,
            reddit_comments_count=5,
            commenters_count=3,
            created_at=datetime.utcnow()
        )
        db_session.add(session)
        db_session.commit()
        
        # 添加对应的帖子和评论
        posts = []
        comments = []
        
        for i in range(2):  # 2个帖子
            post = RedditPost(
                session_id="integrity_test_session",
                id=f"post_{i}",
                title=f"Post {i}",
                author=f"author_{i}",
                subreddit="test",
                score=10,
                num_comments=2,
                created_utc=1700000000.0
            )
            posts.append(post)
            db_session.add(post)
            
            # 每个帖子2个评论
            for j in range(2):
                comment = RedditComment(
                    session_id="integrity_test_session",
                    id=f"comment_{i}_{j}",
                    post_id=f"post_{i}",
                    body=f"Comment {j} on post {i}",
                    author=f"commenter_{i}_{j}",
                    score=5,
                    created_utc=1700001000.0
                )
                comments.append(comment)
                db_session.add(comment)
        
        # 添加第5个评论（总共5个）
        extra_comment = RedditComment(
            session_id="integrity_test_session",
            id="extra_comment",
            post_id="post_0",
            body="Extra comment",
            author="extra_commenter",
            score=3,
            created_utc=1700002000.0
        )
        db_session.add(extra_comment)
        db_session.commit()
        
        # 验证数据完整性
        saved_session = db_session.query(SearchSession).filter_by(
            id="integrity_test_session"
        ).first()
        
        actual_posts = db_session.query(RedditPost).filter_by(
            session_id="integrity_test_session"
        ).count()
        
        actual_comments = db_session.query(RedditComment).filter_by(
            session_id="integrity_test_session"
        ).count()
        
        assert actual_posts == saved_session.reddit_posts_count
        assert actual_comments == saved_session.reddit_comments_count
