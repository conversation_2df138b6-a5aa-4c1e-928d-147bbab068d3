import React from 'react';

const InsufficientPointsModal = ({ isOpen, onClose, onPurchaseClick, currentPoints = 0 }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Background overlay */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal content */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-xl shadow-xl max-w-md w-full transform transition-all">
          {/* Close button */}
          <button
            onClick={onClose}
            className="absolute top-3 right-3 sm:top-4 sm:right-4 text-gray-400 hover:text-gray-600 transition-colors p-1"
          >
            <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          
          {/* Main content */}
          <div className="p-4 sm:p-6 text-center">
            {/* Icon */}
            <div className="mx-auto flex items-center justify-center h-12 w-12 sm:h-16 sm:w-16 rounded-full bg-yellow-100 mb-3 sm:mb-4">
              <svg className="h-8 w-8 sm:h-10 sm:w-10 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            
            {/* Title */}
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">
              Insufficient Points Balance
            </h3>
            
            {/* Description */}
            <div className="text-gray-600 mb-4 sm:mb-6 space-y-2">
              <p className="text-sm sm:text-base">Each deep search requires <span className="font-semibold text-gray-900">20 points</span></p>
              <p className="text-xs sm:text-sm">
                Your current points balance:
                <span className="font-semibold text-yellow-600 ml-1">{currentPoints} points</span>
              </p>
              <p className="text-xs sm:text-sm mt-2 sm:mt-3">
                Recharge to continue using deep search. Points never expire!
              </p>
            </div>
            
            {/* Action buttons */}
            <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 justify-center">
              <button
                onClick={onPurchaseClick}
                className="px-4 sm:px-6 py-2 sm:py-2.5 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm sm:text-base"
              >
                <span className="flex items-center justify-center gap-2">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
                      d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                  Recharge Now
                </span>
              </button>
              <button
                onClick={onClose}
                className="px-4 sm:px-6 py-2 sm:py-2.5 bg-gray-100 text-gray-700 font-medium rounded-lg hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 text-sm sm:text-base"
              >
                Later
              </button>
            </div>
            
            {/* Additional tip */}
            <p className="text-xs text-gray-500 mt-3 sm:mt-4">
              Multiple payment methods supported, safe and convenient
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InsufficientPointsModal;