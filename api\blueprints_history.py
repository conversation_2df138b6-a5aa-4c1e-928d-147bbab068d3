from flask import Blueprint, jsonify, request
from utils.logger_utils import get_logger
from api.tokens import verify_token as _verify_token
from .app import get_cogbridges_service

logger = get_logger(__name__)

bp_history = Blueprint("history", __name__)


@bp_history.route('/api/history', methods=['GET'])
def get_search_history():
    try:
        service = get_cogbridges_service()
        if not service:
            return jsonify({"success": False, "error": "CogBridges service not initialized"}), 503
        limit = request.args.get('limit', 50, type=int)
        uid = None
        try:
            token = request.cookies.get('auth_token') or (request.headers.get('Authorization') or '').replace('Bearer ', '')
            if token:
                uid = _verify_token(token)
        except Exception:
            uid = None
        if not uid:
            return jsonify({"success": True, "data": [], "total": 0})
        sessions = service.data_service.list_sessions(limit=limit, owner_user_id=uid)
        history_data = []
        for session in sessions:
            history_item = {
                "id": session.get("session_id", ""),
                "query": session.get("query", ""),
                "timestamp": session.get("timestamp", ""),
                "source": session.get("source", "unknown"),
                "success": session.get("success", True),
            }
            if session.get("source") == "database":
                history_item.update({
                    "google_results_count": session.get("google_results_count", 0),
                    "reddit_posts_count": session.get("reddit_posts_count", 0),
                    "reddit_comments_count": session.get("reddit_comments_count", 0),
                    "llm_analysis_success": session.get("llm_analysis_success", False),
                    "llm_analysis_time": session.get("llm_analysis_time", 0.0),
                    "created_at": session.get("created_at", ""),
                    "updated_at": session.get("updated_at", ""),
                })
            # 非数据库来源已移除（PostgreSQL-only）
            history_data.append(history_item)
        return jsonify({"success": True, "data": history_data, "total": len(history_data)})
    except Exception as e:
        logger.error(f"Failed to get search history: {e}")
        return jsonify({"success": False, "error": str(e)}), 500