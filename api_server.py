#!/usr/bin/env python3
"""
CogBridges API Server Startup Script
Standalone API server launcher, does not include frontend startup logic
"""

import sys
import os
import time
import socket
import psutil
from pathlib import Path

# Add project path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from utils.logger_utils import get_logger
from api.app import create_app


class APIServer:
    """API server launcher"""
    
    def __init__(self):
        """Initialize API server"""
        self.logger = get_logger(__name__)
        self.app = None
    
    def check_port(self, port, host="localhost"):
        """Check port occupation"""
        print(f"🔍 Checking port {host}:{port} status...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"⚠️ Port {port} is already in use")
                return False
            else:
                print(f"✅ Port {port} is available")
                return True
                
        except Exception as e:
            self.logger.error(f"Error checking port {port}: {e}")
            print(f"⚠️ Error checking port {port}: {e}")
            return False
    
    def cleanup_port(self, port, host="localhost"):
        """Clean up port occupation"""
        print(f"🧹 Cleaning up port {host}:{port}...")
        
        try:
            # Find processes using the port
            processes_using_port = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    connections = proc.connections()
                    for conn in connections:
                        if conn.laddr.port == port:
                            processes_using_port.append({
                                'pid': proc.pid,
                                'name': proc.name()
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
            if processes_using_port:
                print(f"📋 Found {len(processes_using_port)} process(es) using port {port}:")
                for proc_info in processes_using_port:
                    print(f"   - PID: {proc_info['pid']}, Name: {proc_info['name']}")
                
                # Ask whether to clean port
                try:
                    response = input(f"\n❓ Terminate processes using port {port}? (y/N): ").strip().lower()
                    if response in ['y', 'yes']:
                        killed_count = 0
                        for proc_info in processes_using_port:
                            try:
                                proc = psutil.Process(proc_info['pid'])
                                proc.terminate()
                                proc.wait(timeout=5)
                                print(f"✅ Terminated process PID {proc_info['pid']} ({proc_info['name']})")
                                killed_count += 1
                            except (psutil.NoSuchProcess, psutil.TimeoutExpired, psutil.AccessDenied) as e:
                                print(f"⚠️ Unable to terminate process PID {proc_info['pid']}: {e}")
                        
                        if killed_count > 0:
                            print(f"✅ Cleaned {killed_count} process(es) using the port")
                            time.sleep(2)
                            return True
                        else:
                            print("❌ Failed to clean any processes")
                            return False
                    else:
                        print("⏭️ Skipping port cleanup")
                        return False
                except KeyboardInterrupt:
                    print("\n⏭️ User cancelled, skipping port cleanup")
                    return False
            else:
                print(f"⚠️ Port {port} is occupied but unable to identify the process")
                return False
                
        except Exception as e:
            self.logger.error(f"Error cleaning port {port}: {e}")
            print(f"⚠️ Error cleaning port {port}: {e}")
            return False
    
    def check_dependencies(self):
        """Check dependencies"""
        print("🔍 Checking dependencies...")
        
        required_packages = ['flask', 'flask_cors', 'requests', 'praw', 'asyncpraw', 'tenacity', 'psutil']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ Missing the following dependency packages:")
            for package in missing_packages:
                print(f"   - {package}")
            print(f"\n💡 Please run the following command to install dependencies:")
            print(f"   pip install -r requirements.txt")
            return False
        
        print("✅ All dependencies installed")
        return True
    
    def check_configuration(self):
        """Check configuration"""
        print("🔧 Checking configuration...")
        
        # Check required configuration items
        required_configs = [
            ('REDDIT_CLIENT_ID', config.REDDIT_CLIENT_ID),
            ('REDDIT_CLIENT_SECRET', config.REDDIT_CLIENT_SECRET)
        ]
        
        missing_configs = []
        
        for name, value in required_configs:
            if not value or value == "your_api_key_here":
                missing_configs.append(name)
        
        if missing_configs:
            print(f"❌ Missing the following configurations:")
            for config_name in missing_configs:
                print(f"   - {config_name}")
            print(f"\n💡 Please configure these parameters in config.py")
            return False
        
        # Check LLM service configuration (optional)
        if not config.replicate_configured:
            print("⚠️ Replicate API not configured, LLM analysis features will be unavailable")
        else:
            print("✅ LLM service configuration check passed")
        
        print("✅ Configuration check passed")
        return True
    
    def start_server(self):
        """Start API server"""
        print("🚀 Starting CogBridges API server...")
        
        # Check port
        if not self.check_port(config.PORT, config.HOST):
            if not self.cleanup_port(config.PORT, config.HOST):
                print(f"❌ Port {config.PORT} unavailable, startup failed")
                return False
        
        try:
            # Create Flask application
            self.app = create_app()
            
            print(f"📍 Starting API server: {config.HOST}:{config.PORT}")
            print("=" * 60)
            print("🎉 CogBridges API server started successfully!")
            print("=" * 60)
            print(f"🔗 API address: http://{config.HOST}:{config.PORT}")
            print(f"📚 Health check: http://{config.HOST}:{config.PORT}/api/health")
            print(f"🔍 Test endpoint: http://{config.HOST}:{config.PORT}/test")
            print(f"📊 Status query: http://{config.HOST}:{config.PORT}/api/status")
            print("-" * 60)
            print("💡 Available API endpoints:")
            print("  POST /api/search     - Execute search")
            print("  GET  /api/history    - Get search history")
            # Removed bookmark-related endpoints
            print("-" * 60)
            print("🛑 Press Ctrl+C to stop service")
            print("=" * 60)
            
            # Start Flask application
            self.app.run(
                host=config.HOST,
                port=config.PORT,
                debug=False,
                threaded=True
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"API server startup failed: {e}")
            print(f"❌ API server startup failed: {e}")
            return False
    
    def run(self):
        """Run API server"""
        print("🌟 CogBridges API Server")
        print("=" * 60)
        
        # Check dependencies and configuration
        if not self.check_dependencies():
            return False
        
        if not self.check_configuration():
            return False
        
        # Start server
        return self.start_server()


def main():
    """Main function"""
    server = APIServer()
    
    try:
        success = server.run()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 User interrupted, stopping API server...")
    except Exception as e:
        print(f"❌ API server startup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 