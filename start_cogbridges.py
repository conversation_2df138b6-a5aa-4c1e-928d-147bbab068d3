#!/usr/bin/env python3
"""
CogBridges - Unified Startup Script
Single startup script to launch both backend API server and frontend web interface
"""

import sys
import os
import subprocess
import threading
import time
import webbrowser
from pathlib import Path
import signal
import atexit
import socket
import psutil

# Add project path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from utils.logger_utils import get_logger
from api.app import create_app, get_cogbridges_service


class CogBridgesLauncher:
    """CogBridges unified launcher"""
    
    def __init__(self):
        """Initialize launcher"""
        self.logger = get_logger(__name__)
        self.backend_process = None
        self.frontend_server = None
        self.frontend_process = None  # React frontend process
        self.cogbridges_service = None  # CogBridges service instance
        self.running = False
        
        # Register exit handler
        atexit.register(self.cleanup)

        # Only register signal handlers in main thread
        try:
            import threading
            if threading.current_thread() is threading.main_thread():
                signal.signal(signal.SIGINT, self.signal_handler)
                signal.signal(signal.SIGTERM, self.signal_handler)
                self.logger.debug("Signal handlers registered in main thread")
            else:
                self.logger.debug("Not main thread, skipping signal handler registration")
        except Exception as e:
            self.logger.warning(f"Signal handler registration failed: {e}")
    
    def check_and_clean_port(self, port, host="localhost"):
        """Check port occupation and clean if needed"""
        print(f"🔍 Checking port {host}:{port} status...")
        
        try:
            # Check if port is occupied
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"⚠️ Port {port} is already in use, searching for occupying process...")
                
                # Find processes using the port
                processes_using_port = []
                for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                    try:
                        connections = proc.connections()
                        for conn in connections:
                            if conn.laddr.port == port:
                                processes_using_port.append({
                                    'pid': proc.pid,
                                    'name': proc.name(),
                                    'cmdline': ' '.join(proc.cmdline()) if proc.cmdline() else ''
                                })
                    except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                        continue
                
                if processes_using_port:
                    print(f"📋 Found {len(processes_using_port)} process(es) using port {port}:")
                    for proc_info in processes_using_port:
                        print(f"   - PID: {proc_info['pid']}, Name: {proc_info['name']}")
                        if proc_info['cmdline']:
                            print(f"     Command: {proc_info['cmdline'][:100]}...")
                    
                    # Ask whether to clean port
                    try:
                        response = input(f"\n❓ Terminate processes using port {port}? (y/N): ").strip().lower()
                        if response in ['y', 'yes']:
                            killed_count = 0
                            for proc_info in processes_using_port:
                                try:
                                    proc = psutil.Process(proc_info['pid'])
                                    proc.terminate()
                                    proc.wait(timeout=5)
                                    print(f"✅ Terminated process PID {proc_info['pid']} ({proc_info['name']})")
                                    killed_count += 1
                                except (psutil.NoSuchProcess, psutil.TimeoutExpired, psutil.AccessDenied) as e:
                                    print(f"⚠️ Unable to terminate process PID {proc_info['pid']}: {e}")
                            
                            if killed_count > 0:
                                print(f"✅ Cleaned {killed_count} process(es) using the port")
                                # Wait for port to be released
                                time.sleep(2)
                                return True
                            else:
                                print("❌ Failed to clean any processes")
                                return False
                        else:
                            print("⏭️ Skipping port cleanup")
                            return False
                    except KeyboardInterrupt:
                        print("\n⏭️ User cancelled, skipping port cleanup")
                        return False
                else:
                    print(f"⚠️ Port {port} is occupied but unable to identify the process")
                    return False
            else:
                print(f"✅ Port {port} is available")
                return True
                
        except Exception as e:
            self.logger.error(f"Error checking port {port}: {e}")
            print(f"⚠️ Error checking port {port}: {e}")
            return False

    def _get_local_ipv4(self) -> str:
        """Get local LAN IPv4 address for showing mobile-accessible URL
        Priority: env var LAN_IP → RFC1918 private ranges (192.168/10/172.16-31), excluding 169.254/198.18, etc.
        """
        env_ip = os.getenv("LAN_IP")
        if env_ip:
            return env_ip
        try:
            import psutil, ipaddress
            candidates = []
            for _name, addrs in psutil.net_if_addrs().items():
                for addr in addrs:
                    if getattr(socket, 'AF_INET', 2) == addr.family:
                        ip = addr.address
                        try:
                            ip_obj = ipaddress.ip_address(ip)
                            if ip_obj.is_private and not ip_obj.is_loopback:
                                if not ip.startswith("169.254.") and not ip.startswith("198.18."):
                                    candidates.append(ip)
                        except Exception:
                            continue
            if candidates:
                def priority(ip: str):
                    if ip.startswith("192.168."):
                        return (0, ip)
                    if ip.startswith("10."):
                        return (1, ip)
                    if ip.startswith("172."):
                        try:
                            second = int(ip.split('.')[1])
                            if 16 <= second <= 31:
                                return (2, ip)
                        except Exception:
                            pass
                    return (9, ip)
                candidates.sort(key=priority)
                return candidates[0]
        except Exception:
            pass
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            try:
                sock.connect(("*******", 80))
                ip = sock.getsockname()[0]
            finally:
                sock.close()
            import ipaddress
            try:
                ip_obj = ipaddress.ip_address(ip)
                if ip_obj.is_private and not ip.startswith("169.254.") and not ip.startswith("198.18."):
                    return ip
            except Exception:
                pass
        except Exception:
            pass
        try:
            return socket.gethostbyname(socket.gethostname())
        except Exception:
            return "127.0.0.1"
    
    def check_dependencies(self):
        """Check dependencies"""
        print("🔍 Checking dependencies...")
        
        # Define package names and their import names
        package_mappings = {
            'flask': 'flask',
            'flask_cors': 'flask_cors', 
            'requests': 'requests',
            'praw': 'praw',
            'asyncpraw': 'asyncpraw',
            'tenacity': 'tenacity',
            'python-dotenv': 'dotenv',
            'psutil': 'psutil'  # Add psutil dependency
        }
        
        missing_packages = []
        
        for package, import_name in package_mappings.items():
            try:
                __import__(import_name)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ Missing the following dependency packages:")
            for package in missing_packages:
                print(f"   - {package}")
            print(f"\n💡 Please run the following command to install dependencies:")
            print(f"   pip install -r requirements.txt")
            return False
        
        print("✅ All dependencies installed")
        return True
    
    def check_configuration(self):
        """Check configuration"""
        print("🔧 Checking configuration...")
        
        # Check required configuration items
        required_configs = [
            ('REDDIT_CLIENT_ID', config.REDDIT_CLIENT_ID),
            ('REDDIT_CLIENT_SECRET', config.REDDIT_CLIENT_SECRET)
        ]
        
        missing_configs = []
        
        for name, value in required_configs:
            if not value or value == "your_api_key_here":
                missing_configs.append(name)
        
        if missing_configs:
            print(f"❌ Missing the following configurations:")
            for config_name in missing_configs:
                print(f"   - {config_name}")
            print(f"\n💡 Please configure these parameters in config.py")
            return False
        
        # Check LLM service configuration (optional)
        if not config.replicate_configured:
            print("⚠️ Replicate API not configured, LLM analysis features will be unavailable")
        else:
            print("✅ LLM service configuration check passed")
        
        # Check logging configuration
        print("📝 Logging configuration:")
        print(f"   - Log level: {config.LOG_LEVEL}")
        print(f"   - Detailed log saving: {'✅ Enabled' if config.SAVE_DETAILED_LOGS else '❌ Disabled'}")
        print(f"   - JSON log format: {'✅ Enabled' if config.ENABLE_JSON_LOGS else '❌ Disabled'}")
        print(f"   - Log directory: {config.LOGS_DIR}")
        
        # Check database configuration (PostgreSQL-only)
        print("💾 Database configuration:")
        if config.database_configured and config.ENABLE_DATABASE:
            print(f"   - Database: ✅ Enabled & Configured")
            print(f"   - Database URL: {config.database_url[:50]}..." if len(config.database_url) > 50 else f"   - Database URL: {config.database_url}")
            print(f"   - Pool size: {config.DB_POOL_SIZE}")
            print(f"   - Max overflow: {config.DB_MAX_OVERFLOW}")
            print(f"   - Connection timeout: {config.DB_POOL_TIMEOUT}s")
        else:
            print(f"   - Database: ❌ Not configured (PostgreSQL required)")
        
        # Check Grok raw response saving configuration
        print("📄 Grok raw response saving configuration:")
        grok_raw_dir = config.DATA_DIR / "grok_raw_responses"
        print(f"   - Raw response saving: ✅ Enabled")
        print(f"   - Save directory: {grok_raw_dir}")
        print(f"   - File format: JSON")
        print(f"   - Content includes: Complete API response, query info, timestamp")

        # Add: Business pipeline configuration hint (consistent with current implementation)
        print("🧩 Business pipeline:")
        print("   1) Grok performs search only (search_only), returns citations")
        print("   2) Reddit API fetches real posts and comments based on citations")
        print("   3) Fetch commenter history (overview) in parallel")
        print("   4) Use LLM to perform commenter background analysis (optional, requires Replicate config)")
        
        print("✅ Configuration check passed")
        return True
    
    def start_backend_api(self):
        """Start backend API server"""
        print("🚀 Starting backend API server...")
        
        try:
            # Use standalone API server startup script
            from api.app import create_app
            
            # Check and clean port occupation
            if not self.check_and_clean_port(config.PORT, config.HOST):
                print(f"⚠️ Port {config.PORT} cleanup failed, trying alternative port")
                config.PORT = config.PORT + 10
                print(f"🔄 Using port: {config.PORT}")
                # Check new port again
                if not self.check_and_clean_port(config.PORT, config.HOST):
                    print(f"❌ Port {config.PORT} also unavailable, startup failed")
                    return False
            
            # Create Flask application
            app = create_app()
            
            # Start Flask app in separate thread
            def run_flask():
                try:
                    print(f"🔧 Flask configuration: host={config.HOST}, port={config.PORT}")
                    app.run(
                        host=config.HOST,
                        port=config.PORT,
                        debug=False,  # Disable debug mode to avoid duplicate startup
                        threaded=True,
                        use_reloader=False  # Disable reloader to avoid thread conflicts
                    )
                except Exception as e:
                    print(f"❌ Flask application startup failed: {e}")
                    import traceback
                    traceback.print_exc()
            
            backend_thread = threading.Thread(target=run_flask, daemon=True)
            backend_thread.start()
            
            # Wait for server to start
            print("⏳ Waiting for Flask server to start...")
            time.sleep(8)  # Increase wait time
            
            # Test if API is working properly
            import requests
            
            # First test simple route
            try:
                print(f"🔍 Testing simple route: http://{config.HOST}:{config.PORT}/test")
                test_response = requests.get(f"http://{config.HOST}:{config.PORT}/test", timeout=10)
                print(f"📡 Simple route response status code: {test_response.status_code}")
                if test_response.status_code == 200:
                    print(f"📄 Simple route response content: {test_response.text}")
                    print("✅ Simple route test successful")
                else:
                    print(f"❌ Simple route response abnormal: {test_response.status_code}")
                    print(f"📄 Response content: {test_response.text[:200]}...")
            except requests.RequestException as e:
                print(f"❌ Simple route connection failed: {e}")
                return False
            
            # Then test health check route
            try:
                print(f"🔍 Testing API connection: http://{config.HOST}:{config.PORT}/api/health")
                response = requests.get(f"http://{config.HOST}:{config.PORT}/api/health", timeout=10)
                print(f"📡 API response status code: {response.status_code}")
                if response.status_code == 200:
                    print(f"✅ Backend API server started successfully: http://{config.HOST}:{config.PORT}")
                    return True
                else:
                    print(f"❌ Backend API server response abnormal: {response.status_code}")
                    print(f"📄 Response content: {response.text[:200]}...")
                    return False
            except requests.RequestException as e:
                print(f"❌ Backend API server connection failed: {e}")
                print(f"💡 Possible reasons: port occupied, firewall blocking, or server startup failure")
                return False
                
        except Exception as e:
            self.logger.error(f"Backend API server startup failed: {e}")
            print(f"❌ Backend API server startup failed: {e}")
            return False
    
    def start_frontend_server(self):
        """Start React frontend development server"""
        print("🌐 Starting React frontend development server...")
        
        try:
            import subprocess
            import sys
            import os
            
            # Set frontend directory
            frontend_dir = project_root / "frontend"
            if not frontend_dir.exists():
                print(f"❌ React frontend directory does not exist: {frontend_dir}")
                print("💡 Please first run 'npm create vite@latest frontend -- --template react' to create frontend project")
                return False
            
            # Check if package.json exists
            package_json = frontend_dir / "package.json"
            if not package_json.exists():
                print(f"❌ Frontend project not initialized: {package_json}")
                print("💡 Please first run 'npm install' in frontend directory to install dependencies")
                return False
            
            # Check if node_modules exists
            node_modules = frontend_dir / "node_modules"
            if not node_modules.exists():
                print("📦 Detected frontend dependencies not installed, installing...")
                try:
                    result = subprocess.run(
                        "npm install",
                        cwd=str(frontend_dir),
                        capture_output=True,
                        text=True,
                        timeout=120,
                        shell=True
                    )
                    if result.returncode != 0:
                        print(f"❌ Frontend dependency installation failed: {result.stderr}")
                        return False
                    print("✅ Frontend dependencies installed")
                except subprocess.TimeoutExpired:
                    print("❌ Frontend dependency installation timeout")
                    return False
                except Exception as e:
                    print(f"❌ Frontend dependency installation failed: {e}")
                    return False
            
            # Frontend port is API port + 1
            frontend_port = config.PORT + 1
            
            # Check and clean frontend port occupation
            if not self.check_and_clean_port(frontend_port, "localhost"):
                print(f"⚠️ Frontend port {frontend_port} cleanup failed, trying alternative port")
                frontend_port = frontend_port + 1
                print(f"🔄 Using frontend port: {frontend_port}")
                # Check new port again
                if not self.check_and_clean_port(frontend_port, "localhost"):
                    print(f"⚠️ Frontend port {frontend_port} also unavailable, skipping frontend server startup")
                    return True  # Return True to avoid blocking overall startup
            
            def run_frontend_server():
                try:
                    # Start Vite development server
                    env = os.environ.copy()
                    local_ip = self._get_local_ipv4()
                    env['VITE_API_URL'] = f"http://{local_ip}:{config.PORT}"
                    
                    # Use shell=True to ensure npm command can be found
                    cmd = f"npm run dev -- --port {frontend_port} --host"
                    process = subprocess.Popen(
                        cmd,
                        cwd=str(frontend_dir),
                        env=env,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.STDOUT,
                        text=True,
                        shell=True
                    )
                    
                    self.frontend_process = process
                    print(f"✅ React frontend development server started successfully: http://localhost:{frontend_port}")
                    print(f"📱 LAN access (mobile/other devices): http://{local_ip}:{frontend_port}")
                    
                    # Wait for process to end
                    while self.running and process.poll() is None:
                        time.sleep(1)
                        
                except Exception as e:
                    print(f"⚠️ React frontend server startup failed: {e}")
            
            frontend_thread = threading.Thread(target=run_frontend_server, daemon=True)
            frontend_thread.start()
            
            # Wait for server to start
            time.sleep(3)
            return True
            
        except Exception as e:
            self.logger.error(f"React frontend server startup failed: {e}")
            print(f"❌ React frontend server startup failed: {e}")
            return False
    
    def open_browser(self):
        """Open browser"""
        frontend_port = config.PORT + 1
        local_ip = self._get_local_ipv4()
        url = f"http://localhost:{frontend_port}"
        
        print(f"🌐 Opening browser: {url}")
        
        try:
            # Check if frontend service is available
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(("localhost", frontend_port))
            sock.close()
            
            if result == 0:
                webbrowser.open(url)
                print("✅ Browser opened")
            else:
                print(f"⚠️ Frontend server not started, please manually visit: {url}")
        except Exception as e:
            print(f"⚠️ Unable to automatically open browser: {e}")
            print(f"💡 Please manually visit: {url}")
        print(f"📱 Mobile/other devices can access: http://{local_ip}:{frontend_port}")
    
    def display_startup_info(self):
        """Display startup information"""
        print("\n" + "=" * 60)
        print("🎉 CogBridges started successfully!")
        print("=" * 60)
        local_ip = self._get_local_ipv4()
        print(f"🔗 Backend API address: http://{config.HOST}:{config.PORT}")
        print(f"📱 Backend LAN address: http://{local_ip}:{config.PORT}")
        print(f"🌐 Frontend Web address: http://localhost:{config.PORT + 1}")
        print(f"📱 Frontend LAN address: http://{local_ip}:{config.PORT + 1}")
        print(f"📚 Health check: http://{config.HOST}:{config.PORT}/api/health")
        print("-" * 60)
        print("💡 Usage instructions:")
        print("  1. Enter search query in web interface")
        print("  2. Flow: Grok (search only, get citations) → Reddit API fetch real posts/comments")
        print("  3. Fetch commenter history (overview) in parallel")
        print("  4. Use LLM to perform commenter background analysis (if Replicate configured)")
        print("  5. View complete analysis results and user profiles")
        print("-" * 60)
        print("🧠 LLM analysis features:")
        if config.replicate_configured:
            print("  ✅ Commenter background analysis: Enabled")
        else:
            print("  ⚠️ Commenter background analysis: Replicate API not configured")
        print("-" * 60)
        print("📊 Data storage configuration (PostgreSQL-only):")
        print(f"  📝 Log level: {config.LOG_LEVEL}")
        print(f"  📄 Detailed logs: {'✅ Enabled' if config.SAVE_DETAILED_LOGS else '❌ Disabled'}")
        print(f"  📄 JSON logs: {'✅ Enabled' if config.ENABLE_JSON_LOGS else '❌ Disabled'}")
        print(f"  💾 Database storage: {'✅ Enabled' if config.ENABLE_DATABASE else '❌ Disabled'}")
        print(f"  📄 Grok raw response: ✅ Enabled")
        print("-" * 60)
        print("🛑 Press Ctrl+C to stop service")
        print("=" * 60)
    
    def run(self):
        """Run launcher"""
        print("🌟 CogBridges Unified Launcher")
        print("=" * 60)
        
        # Check dependencies and configuration
        if not self.check_dependencies():
            return False
        
        if not self.check_configuration():
            return False
        
        # Create Flask application
        flask_app, self.cogbridges_service = self.start_backend_api_direct()
        if not flask_app:
            return False
        
        # Start frontend web server
        if not self.start_frontend_server():
            return False
        
        # Display startup information
        self.display_startup_info()
        
        # Open browser
        self.open_browser()
        
        # Start Flask application
        print(f"🚀 Starting Flask server: {config.HOST}:{config.PORT}")
        print("⏳ Flask application starting soon, please wait...")
        
        try:
            flask_app.run(
                host=config.HOST,
                port=config.PORT,
                debug=False,  # Disable debug mode to avoid duplicate startup
                threaded=True
            )
        except Exception as e:
            print(f"❌ Flask application run failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
    
    def start_backend_api_direct(self):
        """Start backend API server directly (non-threaded mode)"""
        print("🚀 Starting backend API server (direct mode)...")
        
        try:
            # Create Flask application and CogBridges service
            app = create_app()
            service = get_cogbridges_service()
            return app, service
                
        except Exception as e:
            self.logger.error(f"Backend API server startup failed: {e}")
            print(f"❌ Backend API server startup failed: {e}")
            return None, None
    
    def cleanup(self):
        """Clean up resources"""
        print("\n🧹 Cleaning up resources...")
        self.running = False
        
        # Clean up CogBridges service
        if self.cogbridges_service:
            try:
                # Use asyncio.run to run async close method
                import asyncio
                asyncio.run(self.cogbridges_service.close())
                print("✅ CogBridges service cleaned up")
            except Exception as e:
                print(f"⚠️ Error cleaning up CogBridges service: {e}")
        
        # Clean up React frontend process
        if hasattr(self, 'frontend_process') and self.frontend_process:
            try:
                # Terminate React development server process
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ React frontend server stopped")
            except Exception as e:
                print(f"⚠️ Error closing React frontend server: {e}")
        
        # Clean up old frontend server (compatibility)
        if hasattr(self, 'frontend_server') and self.frontend_server:
            try:
                self.frontend_server.shutdown()
                self.frontend_server.server_close()
                print("✅ Old frontend server stopped")
            except Exception as e:
                print(f"⚠️ Error closing old frontend server: {e}")
        
        # Clean up backend process
        if hasattr(self, 'backend_process') and self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ Backend process stopped")
            except Exception as e:
                print(f"⚠️ Error closing backend process: {e}")
        
        print("👋 CogBridges stopped")
    
    def signal_handler(self, signum, frame):
        """Signal handler"""
        print(f"\n📡 Received signal {signum}, stopping service...")
        
        # Set up timeout cleanup
        import threading
        cleanup_thread = threading.Thread(target=self.cleanup, daemon=True)
        cleanup_thread.start()
        
        # Wait for cleanup to complete or timeout
        cleanup_thread.join(timeout=3)
        
        # Force exit
        print("🚪 Force exit...")
        os._exit(0)


def main():
    """Main function"""
    launcher = CogBridgesLauncher()
    
    try:
        success = launcher.run()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 User interrupted, stopping...")
        launcher.cleanup()
    except Exception as e:
        print(f"❌ Startup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
