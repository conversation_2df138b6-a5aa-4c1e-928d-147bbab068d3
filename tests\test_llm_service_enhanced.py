import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from services.llm_service import LLMService


class TestLLMServiceEnhanced:
    """Enhanced tests for LLM service focusing on core functionality"""

    @pytest.fixture
    def llm_service(self):
        with patch('services.llm_service.config') as mock_config:
            mock_config.replicate_configured = True
            mock_config.REPLICATE_API_TOKEN = "test_token"
            mock_config.REPLICATE_MODEL = "test_model"
            with patch('services.llm_service.replicate') as mock_replicate:
                mock_client = Mock()
                mock_replicate.Client.return_value = mock_client
                service = LLMService()
                service.client = mock_client
                return service

    @pytest.fixture
    def mock_commenter_history(self):
        return {
            'user1': {
                '_metadata': {
                    'subreddits': ['Python', 'programming'],
                    'total_comments': 100,
                    'total_posts': 10,
                    'account_age_days': 365
                },
                'comments': [
                    {
                        'body': 'Great explanation of async programming!',
                        'score': 25,
                        'subreddit': 'Python'
                    }
                ],
                'posts': [
                    {
                        'title': 'How to use async/await',
                        'score': 50,
                        'subreddit': 'Python'
                    }
                ]
            }
        }

    @pytest.fixture
    def mock_llm_response(self):
        return {
            'commenter_profiles': {
                'user1': {
                    'expertise_level': 'expert',
                    'credibility_score': 0.9,
                    'specialization': 'Python programming',
                    'background': 'Experienced developer'
                }
            },
            'overall_analysis': {
                'discussion_quality': 'high',
                'expertise_distribution': 'varied',
                'key_insights': ['Strong technical knowledge', 'Active community member']
            }
        }

    @pytest.mark.asyncio
    async def test_generate_text_success(self, llm_service, mock_llm_response):
        """Test successful text generation"""
        with patch('asyncio.to_thread') as mock_to_thread:
            mock_to_thread.return_value = json.dumps(mock_llm_response)

            result = await llm_service.generate_text("test prompt")

            assert result is not None
            assert isinstance(result, str)
            mock_to_thread.assert_called()

    @pytest.mark.asyncio
    async def test_generate_text_not_configured(self):
        """Test text generation when service not configured"""
        with patch('services.llm_service.config') as mock_config:
            mock_config.replicate_configured = False
            service = LLMService()

            with pytest.raises(ValueError, match="Replicate API未配置"):
                await service.generate_text("test prompt")

    @pytest.mark.asyncio
    async def test_generate_text_with_system_prompt(self, llm_service):
        """Test text generation with system prompt"""
        with patch('asyncio.to_thread') as mock_to_thread:
            mock_to_thread.return_value = "Generated text"

            result = await llm_service.generate_text(
                "test prompt",
                system_prompt="You are a helpful assistant"
            )

            assert result is not None
            mock_to_thread.assert_called()

    @pytest.mark.asyncio
    async def test_generate_text_with_custom_params(self, llm_service):
        """Test text generation with custom parameters"""
        with patch('asyncio.to_thread') as mock_to_thread:
            mock_to_thread.return_value = "Generated text"

            result = await llm_service.generate_text(
                "test prompt",
                max_completion_tokens=8000,
                reasoning_effort="high"
            )

            assert result is not None
            mock_to_thread.assert_called()

    @pytest.mark.asyncio
    async def test_generate_text_failure(self, llm_service):
        """Test text generation failure"""
        with patch('asyncio.to_thread') as mock_to_thread:
            mock_to_thread.side_effect = Exception("API Error")

            with pytest.raises(Exception):
                await llm_service.generate_text("test prompt")

    def test_service_initialization_configured(self):
        """Test LLM service initialization when configured"""
        with patch('services.llm_service.config') as mock_config:
            mock_config.replicate_configured = True
            mock_config.REPLICATE_API_TOKEN = "test_token"
            with patch('services.llm_service.replicate') as mock_replicate:
                mock_replicate.Client.return_value = Mock()

                service = LLMService()

                assert service.configured is True
                assert service.client is not None
                assert service.request_count == 0

    def test_service_initialization_not_configured(self):
        """Test LLM service initialization when not configured"""
        with patch('services.llm_service.config') as mock_config:
            mock_config.replicate_configured = False

            service = LLMService()

            assert service.configured is False

    @pytest.mark.asyncio
    async def test_generate_text_json_response(self, llm_service):
        """Test text generation with JSON response format"""
        with patch('asyncio.to_thread') as mock_to_thread:
            mock_to_thread.return_value = '{"result": "success"}'

            result = await llm_service.generate_text(
                "test prompt",
                force_json=True
            )

            assert result is not None
            assert '"result"' in result

    @pytest.mark.asyncio
    async def test_generate_text_retry_mechanism(self, llm_service):
        """Test text generation retry mechanism"""
        with patch('asyncio.to_thread') as mock_to_thread:
            # First call fails, second succeeds
            mock_to_thread.side_effect = [Exception("Temporary error"), "Success"]

            result = await llm_service.generate_text("test prompt")

            assert result == "Success"
            assert mock_to_thread.call_count == 2

    def test_request_statistics(self, llm_service):
        """Test request statistics tracking"""
        assert hasattr(llm_service, 'request_count')
        assert hasattr(llm_service, 'total_request_time')
        assert llm_service.request_count == 0
        assert llm_service.total_request_time == 0.0

    @pytest.mark.asyncio
    async def test_generate_text_with_cancel_event(self, llm_service):
        """Test text generation with cancel event"""
        cancel_event = Mock()

        with patch('asyncio.to_thread') as mock_to_thread:
            mock_to_thread.return_value = "Generated text"

            result = await llm_service.generate_text(
                "test prompt",
                cancel_event=cancel_event
            )

            assert result is not None

    def test_service_logger(self, llm_service):
        """Test that service has proper logger"""
        assert hasattr(llm_service, 'logger')
        assert llm_service.logger is not None

    @pytest.mark.asyncio
    async def test_generate_text_parameter_validation(self, llm_service):
        """Test parameter validation in text generation"""
        # Test with various parameter combinations
        test_cases = [
            {"max_completion_tokens": 1000},
            {"reasoning_effort": "medium"},
            {"model": "custom_model"},
            {"system_prompt": "Custom system prompt"}
        ]

        with patch('asyncio.to_thread') as mock_to_thread:
            mock_to_thread.return_value = "Generated text"

            for params in test_cases:
                result = await llm_service.generate_text("test prompt", **params)
                assert result is not None
