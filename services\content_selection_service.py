"""
Content Selection Service
使用 Replicate 的 GPT 模型，从抓取到的真实 Reddit 帖子/评论中，筛选出与查询最相关的前 N 条评论。
"""

from typing import List, Dict, Any
import json

from utils.logger_utils import get_logger
from services.llm_service import llm_service
from config import config


class ContentSelectionService:
    """基于 LLM 的内容筛选服务"""

    def __init__(self) -> None:
        self.logger = get_logger(__name__)

    def _local_topk_fallback(
        self,
        posts: List[Dict[str, Any]],
        max_comments: int,
        max_per_post: int,
    ) -> List[Dict[str, Any]]:
        """当 LLM 解析失败或返回异常时，本地确定性 Top-K 裁剪。

        规则：
        - 跨帖子按评论 score 降序挑选前 max_comments 条
        - 同时对每个帖子最多保留 max_per_post 条（保证多样性）
        """
        try:
            # 扁平化并按分数降序
            flat: List[Dict[str, Any]] = []
            for pi, p in enumerate(posts):
                for c in (p.get("comments", []) or []):
                    flat.append({
                        "pi": pi,
                        "score": c.get("score", 0) or 0,
                        "comment": c,
                    })

            if not flat:
                return posts

            flat.sort(key=lambda x: x["score"], reverse=True)

            kept_per_post: Dict[int, int] = {}
            new_posts: List[Dict[str, Any]] = []
            for p in posts:
                new_posts.append({
                    "success": True,
                    "post": p.get("post", {}),
                    "comments": [],
                    "commenters": []
                })

            kept_total = 0
            for item in flat:
                if kept_total >= int(max_comments):
                    break
                pi = item["pi"]
                if kept_per_post.get(pi, 0) >= int(max_per_post):
                    continue
                c = item["comment"]
                new_posts[pi]["comments"].append(c)
                author = c.get("author")
                if author and author != "[deleted]" and author not in new_posts[pi]["commenters"]:
                    new_posts[pi]["commenters"].append(author)
                kept_per_post[pi] = kept_per_post.get(pi, 0) + 1
                kept_total += 1

            # 去除无评论的帖子
            trimmed = [p for p in new_posts if p.get("comments")] or posts
            self.logger.info(
                f"内容筛选使用本地Top-K兜底: -> 保留 {sum(len(p.get('comments', [])) for p in trimmed)} 条"
            )
            return trimmed
        except Exception:
            # 兜底失败则保持原数据
            return posts

    async def select_relevant_comments(
        self,
        posts: List[Dict[str, Any]],
        query: str,
        max_comments: int = 10,
    ) -> List[Dict[str, Any]]:
        """选出与查询最相关的前 N 条评论，并按帖子分组返回精简后的 posts 结构。

        - 若 LLM 未配置或调用失败，直接返回原 posts
        - 输入 posts 结构：[{ post: {...}, comments: [{...}], commenters: [...] }]
        - 输出：仅保留入选评论的帖子/评论；未包含入选评论的帖子将被剔除
        """
        try:
            if not posts or not query:
                return posts
            if not llm_service or not llm_service.configured:
                return posts

            # 扁平化所有评论，构建待排序列表
            flat_comments: List[Dict[str, Any]] = []
            for pi, p in enumerate(posts):
                post = p.get("post", {})
                for ci, c in enumerate(p.get("comments", []) or []):
                    key = c.get("permalink") or c.get("id") or f"p{pi}_c{ci}"
                    flat_comments.append({
                        "key": key,
                        "post_index": pi,
                        "post_title": post.get("title", ""),
                        "post_url": post.get("url", ""),
                        "subreddit": post.get("subreddit", ""),
                        "comment_body": (c.get("body", "") or "")[:1000],
                        "comment_author": c.get("author", ""),
                        "comment_score": c.get("score", 0),
                        "comment_created_utc": c.get("created_utc"),
                        "comment_permalink": c.get("permalink", ""),
                    })

            if not flat_comments:
                return posts

            # 全量送入 LLM：按分数降序，不做输入体量限制
            flat_comments.sort(key=lambda it: it.get("comment_score", 0), reverse=True)
            scan_items = flat_comments
            max_scan = len(scan_items)

            # 记录一次输入规模，便于排查成本与时延
            try:
                self.logger.info(
                    f"内容筛选输入条目: 总数 {len(flat_comments)}，送入LLM {max_scan}，目标TopN {int(max_comments)}"
                )
            except Exception:
                pass

            system_prompt = (
                "You are a ranking and selection engine. Given a query and a list of Reddit comments "
                "with minimal context, select the top-N most relevant comments strictly based on semantic relevance to the query. "
                "IMPORTANT: Return the selected comments ordered by relevance score from highest to lowest. "
                "Return strict JSON only. Do not invent content or usernames."
            )

            payload = {
                "query": query,
                "max_comments": int(max_comments),
                "comments": [
                    {
                        "key": it["key"],
                        "post_title": it["post_title"],
                        "subreddit": it["subreddit"],
                        "body": it["comment_body"],
                        "score": it["comment_score"],
                    }
                    for it in scan_items
                ],
            }

            # 指定输出格式
            schema_hint = {
                "type": "object",
                "properties": {
                    "selected": {
                        "type": "array",
                        "description": "Array of selected comments, MUST be ordered by relevance score from highest to lowest",
                        "items": {
                            "type": "object",
                            "properties": {
                                "key": {"type": "string"},
                                "relevance": {"type": ["number", "integer"], "description": "Relevance score from 0 to 1"},
                                "reason": {"type": "string"}
                            },
                            "required": ["key", "relevance"]
                        }
                    }
                },
                "required": ["selected"]
            }

            # 使用 f-string，避免与 JSON 花括号冲突的 format 调用
            user_prompt = (
                f"Select the top {max_comments} most relevant comments to the query.\n"
                "CRITICAL: The output MUST be ordered by relevance score from highest to lowest.\n"
                "Output strict JSON with a single field `selected`: an ordered list of objects {key, relevance, reason}.\n"
                "The first item should have the highest relevance score, the last item should have the lowest.\n"
                "Ensure keys come only from the provided list.\n\n"
                f"Schema hint:\n{json.dumps(schema_hint)}\n\n"
                f"Input:\n{json.dumps(payload, ensure_ascii=False)}"
            )

            # 使用轻量模型执行“排序/筛选”以降本提速
            from config import config as app_config
            resp = await llm_service.generate_text(
                prompt=user_prompt,
                reasoning_effort=str(getattr(app_config, 'CONTENT_SELECTION_REASONING_EFFORT', 'low') or 'low'),
                max_completion_tokens=int(getattr(app_config, 'CONTENT_SELECTION_MAX_TOKENS', 16000)),
                system_prompt=system_prompt,
                model=getattr(app_config, 'CONTENT_SELECTION_MODEL', None),
                # 强制返回 JSON
                force_json=True,
            )
            if not resp:
                # 无响应则走本地兜底
                max_per_post = max(1, int(getattr(config, 'CONTENT_SELECTION_MAX_PER_POST', 5)))
                return self._local_topk_fallback(posts, int(max_comments), max_per_post)

            text = resp.strip()
            try:
                result = json.loads(text)
            except Exception:
                # 解析失败则走本地兜底，避免后续再二次裁剪
                max_per_post = max(1, int(getattr(config, 'CONTENT_SELECTION_MAX_PER_POST', 5)))
                self.logger.warning("LLM筛选结果解析失败，启用本地Top-K兜底")
                return self._local_topk_fallback(posts, int(max_comments), max_per_post)

            selected_list = result.get("selected") or []
            # 保持LLM返回的顺序，因为LLM已经按相关性排序
            selected_keys_ordered = [x.get("key") for x in selected_list if isinstance(x, dict) and x.get("key")]
            if not selected_keys_ordered:
                return posts

            # 创建 key -> relevance 映射，用于后续标记
            key_to_relevance = {}
            for item in selected_list:
                if isinstance(item, dict) and item.get("key"):
                    key_to_relevance[item["key"]] = item.get("relevance", 0)

            # 多样性约束：限制每个帖子最多保留的评论数量
            max_per_post = max(1, int(getattr(config, 'CONTENT_SELECTION_MAX_PER_POST', 5)))

            # 建立 key -> (post_index, comment) 映射
            key_to_data = {}
            for pi, p in enumerate(posts):
                for c in (p.get("comments", []) or []):
                    ckey = c.get("permalink") or c.get("id")
                    if ckey:
                        key_to_data[ckey] = (pi, c)

            # 按LLM返回的顺序处理评论，同时应用多样性约束
            filtered_selected: List[str] = []
            per_post_counter: Dict[int, int] = {}
            for key in selected_keys_ordered:
                if len(filtered_selected) >= max_comments:
                    break
                if key not in key_to_data:
                    continue
                pi, _ = key_to_data[key]
                if per_post_counter.get(pi, 0) >= max_per_post:
                    continue
                filtered_selected.append(key)
                per_post_counter[pi] = per_post_counter.get(pi, 0) + 1

            # 按帖子分组，但保持LLM返回的顺序
            new_posts: List[Dict[str, Any]] = []
            post_comments: Dict[int, List[Dict[str, Any]]] = {}
            post_commenters: Dict[int, List[str]] = {}
            
            # 初始化每个帖子的空列表
            for pi, p in enumerate(posts):
                post_comments[pi] = []
                post_commenters[pi] = []
            
            # 按LLM返回的顺序添加评论
            for key in filtered_selected:
                if key in key_to_data:
                    pi, comment = key_to_data[key]
                    # 将相关性分数添加到评论中
                    comment["_relevance_score"] = key_to_relevance.get(key, 0)
                    post_comments[pi].append(comment)
                    author = comment.get("author")
                    if author and author != "[deleted]" and author not in post_commenters[pi]:
                        post_commenters[pi].append(author)
            
            # 构建最终结果，只包含有评论的帖子
            for pi, p in enumerate(posts):
                if post_comments[pi]:
                    new_posts.append({
                        "success": True,
                        "post": p.get("post", {}),
                        "comments": post_comments[pi],
                        "commenters": post_commenters[pi]
                    })

            # 如果没有一个帖子留下，走本地兜底
            if not new_posts:
                return self._local_topk_fallback(posts, int(max_comments), max_per_post)

            return new_posts

        except Exception as e:
            self.logger.warning(f"LLM筛选相关评论失败，返回原数据: {e}")
            return posts

