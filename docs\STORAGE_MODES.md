# CogBridges 存储模式（PostgreSQL-only）

本项目现仅支持 PostgreSQL 持久化存储，不再提供 JSON 存储与备份模式。

## 配置

```bash
ENABLE_DATABASE=True
DATABASE_URL=postgresql://user:password@host:port/db
```

## 路径说明

- 应用数据根目录：`/data/`
- 日志目录：`/data/logs/`
- Grok 原始响应：`/data/grok_raw_responses/`

## 性能与维护

- 使用连接池（可配 `DB_POOL_SIZE`、`DB_MAX_OVERFLOW`、`DB_POOL_TIMEOUT`）
- 模型定义见 `models/database_models.py`
- 表创建与校验：`scripts/init_database.py --create-tables / --validate-schema`