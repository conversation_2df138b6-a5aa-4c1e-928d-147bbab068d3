from typing import Optional
from itsdangerous import URLSafeTimedSerializer, BadSignature, SignatureExpired
from config import config as _cfg


_token_serializer = URLSafeTimedSerializer(_cfg.SECRET_KEY)


def issue_token(user_id: int) -> str:
    return _token_serializer.dumps({"uid": int(user_id)})


def verify_token(token: str, max_age_seconds: int = 7 * 24 * 3600) -> Optional[int]:
    try:
        data = _token_serializer.loads(token, max_age=max_age_seconds)
        return int(data.get("uid"))
    except (BadSignature, SignatureExpired, Exception):
        return None