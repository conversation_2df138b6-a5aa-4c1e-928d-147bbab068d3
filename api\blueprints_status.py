from flask import Blueprint, jsonify
from utils.logger_utils import get_logger
from .app import get_cogbridges_service

logger = get_logger(__name__)

bp_status = Blueprint("status", __name__)


@bp_status.route('/api/status', methods=['GET'])
def get_status():
    try:
        service = get_cogbridges_service()
        if not service:
            return jsonify({
                "success": False,
                "error": "CogBridges service not initialized"
            }), 503

        stats = service.get_statistics()
        return jsonify({
            "success": True,
            "statistics": stats
        })
    except Exception as e:
        logger.error(f"Failed to get service status: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500