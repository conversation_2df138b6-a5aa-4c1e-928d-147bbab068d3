# Warning: User-visible content must be ENGLISH ONLY (AI must comply)

All content visible to end users MUST be English (en-US). Any non-English content (including mixed-language UI) is strictly prohibited in UI, emails, and API responses consumed by the UI.

## Mandatory rules
- All user-visible text MUST be English. No Chinese or other languages. No mixed-language strings.
- Do NOT pass through third-party non-English error messages to users. Translate or map to concise English first.
- Frontend default language must be en-US. Remove or disable non-English fallbacks/placeholders.
- Backend API response fields intended for users (`error`, `message`, `description`, etc.) must be English.
- Emails and notifications (subjects and bodies) must be English.

## Scope (must be English)
- Frontend UI: buttons, menus, dialogs, form validations, empty states, loading and error states.
- Frontend static copy: headings, subtitles, placeholders, tooltips, helper texts, and default texts of components.
- Backend outward messages: user-facing fields in API JSON responses.
- Emails and notifications: verification codes, status updates, exceptions.
- Logs that are presented to users (e.g., surfaced via UI) must be English.

## Exceptions (can be non-English, but must NOT be user-visible)
- Source code identifiers, comments, internal logs (server-side only, developer-facing).
- Developer documentation/README not shown to end users.
- Proper nouns/brands (e.g., OpenAI, Reddit). Surrounding descriptive text must still be English.

## Implementation guidance
- Frontend:
  - Ensure components have English defaults. Disable non-English fallbacks. All validation messages must be English.
  - Do not render backend error strings verbatim if they are non-English; provide English mapping.
- Backend:
  - When catching exceptions, convert messages to readable English before returning to client.
  - API contract: when `success: false`, `error` must be a short English summary; optionally include a machine `code` for frontend mapping.
  - Never return third-party non-English stack traces directly.
- Emails:
  - Templates must be English and stylistically consistent.

## Pre-commit self-check (quick grep examples)
Use these as heuristics; always review context manually.

```bash
# Frontend: flag likely non-English copy (heuristic; adjust as needed)
rg -n "[\u4e00-\u9fa5]+" frontend/src --glob '!**/*.test.*' --glob '!**/node_modules/**'

# Backend: flag likely non-English messages in responses
rg -n "jsonify\(|return jsonify|error\s*:\s*\"[\\u4e00-\\u9fa5]" api services --glob '!**/tests/**'
```

Note: The regex is heuristic and may include false positives. Manually confirm whether the string is user-visible.

## Examples (fix these)
- Wrong (non-English):
  - "邮箱或密码错误"
  - "发送/重新发送"
  - "密码强度不足"
- Correct (English):
  - "Invalid email or password"
  - "Send/Resend"
  - "Password strength insufficient"

## PR checklist
- [ ] All new copy is English, no non-English fallbacks
- [ ] Validation and error messages are English
- [ ] Backend responses to end users are English; no non-English passthrough
- [ ] Emails and notifications are English
- [ ] Ran self-check commands and manually reviewed flagged lines

---
This is a mandatory policy. AI assistants/scripts must read and comply with this file before generating or modifying code. If any non-English user-visible content appears, replace it with English immediately.