import { useState, useEffect, useRef } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import LoadingSteps from '../components/LoadingSteps'
import { api } from '../services/api'
import logger from '../utils/logger'
import { getActiveSearch, setActiveSearch, updateActiveSearch, clearActiveSearch, isSearchInProgress } from '../services/searchSession'
import { recordPageVisit } from '../services/analytics'

const LoadingPage = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const [currentStep, setCurrentStep] = useState(0)
  const [progress, setProgress] = useState(0) // UI progress (smoothed + time-based)
  const [searchStatus, setSearchStatus] = useState('starting') // starting, searching, completed, error
  const [errorMessage, setErrorMessage] = useState('')
  const [searchResult, setSearchResult] = useState(null)
  const [etaSeconds, setEtaSeconds] = useState(null)
  const [backendProgress, setBackendProgress] = useState(0) // raw backend progress
  const [sessionId, setSessionId] = useState(null)

  const query = location.state?.query || 'Search query'

  // Search flow
  const startedRef = useRef(false)
  const clientSessionIdRef = useRef(null)
  const searchStartRef = useRef(null)
  const uiTimerRef = useRef(null)
  const searchCompletedRef = useRef(false)  // 添加标志跟踪搜索是否已完成
  const cancellingRef = useRef(false)  // 避免重复取消

  // Assume an average 90s timeline for smoother UX
  const EXPECTED_TOTAL_MS = 90000
  // Four steps: search 6s, fetch Reddit 19s, user history 26s, LLM analysis 39s
  const STEP_DURATIONS_MS = [6000, 19000, 26000, 39000]
  const STEP_CUM_MS = [
    STEP_DURATIONS_MS[0],
    STEP_DURATIONS_MS[0] + STEP_DURATIONS_MS[1],
    STEP_DURATIONS_MS[0] + STEP_DURATIONS_MS[1] + STEP_DURATIONS_MS[2],
    EXPECTED_TOTAL_MS
  ]
  const STEP_CUM_PCT = STEP_CUM_MS.map(ms => (ms / EXPECTED_TOTAL_MS) * 100)

  useEffect(() => {
    // 记录页面访问（用于分析漏斗）
    recordPageVisit('/loading')

    if (startedRef.current) return
    if (searchCompletedRef.current) return

    // Prefer recovering existing active session
    const active = getActiveSearch()
    if (active && isSearchInProgress(active.status) && active.sessionId && active.query) {
      startedRef.current = true
      clientSessionIdRef.current = active.clientSessionId || `${Date.now()}_${Math.random().toString(36).slice(2, 8)}`
      setSessionId(active.sessionId)
      setSearchStatus('searching')
      searchStartRef.current = active.startedAt ? active.startedAt : Date.now()
      // Continue polling existing session
      resumePolling(active.sessionId, active.query)
      return
    }

    if (!location.state?.query) {
      navigate('/')
      return
    }

    startedRef.current = true
    // Stable id across StrictMode mounts to achieve idempotency on backend
    clientSessionIdRef.current = location.state?.sessionId || `${Date.now()}_${Math.random().toString(36).slice(2, 8)}`
    startSearch()
  }, [navigate])

  // 当用户点击浏览器回退（popstate）从 LoadingPage 返回主页时，立即取消后端搜索
  useEffect(() => {
    const handlePopState = async () => {
      if (searchCompletedRef.current) return
      if (cancellingRef.current) return
      cancellingRef.current = true
      try {
        const sid = sessionId || (clientSessionIdRef.current ? `search_${clientSessionIdRef.current}` : null)
        if (sid) {
          await api.cancelSearch(sid)
        }
      } catch (e) {
        console.warn('Cancel on back failed:', e)
      } finally {
        try { clearActiveSearch() } catch (_) {}
      }
    }
    window.addEventListener('popstate', handlePopState)
    return () => {
      window.removeEventListener('popstate', handlePopState)
    }
  }, [sessionId])

  // 处理页面可见性变化 - 当用户切回标签页时检查搜索状态
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // 首先检查内存中是否已有结果
        if (searchCompletedRef.current && searchResult) {
          // 如果搜索已完成且有结果，立即导航到结果页
          navigate('/results', { 
            state: { 
              query,
              searchResult: searchResult,
              searchTime: searchResult.search_time || 0
            },
            replace: true
          })
          return
        }
        
        // 检查localStorage中的状态
        const active = getActiveSearch()
        if (active && active.status === 'completed' && active.result) {
          // 搜索已在后台完成，从localStorage恢复结果
          searchCompletedRef.current = true
          setSearchStatus('completed')
          setSearchResult(active.result)
          
          // 导航到结果页
          setTimeout(() => {
            navigate('/results', { 
              state: { 
                query: active.query || query,
                searchResult: active.result,
                searchTime: active.searchTime || active.result.search_time || 0
              },
              replace: true
            })
          }, 100)
        }
      }
    }
    
    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [searchStatus, searchResult, query, navigate])

  // Map backend percent to step based on the 90s timeline thresholds
  const mapBackendProgressToStep = (pct) => {
    if (pct < STEP_CUM_PCT[0]) return 0
    if (pct < STEP_CUM_PCT[1]) return 1
    if (pct < STEP_CUM_PCT[2]) return 2
    return 3
  }

  // Infer expected step from elapsed time on the fixed timeline
  const getTimeBasedStep = (elapsedMs) => {
    if (elapsedMs < STEP_CUM_MS[0]) return 0
    if (elapsedMs < STEP_CUM_MS[1]) return 1
    if (elapsedMs < STEP_CUM_MS[2]) return 2
    return 3
  }

  const startSearch = async () => {
    try {
      setSearchStatus('searching')
      searchStartRef.current = Date.now()

      // Kick off async search
      const searchResponse = await api.search(query, clientSessionIdRef.current)
      if (!searchResponse.success) {
        throw new Error(searchResponse.error || 'Search request failed')
      }

      const sessionId = searchResponse.session_id
      setSessionId(sessionId)
      setActiveSearch({
        query,
        clientSessionId: clientSessionIdRef.current,
        sessionId,
        status: 'searching',
        startedAt: searchStartRef.current
      })

      // Poll search progress
      const result = await api.pollSearchProgress(
        sessionId,
        (progressData) => {
          const backendPct = Number(progressData.progress || 0)
          const clamped = Math.max(0, Math.min(100, backendPct))
          setBackendProgress(clamped)
          updateActiveSearch({ progress: clamped, status: progressData.status || 'running' })
        },
        600000, // 10 min timeout
        1500 // base polling interval
      )

      // Completed
      setSearchResult(result)
      setProgress(100)
      setCurrentStep(3)
      setEtaSeconds(0)
      setSearchStatus('completed')
      searchCompletedRef.current = true  // 标记搜索已完成
      
      // 在清除activeSearch之前，保存搜索结果到localStorage
      // 这样即使用户在其他标签页，切回时也能获取结果
      updateActiveSearch({ 
        status: 'completed', 
        finishedAt: Date.now(),
        result: result,
        searchTime: result.search_time || 0
      })
      
      // 保留activeSearch，交由 ResultsPage 统一清理，避免切换标签页时丢失状态
      // 不在此处调用 clearActiveSearch()

      setTimeout(() => {
        navigate('/results', { 
          state: { 
            query,
            searchResult: result,
            searchTime: result.search_time || 0 // Use backend-calculated search time
          },
          replace: true  // 替换当前历史记录，而不是添加新的
        })
      }, 500)

    } catch (error) {
      logger.error('loading_search_failed')
      setSearchStatus('error')
      // 针对 Reddit 拥堵（503）显示更友好的提示
      if (error?.status === 503) {
        setErrorMessage(error.message || 'A lot of people are searching right now. Please wait a moment and try again.')
      } else {
        setErrorMessage(error.message)
      }
      
      // Persist failure status to avoid redirect loop
      try {
        updateActiveSearch({ status: 'error', finishedAt: Date.now(), error: error?.message || 'Search failed' })
        clearActiveSearch()
      } catch (_) {}

      // Handle specific error cases without alert
      if (error.message && error.message.includes('Insufficient points')) {
        // 积分不足的情况现在已经在搜索前处理，这里只是备用
        // 不再跳转页面，让用户看到错误信息即可
        logger.warn('loading_insufficient_points_error')
      } else if (error.message && error.message.includes('trial quota exhausted')) {
        setTimeout(() => {
          navigate('/register', { replace: true })
        }, 1500)
      } else if (error.message && (error.message.includes('Please register to search') || error.message.includes('free search limit'))) {
        // 未登录用户尝试搜索或达到匿名搜索限制，重定向回首页并显示注册提示
        try { window.localStorage.setItem('anon_limit_reached', '1') } catch (_) {}
        setTimeout(() => {
          navigate('/', {
            replace: true,
            state: { showTrialLimitModal: true }
          })
        }, 1000)
      } else {
        setTimeout(() => {
          navigate('/', { replace: true })
        }, 3000)
      }
    }
  }

  const resumePolling = async (sid, q) => {
    try {
      const result = await api.pollSearchProgress(
        sid,
        (progressData) => {
          const backendPct = Number(progressData.progress || 0)
          const clamped = Math.max(0, Math.min(100, backendPct))
          setBackendProgress(clamped)
          updateActiveSearch({ progress: clamped, status: progressData.status || 'running' })
        },
        600000,
        1500
      )
      setSearchResult(result)
      setProgress(100)
      setCurrentStep(3)
      setEtaSeconds(0)
      setSearchStatus('completed')
      searchCompletedRef.current = true
      
      // 保存搜索结果到localStorage
      updateActiveSearch({ 
        status: 'completed', 
        finishedAt: Date.now(),
        result: result,
        searchTime: result.search_time || 0
      })
      
      // 不清理 activeSearch，由 ResultsPage 负责
      setTimeout(() => {
        navigate('/results', {
          state: {
            query: q,
            searchResult: result,
            searchTime: result.search_time || 0 // Use backend-calculated search time
          },
          replace: true
        })
      }, 500)
    } catch (error) {
      logger.error('loading_resume_polling_failed')
      setSearchStatus('error')
      setErrorMessage(error.message || 'Failed to resume search')
    }
  }

  // UI ticker based on fixed 90s timeline
  // - Progress advances linearly up to 99% until complete
  // - Step is max(time-based step, backend-derived step)
  // - ETA is derived from the 90s expectation; after 90s, keep ~12s until completion
  useEffect(() => {
    if (searchStatus !== 'searching') return
    if (!searchStartRef.current) return

    const tick = () => {
      const now = Date.now()
      const elapsedMs = now - searchStartRef.current

      // Time-based expected progress (capped at 99%)
      const expectedPct = Math.min(99, (elapsedMs / EXPECTED_TOTAL_MS) * 100)
      // Respect faster backend progress but still cap at 99%
      const backendCapped = Math.min(99, Math.max(0, backendProgress))
      const candidatePct = Math.max(expectedPct, backendCapped)

      setProgress(prev => {
        const next = Math.max(prev, candidatePct)
        return searchStatus === 'completed' ? 100 : Math.min(next, 99)
      })

      // Step selection to avoid visual regressions
      const timeStep = getTimeBasedStep(elapsedMs)
      const backendStep = mapBackendProgressToStep(backendProgress)
      setCurrentStep(Math.max(timeStep, backendStep))

      // ETA calculation
      if (searchStatus !== 'completed') {
        if (elapsedMs <= EXPECTED_TOTAL_MS) {
          setEtaSeconds(Math.max(1, Math.ceil((EXPECTED_TOTAL_MS - elapsedMs) / 1000)))
        } else {
          setEtaSeconds(12)
        }
      }
    }

    // First run then interval
    tick()
    uiTimerRef.current = setInterval(tick, 500)
    return () => {
      if (uiTimerRef.current) clearInterval(uiTimerRef.current)
    }
  }, [searchStatus, backendProgress])

  // 错误态界面（更友好，不使用alert）
  if (searchStatus === 'error') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="text-center max-w-md w-full bg-white p-4 sm:p-6 rounded-xl border border-red-200 shadow">
          <div className="text-red-600 font-semibold mb-2 text-sm sm:text-base">Search failed</div>
          <div className="text-xs sm:text-sm text-gray-600 mb-4">{errorMessage || 'Something went wrong. Please try again.'}</div>
          <button
            onClick={() => navigate('/', { replace: true })}
            className="px-3 sm:px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors text-sm sm:text-base"
          >
            Back to home
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header - 优化移动端显示 */}
      <header className="bg-white shadow-sm border-b border-gray-200 px-3 sm:px-6 py-2 sm:py-4">
        <div className="max-w-4xl mx-auto">
          {/* 移动端垂直布局，桌面端水平布局 */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            {/* Logo 区域 */}
            <div className="flex items-center space-x-2 sm:space-x-3">
              <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <span className="text-white font-bold text-xs sm:text-sm">C</span>
              </div>
              <div>
                <h1 className="text-base sm:text-lg font-semibold text-gray-800">CogBridges</h1>
                <p className="text-xs sm:text-sm text-gray-500 hidden sm:block">Working on your answers...</p>
              </div>
            </div>
            
            {/* 查询文本区域 - 优化移动端显示 */}
            <div className="text-left sm:text-right">
              <p className="text-xs sm:text-sm text-gray-600">Looking up</p>
              <p className="font-medium text-gray-800 text-sm sm:text-base line-clamp-2 break-words">"{query}"</p>
            </div>
          </div>
        </div>
      </header>

      {/* Main - 优化移动端间距 */}
      <main className="flex-1 flex items-center justify-center px-3 sm:px-6 py-4 sm:py-12">
        <div className="w-full max-w-3xl">
          {/* Overall progress - 优化移动端显示 */}
          <div className="mb-4 sm:mb-8">
            <div className="flex justify-between items-center mb-1 sm:mb-2">
              <span className="text-xs sm:text-sm font-medium text-gray-700">Progress</span>
              <span className="text-xs sm:text-sm text-gray-500">{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-1.5 sm:h-2">
              <div 
                className="bg-gradient-to-r from-primary-500 to-primary-600 h-1.5 sm:h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            {etaSeconds != null && searchStatus !== 'completed' && (
              <div className="text-right text-[10px] sm:text-xs text-gray-500 mt-0.5 sm:mt-1">About {etaSeconds} s left</div>
            )}
          </div>

          {/* Steps (driven by backend progress) */}
          <LoadingSteps 
            currentStep={currentStep}
          />

          {/* Optional info card removed */}

          {/* Cancel button - 优化移动端显示 */}
          <div className="mt-6 sm:mt-8 text-center">
            <button
              onClick={async () => {
                try {
                  if (sessionId) {
                    await api.cancelSearch(sessionId)
                  }
                } catch (e) {
                  logger.warn('loading_cancel_failed')
                } finally {
                  clearActiveSearch()
                  navigate('/', { replace: true })
                }
              }}
              className="px-4 sm:px-6 py-1.5 sm:py-2 text-gray-500 hover:text-gray-700 transition-colors text-sm sm:text-base touch-target"
            >
              Cancel and go home
            </button>
          </div>
        </div>
      </main>
    </div>
  )
}

export default LoadingPage 