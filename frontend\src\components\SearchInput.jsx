import React from 'react'
// 导入React Hooks：useState用于状态管理，useRef用于DOM引用
import { useState, useRef } from 'react'
// 导入Lucide React图标：Search搜索图标，X关闭图标，Loader加载图标
import { Search, X, Loader } from 'lucide-react'

// 搜索输入框组件 - 简化版，不包含搜索建议功能
// props参数说明：
// onSearch: 搜索回调函数，当用户提交搜索时调用
// placeholder: 输入框占位符文本，默认为"我该不该裸辞？"
// className: 额外的CSS类名，用于自定义样式
// isLoading: 是否正在加载（测试Reddit连接）
const SearchInput = ({ onSearch, placeholder = "", className = "", isLoading = false }) => {
  // 状态管理
  const [query, setQuery] = useState('')           // 当前输入的搜索查询
  const inputRef = useRef(null)                    // 输入框的DOM引用，用于聚焦等操作

  // 处理表单提交事件
  const handleSubmit = (e) => {
    e.preventDefault()  // 阻止表单默认提交行为
    if (query.trim()) {  // 确保查询不为空（去除首尾空格）
      onSearch(query.trim())  // 调用父组件传入的搜索回调函数
    }
  }

  // 处理输入框内容变化
  const handleInputChange = (e) => {
    setQuery(e.target.value)  // 更新查询内容
  }

  const clearInput = () => {
    setQuery('')
    inputRef.current?.focus()
  }

  return (
    <div className={`relative w-full max-w-3xl sm:max-w-4xl mx-auto ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative flex items-center bg-white/95 backdrop-blur border-2 border-gray-200 rounded-full shadow-2xl hover:shadow-[0_10px_35px_rgba(2,132,199,0.25)] transition-all duration-200 focus-within:border-primary-500 focus-within:ring-4 focus-within:ring-primary-500/30 focus-within:ring-offset-2 overflow-hidden">
          <Search className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 text-primary-600 ml-3 sm:ml-4 md:ml-5 flex-shrink-0" />
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={handleInputChange}
            placeholder={placeholder}
            className="flex-1 min-w-0 px-2 sm:px-3 md:px-4 py-2.5 sm:py-3 md:py-4 text-base md:text-lg placeholder-gray-400 bg-transparent border-none outline-none"
            aria-label="Search questions from Reddit"
          />
          {query && (
            <button
              type="button"
              onClick={clearInput}
              className="p-1 sm:p-1.5 mr-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors flex-shrink-0"
              aria-label="Clear search"
            >
              <X className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
            </button>
          )}
          <button
            type="submit"
            className="px-3 sm:px-4 md:px-6 py-2 sm:py-2.5 mr-1 sm:mr-1.5 text-xs sm:text-sm md:text-base bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-full shadow hover:from-primary-600 hover:to-primary-700 transition-colors duration-200 disabled:opacity-50 flex-shrink-0 whitespace-nowrap relative group flex items-center justify-center gap-2"
            disabled={!query.trim() || isLoading}
            aria-label="Submit search"
          >
            {isLoading ? (
              <>
                <Loader className="w-3.5 h-3.5 sm:w-4 sm:h-4 animate-spin" />
              </>
            ) : (
              <>
                <span className="hidden sm:inline">Search</span>
                <span className="sm:hidden">Go</span>
              </>
            )}
            <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap pointer-events-none hidden sm:block">
              Consumes 20 points
            </span>
          </button>
        </div>
      </form>
    </div>
  )
}

export default SearchInput 