import json
import asyncio

from services.content_selection_service import ContentSelectionService
from services import llm_service as ls


async def _fake_generate_text(prompt: str, **kwargs):
    # 返回固定选择集：选择前两条 key，按相关性降序
    payload_start = prompt.rfind("Input:\n")
    assert payload_start != -1
    # 简化：不解析真实 payload，直接返回固定结构
    return json.dumps({
        "selected": [
            {"key": "k1", "relevance": 0.95, "reason": "match"},
            {"key": "k2", "relevance": 0.90, "reason": "close"}
        ]
    })


def test_content_selection_keeps_only_selected(monkeypatch):
    # mock llm_service.generate_text
    monkeypatch.setattr(ls.llm_service, 'configured', True, raising=True)
    monkeypatch.setattr(ls.llm_service, 'generate_text', _fake_generate_text, raising=True)

    posts = [
        {
            "success": True,
            "post": {"title": "A", "url": "u1", "subreddit": "s"},
            "comments": [
                {"id": "c1", "body": "aaa", "permalink": "k1", "author": "u"},
                {"id": "c2", "body": "bbb", "permalink": "k2", "author": "v"},
                {"id": "c3", "body": "ccc", "permalink": "k3", "author": "w"},
            ],
            "commenters": ["u", "v", "w"]
        }
    ]

    selector = ContentSelectionService()
    out = asyncio.run(selector.select_relevant_comments(posts, query="q", max_comments=2))
    assert isinstance(out, list) and len(out) == 1
    kept = out[0].get("comments")
    assert len(kept) == 2
    kept_keys = [c.get("permalink") for c in kept]
    # 验证顺序按LLM返回的顺序（k1, k2）
    assert kept_keys == ["k1", "k2"]


def test_content_selection_preserves_llm_order(monkeypatch):
    """测试评论按LLM返回的顺序排列，而不是按原始顺序"""
    
    async def _fake_generate_text_with_order(prompt: str, **kwargs):
        # LLM返回的顺序是 k3, k1, k2（按相关性降序）
        return json.dumps({
            "selected": [
                {"key": "k3", "relevance": 0.95, "reason": "most relevant"},
                {"key": "k1", "relevance": 0.85, "reason": "relevant"},
                {"key": "k2", "relevance": 0.70, "reason": "somewhat relevant"}
            ]
        })
    
    monkeypatch.setattr(ls.llm_service, 'configured', True, raising=True)
    monkeypatch.setattr(ls.llm_service, 'generate_text', _fake_generate_text_with_order, raising=True)

    posts = [
        {
            "success": True,
            "post": {"title": "A", "url": "u1", "subreddit": "s"},
            "comments": [
                {"id": "c1", "body": "aaa", "permalink": "k1", "author": "u", "score": 10},
                {"id": "c2", "body": "bbb", "permalink": "k2", "author": "v", "score": 20},
                {"id": "c3", "body": "ccc", "permalink": "k3", "author": "w", "score": 15},
            ],
            "commenters": ["u", "v", "w"]
        }
    ]

    selector = ContentSelectionService()
    out = asyncio.run(selector.select_relevant_comments(posts, query="q", max_comments=3))
    
    assert isinstance(out, list) and len(out) == 1
    kept = out[0].get("comments")
    assert len(kept) == 3
    
    # 验证评论按LLM返回的顺序排列（k3, k1, k2）
    kept_keys = [c.get("permalink") for c in kept]
    assert kept_keys == ["k3", "k1", "k2"]
    
    # 验证相关性分数被正确添加
    relevance_scores = [c.get("_relevance_score", 0) for c in kept]
    assert relevance_scores == [0.95, 0.85, 0.70]


