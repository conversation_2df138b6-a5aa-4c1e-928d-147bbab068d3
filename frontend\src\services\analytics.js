/**
 * Analytics Service
 * 提供访问记录和分析功能
 */

/**
 * 记录页面访问
 * @param {string} pagePath - 页面路径
 * @param {Object} options - 额外选项
 */
export async function recordPageVisit(pagePath, options = {}) {
  try {
    // 延迟加载，避免循环依赖
    const { api } = await import('./api')
    const { default: logger } = await import('../utils/logger')

    // 获取匿名cookie ID（若存在）
    const anonCookie = (document.cookie || '')
      .split('; ')
      .find(row => row.startsWith('cogbridges_anon_id='))
      ?.split('=')[1]

    // 获取UTM参数
    const urlParams = new URLSearchParams(window.location.search)
    const utmSource = urlParams.get('utm_source')
    const utmMedium = urlParams.get('utm_medium')
    const utmCampaign = urlParams.get('utm_campaign')

    // 获取referrer
    const referrer = document.referrer || null

    const payload = {
      anonymous_cookie_id: anonCookie,
      landing_path: pagePath,
      referrer: referrer,
      utm_source: utmSource,
      utm_medium: utmMedium,
      utm_campaign: utmCampaign,
      ...options,
    }

    const resp = await api.analyticsVisit(payload)
    if (resp?.success) {
      logger.event('analytics_visit_ok')
    } else {
      logger.warn('analytics_visit_not_saved')
    }
  } catch (error) {
    // 访问记录失败不应影响页面功能
    const { default: logger } = await import('../utils/logger')
    logger.warn('analytics_visit_failed')
  }
}

/**
 * 记录搜索事件
 * @param {string} query - 搜索查询
 * @param {Object} options - 额外选项
 */
export async function recordSearchEvent(query, options = {}) {
  // 暂无后端事件端点；保留占位（避免404）。可后续扩展。
  try {
    // 复用访问事件以保证至少有一次站点记录
    await recordPageVisit('/search', { query, ...options })
    const { default: logger } = await import('../utils/logger')
    logger.event('analytics_search_event_ok')
  } catch (error) {
    const { default: logger } = await import('../utils/logger')
    logger.warn('analytics_search_event_failed')
  }
}

/**
 * 记录转化事件
 * @param {string} eventType - 事件类型 (register, purchase, etc.)
 * @param {Object} options - 额外选项
 */
export async function recordConversionEvent(eventType, options = {}) {
  // 暂无后端单独转化事件端点；保留占位并记录一次访问。
  try {
    await recordPageVisit('/conversion', { event_type: eventType, ...options })
    const { default: logger } = await import('../utils/logger')
    logger.event('analytics_conversion_event_ok')
  } catch (error) {
    const { default: logger } = await import('../utils/logger')
    logger.warn('analytics_conversion_event_failed')
  }
}
