#!/usr/bin/env python3
"""
Test Markers Update Script
为现有测试文件添加适当的pytest标记

这个脚本会扫描测试文件并建议添加适当的标记。
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Set


class TestMarkersUpdater:
    """测试标记更新器"""
    
    def __init__(self):
        self.tests_dir = Path(__file__).parent
        self.marker_suggestions = {}
    
    def analyze_test_file(self, file_path: Path) -> Set[str]:
        """分析测试文件并建议标记"""
        content = file_path.read_text(encoding='utf-8')
        suggested_markers = set()
        
        # 基于文件名的标记
        filename = file_path.stem
        if 'api' in filename:
            suggested_markers.add('api')
        if 'database' in filename or 'db' in filename:
            suggested_markers.add('database')
        if 'auth' in filename:
            suggested_markers.add('auth')
        if 'billing' in filename or 'points' in filename:
            suggested_markers.add('billing')
        if 'search' in filename:
            suggested_markers.add('search')
        if 'reddit' in filename:
            suggested_markers.add('reddit')
        if 'llm' in filename:
            suggested_markers.add('llm')
        if 'e2e' in filename or 'integration' in filename:
            suggested_markers.add('integration')
        if 'core' in filename or 'business' in filename:
            suggested_markers.add('e2e')
        if 'error' in filename or 'edge' in filename:
            suggested_markers.add('unit')
        if 'comprehensive' in filename:
            suggested_markers.add('integration')
        
        # 基于内容的标记
        if 'mock' in content.lower() or 'Mock' in content:
            suggested_markers.add('mock')
        if 'async def' in content or 'asyncio' in content:
            suggested_markers.add('unit')
        if 'test_client' in content or 'app_client' in content:
            suggested_markers.add('api')
        if 'db_session' in content or 'database' in content.lower():
            suggested_markers.add('database')
        if 'time.sleep' in content or 'slow' in content.lower():
            suggested_markers.add('slow')
        if 'requests' in content or 'aiohttp' in content:
            suggested_markers.add('network')
        
        # 默认标记
        if not suggested_markers:
            suggested_markers.add('unit')
        
        return suggested_markers
    
    def generate_marker_suggestions(self) -> Dict[str, Set[str]]:
        """生成所有测试文件的标记建议"""
        suggestions = {}
        
        for test_file in self.tests_dir.glob('test_*.py'):
            if test_file.name == 'test_markers_update.py':
                continue
            
            markers = self.analyze_test_file(test_file)
            suggestions[test_file.name] = markers
        
        return suggestions
    
    def print_suggestions(self):
        """打印标记建议"""
        suggestions = self.generate_marker_suggestions()
        
        print("🏷️ Test Markers Suggestions")
        print("=" * 50)
        
        for filename, markers in sorted(suggestions.items()):
            print(f"\n📄 {filename}")
            print(f"   Suggested markers: {', '.join(sorted(markers))}")
            
            # 生成pytest.mark装饰器建议
            for marker in sorted(markers):
                print(f"   @pytest.mark.{marker}")
        
        print("\n" + "=" * 50)
        print("💡 Usage examples:")
        print("   pytest -m unit          # Run unit tests")
        print("   pytest -m api           # Run API tests")
        print("   pytest -m 'not slow'    # Skip slow tests")
        print("   pytest -m 'unit and not network'  # Unit tests without network")


def main():
    """主函数"""
    updater = TestMarkersUpdater()
    updater.print_suggestions()


if __name__ == '__main__':
    main()
