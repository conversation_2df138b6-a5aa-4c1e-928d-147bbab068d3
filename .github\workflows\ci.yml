name: ci

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - name: Install Python deps
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
      - name: Run tests with coverage
        run: |
          pytest -q --disable-warnings --maxfail=1 --cov=api --cov=services --cov=models --cov=utils --cov-report=xml:coverage.xml
      - name: Upload coverage.xml
        uses: actions/upload-artifact@v4
        with:
          name: backend-coverage
          path: coverage.xml

  frontend:
    runs-on: ubuntu-latest
    container: mcr.microsoft.com/playwright:v1.54.2-jammy
    defaults:
      run:
        working-directory: frontend
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '22'
      - name: Install deps
        run: npm ci
      - name: Install Playwright browsers
        run: npx playwright install
      - name: Run e2e tests
        run: npm run test:e2e

    # 备选方案（若不使用容器镜像）：
    # steps:
    #   - uses: actions/checkout@v4
    #   - uses: actions/setup-node@v4
    #     with:
    #       node-version: '22'
    #   - name: Install deps
    #     run: npm ci
    #   - name: Install Playwright browsers with deps
    #     run: npx playwright install --with-deps
    #   - name: Run e2e tests
    #     run: npm run test:e2e
