import os
import smtplib
import ssl

try:
    from dotenv import load_dotenv
except Exception:
    load_dotenv = None


def try_once(host: str, port: int, use_ssl: bool, use_tls: bool, user: str, password: str) -> bool:
    print(f"→ try host={host} port={port} ssl={use_ssl} tls={use_tls}")
    context = ssl.create_default_context()
    if use_ssl:
        server = smtplib.SMTP_SSL(host, port, context=context, timeout=20)
    else:
        server = smtplib.SMTP(host, port, timeout=20)
        server.ehlo()
        if use_tls:
            server.starttls(context=context)
            server.ehlo()
    server.login(user, password)
    server.quit()
    return True


def main():
    if load_dotenv:
        load_dotenv()

    env_host = os.getenv("EMAIL_SMTP_HOST", "")
    user = os.getenv("EMAIL_SMTP_USER", "")
    password = os.getenv("EMAIL_SMTP_PASSWORD", "")
    env_port = int(os.getenv("EMAIL_SMTP_PORT", "587"))
    env_use_ssl = os.getenv("EMAIL_USE_SSL", "False").lower() == "true"
    env_use_tls = os.getenv("EMAIL_USE_TLS", "True").lower() == "true"

    if not user or not password:
        print("Missing EMAIL_SMTP_USER or EMAIL_SMTP_PASSWORD")
        return

    # Candidates to try (env first)
    hosts = []
    for h in [env_host, "smtp.zoho.com", "smtppro.zoho.com", "smtp.zoho.eu", "smtppro.zoho.eu", "smtp.zoho.in", "smtppro.zoho.in"]:
        if h and h not in hosts:
            hosts.append(h)

    modes = [
        (env_port, env_use_ssl, env_use_tls),
        (587, False, True),
        (465, True, False),
    ]

    last_error = None
    for host in hosts:
        for port, use_ssl, use_tls in modes:
            try:
                ok = try_once(host, port, use_ssl, use_tls, user, password)
                if ok:
                    print(f"✅ SMTP ok: host={host} port={port} ssl={use_ssl} tls={use_tls}")
                    return
            except Exception as e:
                last_error = e
                print(f"   failed: {e}")

    print(f"❌ All attempts failed. Last error: {last_error}")


if __name__ == "__main__":
    main()


