#!/usr/bin/env python3
"""
CogBridges Test Utilities
测试工具和辅助函数

提供通用的测试工具、数据生成器和验证函数，提高测试效率和一致性。
"""

import time
import json
import random
import string
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from unittest.mock import MagicMock, AsyncMock


class TestDataGenerator:
    """测试数据生成器"""
    
    @staticmethod
    def generate_email(prefix: str = "test") -> str:
        """生成唯一的测试邮箱"""
        timestamp = int(time.time())
        random_suffix = ''.join(random.choices(string.ascii_lowercase, k=4))
        return f"{prefix}_{timestamp}_{random_suffix}@example.com"
    
    @staticmethod
    def generate_session_id(prefix: str = "session") -> str:
        """生成唯一的会话ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        random_hex = ''.join(random.choices(string.hexdigits.lower(), k=8))
        return f"{prefix}_{timestamp}_{random_hex}"
    
    @staticmethod
    def generate_user_data(
        email: Optional[str] = None,
        verified: bool = True,
        points: int = 100
    ) -> Dict[str, Any]:
        """生成用户数据"""
        return {
            "id": random.randint(1, 10000),
            "email": email or TestDataGenerator.generate_email(),
            "password_hash": "hashed_password_" + ''.join(random.choices(string.ascii_letters, k=10)),
            "email_verified": verified,
            "points_balance": points,
            "created_at": datetime.utcnow(),
            "last_login_at": datetime.utcnow() if verified else None
        }
    
    @staticmethod
    def generate_reddit_post(
        post_id: Optional[str] = None,
        subreddit: str = "test",
        score: int = 100
    ) -> Dict[str, Any]:
        """生成Reddit帖子数据"""
        post_id = post_id or ''.join(random.choices(string.ascii_letters + string.digits, k=6))
        return {
            "id": post_id,
            "title": f"Test Post {post_id}",
            "selftext": f"This is test content for post {post_id}",
            "author": f"author_{post_id}",
            "subreddit": subreddit,
            "score": score,
            "num_comments": random.randint(5, 50),
            "created_utc": int(time.time()) - random.randint(3600, 86400),
            "url": f"https://reddit.com/r/{subreddit}/comments/{post_id}/",
            "permalink": f"/r/{subreddit}/comments/{post_id}/"
        }
    
    @staticmethod
    def generate_reddit_comment(
        comment_id: Optional[str] = None,
        post_id: str = "test_post",
        author: Optional[str] = None,
        score: int = 50
    ) -> Dict[str, Any]:
        """生成Reddit评论数据"""
        comment_id = comment_id or ''.join(random.choices(string.ascii_letters + string.digits, k=8))
        author = author or f"commenter_{comment_id[:4]}"
        return {
            "id": comment_id,
            "body": f"This is a test comment {comment_id}",
            "author": author,
            "score": score,
            "created_utc": int(time.time()) - random.randint(1800, 43200),
            "parent_id": post_id,
            "permalink": f"/r/test/comments/{post_id}/{comment_id}/"
        }
    
    @staticmethod
    def generate_user_history(
        username: Optional[str] = None,
        comment_count: int = 100,
        post_count: int = 10
    ) -> Dict[str, Any]:
        """生成用户历史数据"""
        username = username or f"user_{random.randint(1000, 9999)}"
        return {
            "username": username,
            "_metadata": {
                "subreddits": ["Python", "programming", "test"],
                "total_comments": comment_count,
                "total_posts": post_count,
                "account_age_days": random.randint(30, 1000)
            },
            "comments": [
                {
                    "body": f"Comment {i} by {username}",
                    "score": random.randint(1, 100),
                    "subreddit": random.choice(["Python", "programming", "test"]),
                    "created_utc": int(time.time()) - random.randint(3600, 86400)
                }
                for i in range(min(comment_count, 20))  # 限制数量
            ],
            "posts": [
                {
                    "title": f"Post {i} by {username}",
                    "score": random.randint(10, 500),
                    "subreddit": random.choice(["Python", "programming", "test"]),
                    "created_utc": int(time.time()) - random.randint(3600, 86400)
                }
                for i in range(min(post_count, 10))  # 限制数量
            ]
        }
    
    @staticmethod
    def generate_llm_analysis() -> Dict[str, Any]:
        """生成LLM分析结果"""
        return {
            "success": True,
            "analysis_summary": {
                "overall_credibility": random.choice(["high", "medium", "low"]),
                "consensus_level": random.choice(["strong", "moderate", "weak"]),
                "key_insights": [
                    "Test insight 1",
                    "Test insight 2",
                    "Test insight 3"
                ]
            },
            "commenter_profiles": {
                f"user_{i}": {
                    "expertise_level": random.choice(["expert", "intermediate", "beginner"]),
                    "credibility_score": round(random.uniform(0.1, 1.0), 2),
                    "specialization": f"test domain {i}",
                    "background": f"Test background for user {i}"
                }
                for i in range(1, 4)
            }
        }


class MockServiceFactory:
    """模拟服务工厂"""
    
    @staticmethod
    def create_mock_cogbridges_service() -> MagicMock:
        """创建模拟的CogBridges服务"""
        mock_service = MagicMock()
        
        # 配置基本属性
        mock_service.max_search_results = 5
        mock_service.max_comments_per_post = 6
        mock_service.max_user_comments = 20
        mock_service.max_user_posts = 10
        
        # 配置搜索方法
        async def mock_search(query, save_to_db=True, cancel_event=None):
            result = MagicMock()
            result.success = True
            result.query = query
            result.session_id = TestDataGenerator.generate_session_id()
            result.reddit_posts = [
                {
                    "post": TestDataGenerator.generate_reddit_post(),
                    "comments": [TestDataGenerator.generate_reddit_comment()],
                    "commenters": ["test_commenter"]
                }
            ]
            result.commenters_history = {
                "test_commenter": TestDataGenerator.generate_user_history("test_commenter")
            }
            result.llm_analysis = TestDataGenerator.generate_llm_analysis()
            result.total_time = 2.5
            return result
        
        mock_service.search = mock_search
        
        # 配置数据服务
        mock_service.data_service = MagicMock()
        mock_service.data_service.generate_session_id.side_effect = TestDataGenerator.generate_session_id
        mock_service.data_service.save_complete_session.return_value = "saved"
        mock_service.data_service.load_session_data.return_value = None
        
        return mock_service
    
    @staticmethod
    def create_mock_reddit_service() -> MagicMock:
        """创建模拟的Reddit服务"""
        mock_service = MagicMock()
        
        mock_service.configured = True
        mock_service.check_availability.return_value = True
        
        # 配置URL解析
        mock_service.parse_reddit_url.return_value = {
            "type": "post",
            "subreddit": "test",
            "post_id": "test_post_1"
        }
        
        # 配置异步方法
        async def mock_get_post_details(url):
            return TestDataGenerator.generate_reddit_post()
        
        async def mock_get_user_overview(username):
            return TestDataGenerator.generate_user_history(username)
        
        mock_service.get_post_details = mock_get_post_details
        mock_service.get_user_overview = mock_get_user_overview
        
        return mock_service
    
    @staticmethod
    def create_mock_llm_service() -> MagicMock:
        """创建模拟的LLM服务"""
        mock_service = MagicMock()
        
        mock_service.configured = True
        
        # 配置异步方法
        async def mock_generate_text(prompt, **kwargs):
            return json.dumps(TestDataGenerator.generate_llm_analysis())
        
        async def mock_analyze_commenter_credibility(username, **kwargs):
            return {
                "expertise_level": "expert",
                "credibility_score": 0.9,
                "specialization": "test domain",
                "background": f"Expert in test domain with username {username}"
            }
        
        mock_service.generate_text = mock_generate_text
        mock_service.analyze_commenter_credibility = mock_analyze_commenter_credibility
        
        return mock_service


class TestValidators:
    """测试验证器"""
    
    @staticmethod
    def validate_api_response(response, expected_status: int = 200) -> Dict[str, Any]:
        """验证API响应格式"""
        assert response.status_code == expected_status, f"Expected {expected_status}, got {response.status_code}"
        
        data = response.get_json()
        assert data is not None, "Response should contain JSON data"
        assert 'success' in data, "Response should contain 'success' field"
        
        return data
    
    @staticmethod
    def validate_search_result(result) -> None:
        """验证搜索结果格式"""
        assert hasattr(result, 'success'), "Result should have 'success' attribute"
        assert hasattr(result, 'query'), "Result should have 'query' attribute"
        assert hasattr(result, 'session_id'), "Result should have 'session_id' attribute"
        
        if result.success:
            assert hasattr(result, 'reddit_posts'), "Successful result should have 'reddit_posts'"
            assert hasattr(result, 'total_time'), "Successful result should have 'total_time'"
    
    @staticmethod
    def validate_user_data(user_data: Dict[str, Any]) -> None:
        """验证用户数据格式"""
        required_fields = ['id', 'email', 'email_verified', 'points_balance']
        for field in required_fields:
            assert field in user_data, f"User data should contain '{field}' field"
        
        assert '@' in user_data['email'], "Email should be valid format"
        assert isinstance(user_data['points_balance'], int), "Points balance should be integer"
    
    @staticmethod
    def validate_reddit_post(post_data: Dict[str, Any]) -> None:
        """验证Reddit帖子数据格式"""
        required_fields = ['id', 'title', 'author', 'subreddit', 'score', 'url']
        for field in required_fields:
            assert field in post_data, f"Post data should contain '{field}' field"
        
        assert post_data['url'].startswith('https://reddit.com'), "URL should be Reddit URL"
    
    @staticmethod
    def validate_reddit_comment(comment_data: Dict[str, Any]) -> None:
        """验证Reddit评论数据格式"""
        required_fields = ['id', 'body', 'author', 'score']
        for field in required_fields:
            assert field in comment_data, f"Comment data should contain '{field}' field"
        
        assert len(comment_data['body']) > 0, "Comment body should not be empty"


class TestHelpers:
    """测试辅助函数"""
    
    @staticmethod
    def wait_for_condition(condition_func, timeout: float = 5.0, interval: float = 0.1) -> bool:
        """等待条件满足"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            if condition_func():
                return True
            time.sleep(interval)
        return False
    
    @staticmethod
    def create_test_file(content: str, filename: str = None) -> str:
        """创建临时测试文件"""
        import tempfile
        import os
        
        if filename:
            filepath = os.path.join(tempfile.gettempdir(), filename)
        else:
            fd, filepath = tempfile.mkstemp(suffix='.json')
            os.close(fd)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return filepath
    
    @staticmethod
    def cleanup_test_file(filepath: str) -> None:
        """清理测试文件"""
        import os
        try:
            if os.path.exists(filepath):
                os.remove(filepath)
        except Exception:
            pass  # 忽略清理错误
    
    @staticmethod
    def assert_timing(func, min_time: float = 0.0, max_time: float = float('inf')):
        """断言函数执行时间"""
        start_time = time.time()
        result = func()
        elapsed = time.time() - start_time
        
        assert min_time <= elapsed <= max_time, f"Execution time {elapsed:.3f}s not in range [{min_time}, {max_time}]"
        return result
    
    @staticmethod
    def mock_async_context_manager(return_value=None):
        """创建异步上下文管理器的mock"""
        mock = MagicMock()
        mock.__aenter__ = AsyncMock(return_value=return_value)
        mock.__aexit__ = AsyncMock(return_value=None)
        return mock
