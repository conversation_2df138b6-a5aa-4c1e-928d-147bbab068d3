import { useState, useEffect } from 'react'
import { Search, Users, Brain, Lightbulb, Check, Loader } from 'lucide-react'

const LoadingSteps = ({ currentStep = 0, onComplete }) => {
  const [animatedStep, setAnimatedStep] = useState(0)

  // Keep 4 steps consistent with LoadingPage timing
  const steps = [
    {
      id: 'search',
      icon: Search,
      title: 'Finding related discussions',
      description: 'Looking across Reddit for conversations related to your question',
      duration: 6000
    },
    {
      id: 'fetch-reddit',
      icon: Users,
      title: 'Gathering posts and replies',
      description: 'Collecting the key posts and top replies',
      duration: 19000
    },
    {
      id: 'user-analysis',
      icon: Brain,
      title: 'Reviewing commenter profiles',
      description: 'Understanding who the commenters are and what they usually talk about',
      duration: 26000
    },
    {
      id: 'llm-analysis',
      icon: Lightbulb,
      title: 'Summarizing insights',
      description: 'Organizing the most helpful answers for you',
      duration: 39000
    }
  ]

  useEffect(() => {
    if (currentStep > animatedStep) {
      const timer = setTimeout(() => {
        setAnimatedStep(currentStep)
      }, 300)
      return () => clearTimeout(timer)
    }
  }, [currentStep, animatedStep])

  const getStepStatus = (index) => {
    if (index < animatedStep) return 'completed'
    if (index === animatedStep) return 'current'
    return 'pending'
  }

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Heading - 优化移动端显示 */}
      <div className="text-center mb-4 sm:mb-8">
        <h2 className="text-lg sm:text-2xl font-semibold text-gray-800 mb-1 sm:mb-2">
          Finding the best answers for you
        </h2>
        <p className="text-xs sm:text-base text-gray-600 px-4 sm:px-0">
          We're looking at discussions and the people behind them to bring you trustworthy insights
        </p>
      </div>

      {/* Steps - 优化移动端间距 */}
      <div className="space-y-3 sm:space-y-6">
        {steps.map((step, index) => {
          const status = getStepStatus(index)
          const Icon = step.icon
          
          return (
            <div
              key={step.id}
              className={`flex items-center p-3 sm:p-4 rounded-lg transition-all duration-500 ${
                status === 'current' 
                  ? 'bg-primary-50 border border-primary-200 shadow-md transform sm:scale-105' 
                  : status === 'completed'
                    ? 'bg-green-50 border border-green-200'
                    : 'bg-gray-50 border border-gray-200 opacity-60'
              }`}
            >
              {/* Icon - 优化移动端大小 */}
              <div className={`flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center ${
                status === 'completed'
                  ? 'bg-green-500 text-white'
                  : status === 'current'
                    ? 'bg-primary-500 text-white'
                    : 'bg-gray-300 text-gray-500'
              }`}>
                {status === 'completed' ? (
                  <Check className="w-5 h-5 sm:w-6 sm:h-6" />
                ) : status === 'current' ? (
                  <Loader className="w-5 h-5 sm:w-6 sm:h-6 animate-spin" />
                ) : (
                  <Icon className="w-5 h-5 sm:w-6 sm:h-6" />
                )}
              </div>

              {/* Content - 优化移动端文本 */}
              <div className="ml-3 sm:ml-4 flex-1 min-w-0">
                <h3 className={`font-medium text-sm sm:text-base ${
                  status === 'current' ? 'text-primary-700' : 
                  status === 'completed' ? 'text-green-700' : 'text-gray-500'
                }`}>
                  {step.title}
                </h3>
                <p className={`text-xs sm:text-sm mt-0.5 sm:mt-1 pr-2 ${
                  status === 'current' ? 'text-primary-600' :
                  status === 'completed' ? 'text-green-600' : 'text-gray-400'
                }`}>
                  {step.description}
                </p>
              </div>

              {/* Indicator - 优化移动端显示 */}
              {status === 'current' && (
                <div className="flex-shrink-0 w-1.5 sm:w-2 h-6 sm:h-8 bg-primary-200 rounded-full overflow-hidden">
                  <div className="w-full bg-primary-500 rounded-full animate-pulse-slow h-full"></div>
                </div>
              )}
            </div>
          )
        })}
      </div>

      {/* Bottom note (ETA shown by parent) */}
    </div>
  )
}

export default LoadingSteps 