import '@testing-library/jest-dom'

// JSDOM polyfills for React 18 createRoot
if (!globalThis.document) {
  const { JSDOM } = await import('jsdom')
  const dom = new JSDOM('<!doctype html><html><body><div id="root"></div></body></html>')
  globalThis.window = dom.window as any
  globalThis.document = dom.window.document as any
  globalThis.navigator = { userAgent: 'node.js' } as any
}

// Ensure a container exists
if (!document.getElementById('root')) {
  const root = document.createElement('div')
  root.id = 'root'
  document.body.appendChild(root)
}

