#!/usr/bin/env python3
"""
CogBridges Services Comprehensive Tests
全面测试所有服务层组件的功能

这些测试覆盖：
- Reddit服务（帖子抓取、评论获取、用户历史）
- LLM服务（文本生成、评论者分析）
- 数据服务（会话管理、数据持久化）
- 认证服务（用户管理、验证）
- 邮件服务（邮件发送）
- Grok服务（搜索、citations处理）
"""

import os
import time
import pytest
import asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from datetime import datetime

# Configure test environment
os.environ.setdefault("ENABLE_DATABASE", "True")
os.environ.setdefault("TEST_MODE", "True")

from services.reddit_service import RedditService
from services.llm_service import LLMService
from services.data_service import DataService
from services.auth_service import AuthService
from services.email_service import EmailService
from services.grok_reddit_service import GrokRedditService
from config import config


class TestRedditService:
    """Reddit服务测试"""

    @pytest.fixture
    def reddit_service(self):
        """创建Reddit服务实例"""
        return RedditService()

    def test_parse_reddit_url_valid_post(self, reddit_service):
        """测试解析有效的Reddit帖子URL"""
        url = "https://reddit.com/r/Python/comments/abc123/test_post/"
        result = reddit_service.parse_reddit_url(url)
        
        assert result is not None
        assert result["type"] == "post"
        assert result["subreddit"] == "Python"
        assert result["post_id"] == "abc123"

    def test_parse_reddit_url_valid_comment(self, reddit_service):
        """测试解析有效的Reddit评论URL"""
        url = "https://reddit.com/r/Python/comments/abc123/test_post/def456/"
        result = reddit_service.parse_reddit_url(url)
        
        assert result is not None
        assert result["type"] == "comment"
        assert result["subreddit"] == "Python"
        assert result["post_id"] == "abc123"
        assert result["comment_id"] == "def456"

    def test_parse_reddit_url_invalid(self, reddit_service):
        """测试解析无效URL"""
        invalid_urls = [
            "https://example.com/not-reddit",
            "not-a-url",
            "",
            None
        ]
        
        for url in invalid_urls:
            result = reddit_service.parse_reddit_url(url)
            assert result is None

    def test_check_availability_configured(self, reddit_service):
        """测试已配置Reddit服务的可用性检查"""
        with patch('requests.get') as mock_get:
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_get.return_value = mock_response
            
            # 假设Reddit已配置
            reddit_service.configured = True
            
            result = reddit_service.check_availability()
            assert result is True

    def test_check_availability_unconfigured(self, reddit_service):
        """测试未配置Reddit服务的可用性检查"""
        reddit_service.configured = False
        
        result = reddit_service.check_availability()
        assert result is True  # 未配置时返回True以保持最小功能

    def test_check_availability_network_error(self, reddit_service):
        """测试网络错误时的可用性检查"""
        with patch('requests.get') as mock_get:
            mock_get.side_effect = Exception("Network error")
            
            reddit_service.configured = True
            
            result = reddit_service.check_availability()
            assert result is False

    async def test_get_post_details_success(self, reddit_service):
        """测试成功获取帖子详情"""
        if not reddit_service.configured:
            pytest.skip("Reddit API not configured")
        
        # Mock asyncpraw
        with patch.object(reddit_service, '_ensure_async_reddit') as mock_reddit:
            mock_submission = MagicMock()
            mock_submission.id = "abc123"
            mock_submission.title = "Test Post"
            mock_submission.selftext = "Test content"
            mock_submission.author.name = "test_user"
            mock_submission.subreddit.display_name = "Python"
            mock_submission.score = 100
            mock_submission.num_comments = 10
            mock_submission.created_utc = 1700000000
            mock_submission.permalink = "/r/Python/comments/abc123/"
            mock_submission.url = "https://reddit.com/r/Python/comments/abc123/"
            
            mock_reddit_instance = MagicMock()
            mock_reddit_instance.submission.return_value = mock_submission
            mock_reddit.return_value = mock_reddit_instance
            
            # Mock load method
            async def mock_load():
                pass
            mock_submission.load = mock_load
            
            url = "https://reddit.com/r/Python/comments/abc123/test_post/"
            result = await reddit_service.get_post_details(url)
            
            assert result is not None
            assert result["id"] == "abc123"
            assert result["title"] == "Test Post"
            assert result["author"] == "test_user"

    async def test_get_post_details_invalid_url(self, reddit_service):
        """测试无效URL的帖子详情获取"""
        result = await reddit_service.get_post_details("invalid-url")
        assert result is None

    async def test_get_user_overview_success(self, reddit_service):
        """测试成功获取用户概览"""
        if not reddit_service.configured:
            pytest.skip("Reddit API not configured")
        
        with patch.object(reddit_service, '_ensure_async_reddit') as mock_reddit:
            # Mock用户数据
            mock_user = MagicMock()
            mock_user.name = "test_user"
            mock_user.created_utc = 1600000000
            
            # Mock用户评论和帖子
            mock_comment = MagicMock()
            mock_comment.body = "Test comment"
            mock_comment.score = 10
            mock_comment.subreddit.display_name = "Python"
            mock_comment.created_utc = 1700000000
            
            mock_post = MagicMock()
            mock_post.title = "Test post"
            mock_post.score = 50
            mock_post.subreddit.display_name = "Python"
            mock_post.created_utc = 1700001000
            
            # Mock submissions和comments属性
            mock_user.submissions.new.return_value = [mock_post]
            mock_user.comments.new.return_value = [mock_comment]
            
            mock_reddit_instance = MagicMock()
            mock_reddit_instance.redditor.return_value = mock_user
            mock_reddit.return_value = mock_reddit_instance
            
            result = await reddit_service.get_user_overview("test_user")
            
            assert result is not None
            assert result["username"] == "test_user"
            assert "comments" in result
            assert "posts" in result
            assert "_metadata" in result

    async def test_get_user_overview_not_found(self, reddit_service):
        """测试获取不存在用户的概览"""
        if not reddit_service.configured:
            pytest.skip("Reddit API not configured")
        
        with patch.object(reddit_service, '_ensure_async_reddit') as mock_reddit:
            mock_reddit_instance = MagicMock()
            mock_reddit_instance.redditor.side_effect = Exception("User not found")
            mock_reddit.return_value = mock_reddit_instance
            
            result = await reddit_service.get_user_overview("nonexistent_user")
            assert result is None


class TestLLMService:
    """LLM服务测试"""

    @pytest.fixture
    def llm_service(self):
        """创建LLM服务实例"""
        return LLMService()

    async def test_generate_text_success(self, llm_service):
        """测试成功生成文本"""
        if not llm_service.configured:
            pytest.skip("LLM service not configured")
        
        with patch.object(llm_service, 'client') as mock_client:
            # Mock Replicate响应
            mock_prediction = MagicMock()
            mock_prediction.id = "test_prediction_id"
            mock_prediction.status = "succeeded"
            mock_prediction.output = ["Generated text response"]
            
            mock_client.run.return_value = mock_prediction
            
            result = await llm_service.generate_text(
                prompt="Test prompt",
                max_completion_tokens=100
            )
            
            assert result is not None
            assert isinstance(result, str)
            assert len(result) > 0

    async def test_generate_text_not_configured(self, llm_service):
        """测试未配置LLM服务时的文本生成"""
        llm_service.configured = False
        
        with pytest.raises(ValueError, match="Replicate API未配置"):
            await llm_service.generate_text("Test prompt")

    async def test_analyze_commenter_credibility_success(self, llm_service):
        """测试成功分析评论者可信度"""
        if not llm_service.configured:
            pytest.skip("LLM service not configured")
        
        with patch.object(llm_service, 'generate_text') as mock_generate:
            # Mock LLM响应
            mock_response = """
            {
                "expertise_level": "expert",
                "credibility_score": 0.9,
                "specialization": "Python programming",
                "background": "Experienced developer",
                "subreddits_active_in": ["Python", "programming"]
            }
            """
            mock_generate.return_value = mock_response
            
            high_score_content = [
                {
                    "body": "Great explanation of async programming",
                    "score": 50,
                    "subreddit": "Python"
                }
            ]
            
            result = await llm_service.analyze_commenter_credibility(
                username="test_user",
                high_score_content=high_score_content,
                query="Python async programming"
            )
            
            assert result is not None
            assert "expertise_level" in result
            assert "credibility_score" in result
            assert result["expertise_level"] == "expert"

    async def test_analyze_commenter_credibility_invalid_json(self, llm_service):
        """测试LLM返回无效JSON时的处理"""
        if not llm_service.configured:
            pytest.skip("LLM service not configured")
        
        with patch.object(llm_service, 'generate_text') as mock_generate:
            mock_generate.return_value = "Invalid JSON response"
            
            result = await llm_service.analyze_commenter_credibility(
                username="test_user",
                high_score_content=[],
                query="test"
            )
            
            # 应该返回默认结构
            assert result is not None
            assert "expertise_level" in result
            assert result["expertise_level"] == "unknown"


class TestDataService:
    """数据服务测试"""

    @pytest.fixture
    def data_service(self):
        """创建数据服务实例"""
        return DataService()

    def test_generate_session_id(self, data_service):
        """测试生成会话ID"""
        query = "test query"
        session_id = data_service.generate_session_id(query)
        
        assert session_id is not None
        assert isinstance(session_id, str)
        assert len(session_id) > 0
        assert "_" in session_id  # 应该包含分隔符

    def test_generate_session_id_unique(self, data_service):
        """测试生成的会话ID唯一性"""
        query = "test query"
        id1 = data_service.generate_session_id(query)
        time.sleep(0.001)  # 确保时间戳不同
        id2 = data_service.generate_session_id(query)
        
        assert id1 != id2

    def test_save_complete_session_database_unavailable(self, data_service):
        """测试数据库不可用时的会话保存"""
        # Mock数据库不可用
        data_service.storage_service = None
        
        with pytest.raises(RuntimeError, match="数据库存储服务不可用"):
            data_service.save_complete_session("test_session", {})

    def test_load_session_data_not_found(self, data_service):
        """测试加载不存在的会话数据"""
        with patch.object(data_service, 'storage_service') as mock_storage:
            mock_storage.is_available.return_value = True
            mock_storage.load_search_session.return_value = None
            
            result = data_service.load_session_data("nonexistent_session")
            assert result is None

    def test_list_sessions_with_user(self, data_service):
        """测试列出用户会话"""
        with patch.object(data_service, 'storage_service') as mock_storage:
            mock_storage.is_available.return_value = True
            mock_sessions = [
                {
                    'session_id': 'session_1',
                    'query': 'test query 1',
                    'timestamp': datetime.now()
                }
            ]
            mock_storage.list_user_sessions.return_value = mock_sessions
            
            result = data_service.list_sessions(owner_user_id=1)
            assert result == mock_sessions


class TestAuthService:
    """认证服务测试"""

    @pytest.fixture
    def auth_service(self):
        """创建认证服务实例"""
        return AuthService()

    def test_hash_password(self, auth_service):
        """测试密码哈希"""
        password = "test_password"
        hashed = auth_service.hash_password(password)
        
        assert hashed is not None
        assert hashed != password
        assert len(hashed) > 0

    def test_verify_password_correct(self, auth_service):
        """测试正确密码验证"""
        password = "test_password"
        hashed = auth_service.hash_password(password)
        
        result = auth_service.verify_password(password, hashed)
        assert result is True

    def test_verify_password_incorrect(self, auth_service):
        """测试错误密码验证"""
        password = "test_password"
        wrong_password = "wrong_password"
        hashed = auth_service.hash_password(password)
        
        result = auth_service.verify_password(wrong_password, hashed)
        assert result is False

    def test_generate_verification_code(self, auth_service):
        """测试生成验证码"""
        code = auth_service.generate_verification_code()
        
        assert code is not None
        assert isinstance(code, str)
        assert len(code) == 6
        assert code.isdigit()

    def test_store_verification_code(self, auth_service):
        """测试存储验证码"""
        email = "<EMAIL>"
        code = "123456"
        
        # 这个测试依赖于具体实现
        # 这里只测试方法不抛出异常
        try:
            auth_service.store_verification_code(email, code)
            # 如果没有异常，测试通过
            assert True
        except Exception as e:
            pytest.fail(f"store_verification_code raised {e}")


class TestEmailService:
    """邮件服务测试"""

    @pytest.fixture
    def email_service(self):
        """创建邮件服务实例"""
        return EmailService()

    def test_send_email_success(self, email_service):
        """测试成功发送邮件"""
        with patch('smtplib.SMTP') as mock_smtp:
            mock_server = MagicMock()
            mock_smtp.return_value.__enter__.return_value = mock_server
            
            result = email_service.send_email(
                to_email="<EMAIL>",
                subject="Test Subject",
                body="Test Body"
            )
            
            assert result is True

    def test_send_email_failure(self, email_service):
        """测试邮件发送失败"""
        with patch('smtplib.SMTP') as mock_smtp:
            mock_smtp.side_effect = Exception("SMTP error")
            
            result = email_service.send_email(
                to_email="<EMAIL>",
                subject="Test Subject",
                body="Test Body"
            )
            
            assert result is False


class TestGrokRedditService:
    """Grok Reddit服务测试"""

    @pytest.fixture
    def grok_service(self):
        """创建Grok服务实例"""
        return GrokRedditService()

    async def test_search_reddit_not_configured(self, grok_service):
        """测试未配置Grok服务时的搜索"""
        grok_service.configured = False
        
        result = await grok_service.search_reddit("test query")
        
        assert result["success"] is False
        assert "未配置" in result["error"]

    async def test_search_reddit_success(self, grok_service):
        """测试成功的Grok搜索"""
        if not grok_service.configured:
            pytest.skip("Grok API not configured")
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            # Mock HTTP响应
            mock_response = MagicMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value={
                "choices": [{
                    "message": {
                        "content": "Search results with citations"
                    }
                }]
            })
            mock_post.return_value.__aenter__.return_value = mock_response
            
            result = await grok_service.search_reddit("Python programming")
            
            assert result["success"] is True
            assert "search_time" in result

    async def test_search_reddit_api_error(self, grok_service):
        """测试Grok API错误"""
        if not grok_service.configured:
            pytest.skip("Grok API not configured")
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_response = MagicMock()
            mock_response.status = 429  # Rate limit
            mock_response.text = AsyncMock(return_value="Rate limit exceeded")
            mock_post.return_value.__aenter__.return_value = mock_response
            
            result = await grok_service.search_reddit("test query")
            
            assert result["success"] is False
            assert "429" in result["error"] or "rate" in result["error"].lower()
