"""
CogBridges Search - 数据库服务
实现PostgreSQL数据库的CRUD操作和数据迁移功能
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
import services.storage_service as storage_service
from services.storage_service import Session

from contextlib import contextmanager

from config import config
from models.database_models import (
    Base, SearchSession, RedditPost, RedditComment,
    UserHistory, SubredditSimilarity, CommentMotivationAnalysis,
    SiteVisit, AnonymousUsageLimit, UserAccount, PointsLedger,
)
from models.search_models import SearchResult, SearchQuery

from utils.logger_utils import get_logger


class DatabaseService:
    """数据库服务类"""
    
    def __init__(self):
        """初始化数据库服务"""
        self.logger = get_logger(__name__)
        self.engine = None
        self.SessionLocal = None
        
        # 在存在显式 DATABASE_URL 时，允许跳过 ENABLE_DATABASE 开关以便测试/简化部署
        if (config.ENABLE_DATABASE and config.database_configured) or bool(config.DATABASE_URL):
            try:
                self._initialize_database()
                # 自动建表（幂等）
                self.create_tables()
                self.logger.info("数据库服务初始化成功")
            except Exception as e:
                self.logger.error(f"数据库服务初始化失败: {e}")
                # Clean up on failure
                self.cleanup()
                raise
        else:
            self.logger.warning("数据库未启用或未配置")
    
    def cleanup(self):
        """清理数据库连接和资源"""
        try:
            if self.engine:
                self.engine.dispose()
                self.logger.info("数据库连接池已清理")
        except Exception as e:
            self.logger.error(f"清理数据库连接时出错: {e}")
    
    def __del__(self):
        """析构函数 - 确保连接被清理"""
        self.cleanup()
    
    def _initialize_database(self):
        """初始化 PostgreSQL 数据库连接"""
        try:
            # 仅支持 PostgreSQL 数据库
            url = config.database_url
            if not url.startswith('postgresql'):
                raise ValueError(f"仅支持 PostgreSQL 数据库，当前 URL: {url}")

            # PostgreSQL 连接池配置
            pool_size = int(getattr(config, 'DB_POOL_SIZE', 5))
            max_overflow = int(getattr(config, 'DB_MAX_OVERFLOW', 10))

            # Supabase Session Pooler 优化
            try:
                url_lower = (url or "").lower()
                if ("supabase.com" in url_lower) and ("pooler" in url_lower) and (":5432" in url_lower):
                    pool_size = min(pool_size, 2)
                    max_overflow = min(max_overflow, 1)
                    self.logger.warning(f"Detected Supabase Session Pooler. Reducing pool_size to {pool_size} and max_overflow to {max_overflow}. "
                                     f"Consider switching to Transaction Pooler (port 6543) for better concurrency.")
            except Exception:
                pass

            engine_kwargs = {
                "echo": config.DEBUG_MODE,
                "pool_pre_ping": True,
                "pool_size": pool_size,
                "max_overflow": max_overflow,
                "pool_timeout": config.DB_POOL_TIMEOUT,
                "pool_recycle": 300,
            }

            self.engine = storage_service.create_engine(url, **engine_kwargs)
            
            self.SessionLocal = storage_service.sessionmaker(
                autocommit=False,
                autoflush=False,
                expire_on_commit=False,
                bind=self.engine,
            )
            
            with self.engine.connect() as conn:
                conn.execute(storage_service.text("SELECT 1"))

            self.logger.info("数据库连接建立成功")
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """获取数据库会话的上下文管理器"""
        if not self.SessionLocal:
            raise RuntimeError("数据库未初始化")
        
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            self.logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            session.close()
    
    def create_tables(self):
        """创建数据库表"""
        if not self.engine:
            raise RuntimeError("数据库引擎未初始化")
        
        try:
            Base.metadata.create_all(bind=self.engine)
            self.logger.info("数据库表创建成功")
            # 为PostgreSQL创建优化索引（幂等）
            try:
                self._create_optimized_indexes_postgres()
            except Exception as idx_err:
                # 索引失败不应中断应用启动
                self.logger.warning(f"创建优化索引时出错（已忽略）: {idx_err}")
            # 执行一次性迁移：将 reddit_posts / reddit_comments 调整为复合主键 (session_id, id)
            try:
                self._migrate_schema_if_needed()
            except Exception as mig_err:
                # 迁移失败不阻挡运行，但会记录告警
                self.logger.warning(f"数据库结构迁移未完成（已忽略）: {mig_err}")
        except Exception as e:
            self.logger.error(f"创建数据库表失败: {e}")
            raise

    def _create_optimized_indexes_postgres(self) -> None:
        """
        在PostgreSQL上为热点查询和JSON(B)字段创建优化索引。
        幂等：使用 IF NOT EXISTS。
        """
        try:
            dialect_name = getattr(getattr(self.engine, 'dialect', None), 'name', '')
            if str(dialect_name).lower() not in ("postgres", "postgresql"):
                return
        except Exception:
            return

        stmts = [
            # JSON/JSONB 字段：通过函数索引统一为 jsonb 类型以支持 GIN
            "CREATE INDEX IF NOT EXISTS ix_search_sessions_raw_data_gin ON search_sessions USING GIN ((raw_data::jsonb));",
            "CREATE INDEX IF NOT EXISTS ix_user_accounts_trial_flags_gin ON user_accounts USING GIN ((trial_flags::jsonb));",
            "CREATE INDEX IF NOT EXISTS ix_points_ledger_metadata_gin ON points_ledger USING GIN ((metadata::jsonb));",
            "CREATE INDEX IF NOT EXISTS ix_subreddit_similarities_results_gin ON subreddit_similarities USING GIN ((similarity_results::jsonb));",
            # 时间/范围过滤
            "CREATE INDEX IF NOT EXISTS ix_reddit_posts_created_utc ON reddit_posts (created_utc);",
            "CREATE INDEX IF NOT EXISTS ix_reddit_comments_created_utc ON reddit_comments (created_utc);",
        ]

        with self.engine.connect() as conn:
            for sql in stmts:
                try:
                    conn.execute(storage_service.text(sql))
                except Exception as e:
                    # 个别索引失败不影响整体
                    self.logger.debug(f"索引创建失败但已略过: {sql} -> {e}")

    def _migrate_schema_if_needed(self) -> None:
        """在PostgreSQL上将 reddit_posts / reddit_comments 的主键迁移为 (session_id, id)，
        并移除 reddit_comments.post_id 的外键约束（与复合主键不兼容）。
        同时清理任何遗留的仅对 id 列的唯一约束/索引，避免跨会话写入冲突。
        幂等：均使用 IF EXISTS/IF NOT EXISTS。
        """
        try:
            dialect_name = getattr(getattr(self.engine, 'dialect', None), 'name', '')
            if str(dialect_name).lower() not in ("postgres", "postgresql"):
                return
        except Exception:
            return

        # 在变更主键前尝试删除历史上可能遗留的唯一约束/索引（仅针对 id 单列）
        cleanup_sqls = [
            # reddit_comments: 先删除可能存在的 UNIQUE 约束
            "ALTER TABLE IF EXISTS reddit_comments DROP CONSTRAINT IF EXISTS reddit_comments_id_key;",
            # reddit_posts: 删除可能存在的 UNIQUE 约束
            "ALTER TABLE IF EXISTS reddit_posts DROP CONSTRAINT IF EXISTS reddit_posts_id_key;",
        ]

        # 动态清理仅作用于(id)的唯一索引（不包含 session_id）
        dynamic_cleanup_sql = """
        SELECT indexname
        FROM pg_indexes
        WHERE schemaname = current_schema()
          AND tablename IN ('reddit_comments','reddit_posts')
          AND indexdef ILIKE '%UNIQUE%'
          AND indexdef ILIKE '%(id)%'
          AND indexdef NOT ILIKE '%session_id%';
        """

        stmts = [
            # 先移除可能存在的外键（评论->帖子）以避免复合键约束冲突
            "ALTER TABLE IF EXISTS reddit_comments DROP CONSTRAINT IF EXISTS reddit_comments_post_id_fkey;",
            # reddit_posts 主键迁移
            "ALTER TABLE IF EXISTS reddit_posts DROP CONSTRAINT IF EXISTS reddit_posts_pkey;",
            "ALTER TABLE IF EXISTS reddit_posts ADD CONSTRAINT reddit_posts_pkey PRIMARY KEY (session_id, id);",
            # reddit_comments 主键迁移
            "ALTER TABLE IF EXISTS reddit_comments DROP CONSTRAINT IF EXISTS reddit_comments_pkey;",
            "ALTER TABLE IF EXISTS reddit_comments ADD CONSTRAINT reddit_comments_pkey PRIMARY KEY (session_id, id);",
            # 补充有用索引
            "CREATE INDEX IF NOT EXISTS ix_reddit_posts_session_sub ON reddit_posts (session_id, subreddit);",
            "CREATE INDEX IF NOT EXISTS ix_reddit_comments_session_post ON reddit_comments (session_id, post_id);",
        ]

        with self.engine.connect() as conn:
            # 先做静态清理
            for sql in cleanup_sqls:
                try:
                    conn.execute(storage_service.text(sql))
                except Exception as e:
                    self.logger.debug(f"约束清理失败但已略过: {sql} -> {e}")
            
            # 动态删除仅(id)的唯一索引
            try:
                rows = conn.execute(storage_service.text(dynamic_cleanup_sql)).fetchall()
                for (indexname,) in rows:
                    try:
                        drop_sql = f"DROP INDEX IF EXISTS \"{indexname}\";"
                        conn.execute(storage_service.text(drop_sql))
                    except Exception as e:
                        self.logger.debug(f"索引清理失败但已略过: {indexname} -> {e}")
            except Exception as e:
                self.logger.debug(f"查询遗留唯一索引失败（已忽略）: {e}")

            # 应用主键迁移与必要索引
            for sql in stmts:
                try:
                    conn.execute(storage_service.text(sql))
                except Exception as e:
                    # 单条失败不影响整体（可能已处于目标状态）
                    self.logger.debug(f"迁移语句执行失败但已略过: {sql} -> {e}")

    
    def drop_tables(self):
        """删除数据库表（谨慎使用）"""
        if not self.engine:
            raise RuntimeError("数据库引擎未初始化")
        
        try:
            Base.metadata.drop_all(bind=self.engine)
            self.logger.warning("数据库表已删除")
        except Exception as e:
            self.logger.error(f"删除数据库表失败: {e}")
            raise
    
    def save_search_session(
        self,
        session_id: str,
        search_result: SearchResult,
        reddit_data: Optional[Dict[str, Any]] = None,
        raw_data: Optional[Dict[str, Any]] = None,
        llm_analysis: Optional[Dict[str, Any]] = None,
        owner_user_id: Optional[int] = None,
        anonymous_cookie_id: Optional[str] = None
    ) -> bool:
        """
        保存搜索会话数据

        Args:
            session_id: 会话ID
            search_result: 搜索结果对象
            reddit_data: Reddit数据
            raw_data: 原始JSON数据（用于备份）
            llm_analysis: LLM分析结果
            owner_user_id: 用户ID（可为空，表示匿名用户）
            anonymous_cookie_id: 匿名用户cookie ID（可为空，表示注册用户）

        Returns:
            保存是否成功
        """
        try:
            with self.get_session() as session:
                # 创建搜索会话记录
                # 会话级度量（毫秒）
                total_time_ms = int((getattr(search_result, 'total_time', 0.0) or 0.0) * 1000)
                grok_time_ms = int((getattr(search_result, 'google_search_time', 0.0) or 0.0) * 1000)
                reddit_time_ms = int((getattr(search_result, 'reddit_posts_time', 0.0) or 0.0) * 1000)
                history_time_ms = int((getattr(search_result, 'commenters_history_time', 0.0) or 0.0) * 1000)
                llm_time_ms = int((getattr(search_result, 'llm_analysis_time', 0.0) or 0.0) * 1000)

                # 确定是否为匿名用户
                is_anonymous = owner_user_id is None or owner_user_id == 0
                
                search_session = SearchSession(
                    id=session_id,
                    query=search_result.query.query,
                    timestamp=search_result.query.timestamp,
                    search_type=search_result.query.search_type,
                    max_results=search_result.query.max_results,
                    site_filter=search_result.query.site_filter,
                    success=search_result.success,
                    error_message=search_result.error_message,
                    raw_data=(raw_data if getattr(config, 'DB_STORE_RAW_DATA', True) else None),
                    owner_user_id=owner_user_id if not is_anonymous else None,
                    anonymous_cookie_id=anonymous_cookie_id if is_anonymous else None,
                    is_anonymous=is_anonymous,
                    total_time_ms=total_time_ms,
                    grok_time_ms=grok_time_ms,
                    reddit_time_ms=reddit_time_ms,
                    history_time_ms=history_time_ms,
                    llm_time_ms=llm_time_ms,
                )

                # 添加LLM分析信息
                if llm_analysis:
                    search_session.llm_analysis_success = llm_analysis.get('success', False)
                    search_session.llm_analysis_time = llm_analysis.get('analysis_time', 0.0)
                    search_session.llm_analysis_error = llm_analysis.get('error')

                # 首先添加并提交搜索会话记录，确保外键约束满足
                session.add(search_session)
                session.flush()  # 立即执行插入，但不提交事务

                # 保存Reddit数据（现在search_session已存在）
                if reddit_data:
                    self._save_reddit_data(session, session_id, reddit_data, search_session)

                # 保存LLM分析数据
                if llm_analysis:
                    self._save_llm_analysis_data(session, session_id, llm_analysis)
                
            self.logger.info(f"搜索会话数据保存成功: {session_id} (匿名用户: {is_anonymous})")
            return True
            
        except Exception as e:
            self.logger.error(f"保存搜索会话数据失败: {e}")
            try:
                if getattr(config, 'TEST_MODE', False):
                    raise
            except Exception:
                raise
            return False
    
    def _save_reddit_data(
        self,
        session: Session,
        session_id: str,
        reddit_data: Dict[str, Any],
        search_session: SearchSession
    ):
        """保存Reddit数据到数据库"""
        posts_count = 0
        comments_count = 0
        
        # 保存Reddit帖子
        # 支持两种键名格式以保持向后兼容
        reddit_posts = reddit_data.get('posts') or reddit_data.get('reddit_posts', [])
        saved_post_ids = set()  # 记录已保存的帖子ID
        if reddit_posts:
            for post_data in reddit_posts:
                if isinstance(post_data, dict):
                    post_id = (post_data.get('id') or '')
                    if post_id:
                        reddit_post = RedditPost(
                            id=post_id,
                            session_id=session_id,
                            title=(post_data.get('title') or ''),
                            selftext=(post_data.get('selftext') or ''),
                            author=(post_data.get('author') or ''),
                            score=int(post_data.get('score', 0) or 0),
                            num_comments=int(post_data.get('num_comments', 0) or 0),
                            created_utc=float(post_data.get('created_utc', 0) or 0),
                            subreddit=(post_data.get('subreddit') or ''),
                            permalink=(post_data.get('permalink') or ''),
                            url=(post_data.get('url') or '')
                        )
                        session.add(reddit_post)
                        saved_post_ids.add(post_id)
                        posts_count += 1
        
        # 先提交帖子，确保外键约束满足
        session.flush()
        
        # 保存Reddit评论（去重同一会话内的重复ID）
        # 支持两种键名格式以保持向后兼容
        reddit_comments = reddit_data.get('comments') or reddit_data.get('reddit_comments', [])
        if reddit_comments:
            seen_ids_in_session: set = set()
            for comment_data in reddit_comments:
                if not isinstance(comment_data, dict):
                    continue
                cid = (comment_data.get('id') or '')
                if not cid or cid in seen_ids_in_session:
                    continue
                
                # 检查评论的 post_id 是否在已保存的帖子中
                comment_post_id = comment_data.get('post_id')
                if comment_post_id and comment_post_id not in saved_post_ids:
                    # 如果帖子不存在，创建一个最小的帖子记录
                    minimal_post = RedditPost(
                        id=comment_post_id,
                        session_id=session_id,
                        title='[Post data not available]',
                        selftext='',
                        author='[deleted]',
                        score=0,
                        num_comments=0,
                        created_utc=0,
                        subreddit='',
                        permalink='',
                        url=''
                    )
                    session.add(minimal_post)
                    saved_post_ids.add(comment_post_id)
                    session.flush()
                
                seen_ids_in_session.add(cid)
                reddit_comment = RedditComment(
                    id=cid,
                    session_id=session_id,
                    post_id=comment_post_id,
                    body=(comment_data.get('body') or ''),
                    author=(comment_data.get('author') or ''),
                    score=int(comment_data.get('score', 0) or 0),
                    created_utc=float(comment_data.get('created_utc', 0) or 0),
                    parent_id=(comment_data.get('parent_id') or ''),
                    subreddit=(comment_data.get('subreddit') or ''),
                    permalink=(comment_data.get('permalink') or ''),
                    relevance_score=float(comment_data.get('relevance_score', 0.0) or 0.0)
                )
                session.add(reddit_comment)
                comments_count += 1
        
        # 保存用户历史数据
        if 'user_histories' in reddit_data:
            for user_data in reddit_data['user_histories']:
                if isinstance(user_data, dict):
                    user_history = UserHistory(
                        session_id=session_id,
                        username=user_data.get('username', ''),
                        total_comments=user_data.get('total_comments', 0),
                        total_posts=user_data.get('total_posts', 0),
                        account_created_utc=user_data.get('account_created_utc'),
                        comments_data=user_data.get('comments_data'),
                        posts_data=user_data.get('posts_data')
                    )
                    session.add(user_history)
        
        # 更新统计信息
        search_session.reddit_posts_count = posts_count
        search_session.reddit_comments_count = comments_count

    def _save_llm_analysis_data(
        self,
        session: Session,
        session_id: str,
        llm_analysis: Dict[str, Any]
    ):
        """保存LLM分析数据到数据库"""
        try:
            # 保存子版块相似性分析结果
            similarity_analysis = llm_analysis.get('similarity_analysis', {})
            if similarity_analysis:
                for username, user_similarity in similarity_analysis.items():
                    if isinstance(user_similarity, dict):
                        subreddit_similarity = SubredditSimilarity(
                            session_id=session_id,
                            username=username,
                            target_subreddits=user_similarity.get('target_subreddits', []),
                            user_subreddits=user_similarity.get('user_subreddits', []),
                            similarity_results=user_similarity.get('similarity_results', {}),
                            all_similar_subreddits=user_similarity.get('all_similar_subreddits', []),
                            analysis_success=user_similarity.get('success', True),
                            analysis_error=user_similarity.get('error'),
                            llm_response_raw=user_similarity.get('llm_response_raw')
                        )
                        session.add(subreddit_similarity)

            # 保存评论动机分析结果
            motivation_analysis = llm_analysis.get('motivation_analysis', {})
            if motivation_analysis:
                for username, user_motivations in motivation_analysis.items():
                    if isinstance(user_motivations, list):
                        for motivation_data in user_motivations:
                            if isinstance(motivation_data, dict):
                                comment_motivation = CommentMotivationAnalysis(
                                    session_id=session_id,
                                    username=username,
                                    comment_id=motivation_data.get('comment_id'),
                                    target_subreddit=motivation_data.get('target_subreddit', ''),
                                    professional_background=motivation_data.get('professional_background'),
                                    participation_motivation=motivation_data.get('participation_motivation'),
                                    interest_areas=motivation_data.get('interest_areas'),
                                    user_profile=motivation_data.get('user_profile'),
                                    matching_value=motivation_data.get('matching_value'),
                                    overall_assessment=motivation_data.get('overall_assessment'),
                                    analysis_success=motivation_data.get('success', True),
                                    analysis_error=motivation_data.get('error'),
                                    llm_response_raw=motivation_data.get('raw_analysis'),
                                    target_post_data=motivation_data.get('target_post_data'),
                                    user_comment_data=motivation_data.get('user_comment_data'),
                                    similar_subreddits_data=motivation_data.get('similar_subreddits_data'),
                                    user_overview_data=motivation_data.get('user_overview_data')
                                )
                                session.add(comment_motivation)

            self.logger.info(f"LLM分析数据保存成功: {session_id}")

        except Exception as e:
            self.logger.error(f"保存LLM分析数据失败: {e}")
            # 不抛出异常，允许其他数据继续保存

    def _load_llm_analysis_data(self, session: Session, session_id: str) -> Optional[Dict[str, Any]]:
        """从数据库加载LLM分析数据，并从 raw_data 兼容性合并 credibility_analysis。"""
        try:
            llm_analysis: Dict[str, Any] = {}

            # 加载子版块相似性分析
            subreddit_similarities = session.query(SubredditSimilarity).filter(
                SubredditSimilarity.session_id == session_id
            ).all()

            if subreddit_similarities:
                similarity_analysis: Dict[str, Any] = {}
                for similarity in subreddit_similarities:
                    similarity_analysis[similarity.username] = similarity.to_dict()
                if similarity_analysis:
                    llm_analysis['similarity_analysis'] = similarity_analysis

            # 加载评论动机分析
            motivation_analyses = session.query(CommentMotivationAnalysis).filter(
                CommentMotivationAnalysis.session_id == session_id
            ).all()

            if motivation_analyses:
                motivation_analysis: Dict[str, Any] = {}
                for motivation in motivation_analyses:
                    username = motivation.username
                    if username not in motivation_analysis:
                        motivation_analysis[username] = []
                    motivation_analysis[username].append(motivation.to_dict())
                if motivation_analysis:
                    llm_analysis['motivation_analysis'] = motivation_analysis

            # 兼容历史：从 raw_data 合并 credibility_analysis（如果数据库未结构化存储）
            try:
                search_session = session.query(SearchSession).filter(SearchSession.id == session_id).first()
                raw = getattr(search_session, 'raw_data', None) if search_session else None
                if isinstance(raw, dict):
                    raw_llm = raw.get('llm_analysis') or raw.get('search_result', {}).get('llm_analysis')
                    if isinstance(raw_llm, dict) and raw_llm.get('credibility_analysis'):
                        # 仅当当前 llm_analysis 中没有 credibility_analysis 时合并，避免覆盖将来结构化存储
                        if 'credibility_analysis' not in llm_analysis:
                            llm_analysis['credibility_analysis'] = raw_llm.get('credibility_analysis')
            except Exception as merge_err:
                # 合并失败不应影响主流程
                self.logger.warning(f"兼容性合并 credibility_analysis 失败（已忽略）: {merge_err}")

            return llm_analysis if llm_analysis else None

        except Exception as e:
            self.logger.error(f"加载LLM分析数据失败: {e}")
            return None

    def load_search_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        加载搜索会话数据

        Args:
            session_id: 会话ID

        Returns:
            会话数据字典
        """
        try:
            with self.get_session() as session:
                # 查询搜索会话
                search_session = session.query(SearchSession).filter(
                    SearchSession.id == session_id
                ).first()

                if not search_session:
                    self.logger.warning(f"未找到会话: {session_id}")
                    return None

                # 构建完整的会话数据
                session_data = search_session.to_dict()

                session_data['google_results'] = []

                # 加载Reddit帖子
                reddit_posts = session.query(RedditPost).filter(
                    RedditPost.session_id == session_id
                ).all()

                session_data['reddit_posts'] = [post.to_dict() for post in reddit_posts]

                # 加载Reddit评论
                reddit_comments = session.query(RedditComment).filter(
                    RedditComment.session_id == session_id
                ).all()

                session_data['reddit_comments'] = [comment.to_dict() for comment in reddit_comments]

                # 加载用户历史
                user_histories = session.query(UserHistory).filter(
                    UserHistory.session_id == session_id
                ).all()

                session_data['user_histories'] = [history.to_dict() for history in user_histories]

                # 加载LLM分析数据
                llm_analysis_data = self._load_llm_analysis_data(session, session_id)
                if llm_analysis_data:
                    session_data['llm_analysis'] = llm_analysis_data

                self.logger.info(f"会话数据加载成功: {session_id}")
                return session_data

        except Exception as e:
            self.logger.error(f"加载会话数据失败: {e}")
            return None

    def list_search_sessions(self, limit: int = 50, offset: int = 0, owner_user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        列出搜索会话

        Args:
            limit: 返回数量限制
            offset: 偏移量

        Returns:
            会话信息列表
        """
        try:
            with self.get_session() as session:
                query = session.query(SearchSession)
                if owner_user_id is not None:
                    query = query.filter(SearchSession.owner_user_id == int(owner_user_id))
                sessions = query.order_by(
                    SearchSession.created_at.desc()
                ).offset(offset).limit(limit).all()

                return [s.to_dict() for s in sessions]

        except Exception as e:
            self.logger.error(f"列出会话失败: {e}")
            return []

    def delete_search_session(self, session_id: str) -> bool:
        """
        删除搜索会话数据

        Args:
            session_id: 会话ID

        Returns:
            删除是否成功
        """
        try:
            with self.get_session() as session:
                # 查询会话
                search_session = session.query(SearchSession).filter(
                    SearchSession.id == session_id
                ).first()

                if not search_session:
                    self.logger.warning(f"未找到会话: {session_id}")
                    return False

                # 删除会话（级联删除相关数据）
                session.delete(search_session)

                self.logger.info(f"会话数据删除成功: {session_id}")
                return True

        except Exception as e:
            self.logger.error(f"删除会话数据失败: {e}")
            return False

    # ------------------------------
    # 新增：站点访问记录
    # ------------------------------
    def save_site_visit(
        self,
        anonymous_cookie_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        referrer: Optional[str] = None,
        landing_path: Optional[str] = None,
        utm_source: Optional[str] = None,
        utm_medium: Optional[str] = None,
        utm_campaign: Optional[str] = None,
    ) -> bool:
        """保存站点访问记录（隐私友好：IP 使用 SECRET_KEY 加盐哈希）。"""
        try:
            # 计算 IP 哈希
            ip_hash: Optional[str] = None
            try:
                if ip_address:
                    import hashlib
                    salt = str(getattr(config, 'SECRET_KEY', 'cogbridges-salt'))
                    ip_hash = hashlib.sha256((salt + '|' + ip_address).encode('utf-8')).hexdigest()
            except Exception:
                ip_hash = None

            with self.get_session() as session:
                visit = SiteVisit(
                    anonymous_cookie_id=(str(anonymous_cookie_id) if anonymous_cookie_id else None),
                    ip_hash=ip_hash,
                    user_agent=(user_agent or None),
                    referrer=(referrer or None),
                    landing_path=(landing_path or None),
                    utm_source=(utm_source or None),
                    utm_medium=(utm_medium or None),
                    utm_campaign=(utm_campaign or None),
                )
                session.add(visit)
            return True
        except Exception as e:
            self.logger.error(f"保存站点访问记录失败: {e}")
            return False

    def get_database_statistics(self) -> Dict[str, Any]:
        """
        获取数据库统计信息

        Returns:
            数据库统计信息字典
        """
        try:
            with self.get_session() as session:
                # 统计各表记录数
                sessions_count = session.query(SearchSession).count()
                google_results_count = 0
                reddit_posts_count = session.query(RedditPost).count()
                reddit_comments_count = session.query(RedditComment).count()
                user_histories_count = session.query(UserHistory).count()
                subreddit_similarities_count = session.query(SubredditSimilarity).count()
                motivation_analyses_count = session.query(CommentMotivationAnalysis).count()

                # 获取最新会话时间
                latest_session = session.query(SearchSession).order_by(
                    SearchSession.created_at.desc()
                ).first()

                latest_session_time = None
                if latest_session:
                    latest_session_time = latest_session.created_at.isoformat()

                return {
                    "database_enabled": config.ENABLE_DATABASE,
                    "database_configured": config.database_configured,
                    "sessions_count": sessions_count,
                    "google_results_count": google_results_count,
                    "reddit_posts_count": reddit_posts_count,
                    "reddit_comments_count": reddit_comments_count,
                    "user_histories_count": user_histories_count,
                    "subreddit_similarities_count": subreddit_similarities_count,
                    "motivation_analyses_count": motivation_analyses_count,
                    "latest_session_time": latest_session_time,
                    "database_url_configured": bool(config.DATABASE_URL)
                }

        except Exception as e:
            self.logger.error(f"获取数据库统计失败: {e}")
            return {
                "database_enabled": config.ENABLE_DATABASE,
                "database_configured": config.database_configured,
                "error": str(e)
            }

    # ------------------------------
    # 匿名使用次数限制（数据库存储）
    # ------------------------------
    def get_anonymous_usage(self, cookie_id: Optional[str], ip_address: Optional[str]):
        try:
            with self.get_session() as session:
                q = session.query(AnonymousUsageLimit)
                if cookie_id and ip_address:
                    q = q.filter(AnonymousUsageLimit.cookie_id == cookie_id, AnonymousUsageLimit.ip_address == ip_address)
                elif cookie_id:
                    q = q.filter(AnonymousUsageLimit.cookie_id == cookie_id)
                elif ip_address:
                    q = q.filter(AnonymousUsageLimit.ip_address == ip_address)
                else:
                    return None
                return q.order_by(AnonymousUsageLimit.updated_at.desc()).first()
        except Exception as e:
            self.logger.error(f"查询匿名使用记录失败: {e}")
            return None

    def increment_anonymous_usage(self, cookie_id: Optional[str], ip_address: Optional[str]):
        from datetime import datetime
        try:
            with self.get_session() as session:
                record = None
                if cookie_id and ip_address:
                    record = session.query(AnonymousUsageLimit).filter(
                        AnonymousUsageLimit.cookie_id == cookie_id,
                        AnonymousUsageLimit.ip_address == ip_address
                    ).first()
                if not record and cookie_id:
                    record = session.query(AnonymousUsageLimit).filter(
                        AnonymousUsageLimit.cookie_id == cookie_id,
                        AnonymousUsageLimit.ip_address.is_(None)
                    ).first()
                if not record and ip_address:
                    record = session.query(AnonymousUsageLimit).filter(
                        AnonymousUsageLimit.cookie_id.is_(None),
                        AnonymousUsageLimit.ip_address == ip_address
                    ).first()

                if not record:
                    record = AnonymousUsageLimit(cookie_id=cookie_id, ip_address=ip_address, usage_count=0, last_used_at=datetime.utcnow())
                    session.add(record)

                record.usage_count = int(record.usage_count or 0) + 1
                record.last_used_at = datetime.utcnow()
                session.commit()
                session.refresh(record)
                return record
        except Exception as e:
            self.logger.error(f"递增匿名使用记录失败: {e}")
            raise

    def migrate_json_to_database(self, json_file_path: str) -> bool:
        """
        将JSON文件数据迁移到数据库

        Args:
            json_file_path: JSON文件路径

        Returns:
            迁移是否成功
        """
        try:
            # 读取JSON文件
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 提取会话ID（从文件名或数据中）
            session_id = data.get('session_id')
            if not session_id:
                # 从文件名提取
                import os
                filename = os.path.basename(json_file_path)
                if filename.startswith('complete_session_'):
                    session_id = filename.replace('complete_session_', '').replace('.json', '')
                else:
                    self.logger.error(f"无法确定会话ID: {json_file_path}")
                    return False

            # 检查会话是否已存在
            with self.get_session() as session:
                existing = session.query(SearchSession).filter(
                    SearchSession.id == session_id
                ).first()

                if existing:
                    self.logger.warning(f"会话已存在，跳过迁移: {session_id}")
                    return True

            # 构建搜索结果对象
            search_result_data = data.get('search_result', {})
            query_data = search_result_data.get('query', {})

            search_query = SearchQuery(
                query=query_data.get('query', ''),
                timestamp=datetime.fromisoformat(query_data.get('timestamp', datetime.now().isoformat())),
                search_type=query_data.get('search_type', 'reddit'),
                max_results=query_data.get('max_results', 5),
                site_filter=query_data.get('site_filter', 'site:reddit.com')
            )

            search_result = SearchResult(
                query=search_query,
                success=search_result_data.get('success', True),
                error_message=search_result_data.get('error_message')
            )

            search_result.google_results = []

            # 保存到数据库
            reddit_data = data.get('reddit_data', {})
            owner_user_id = None
            try:
                raw_owner = data.get('_owner_user_id')
                if raw_owner is not None:
                    owner_user_id = int(raw_owner)
            except Exception:
                owner_user_id = None

            success = self.save_search_session(
                session_id=session_id,
                search_result=search_result,
                reddit_data=reddit_data,
                raw_data=data,
                owner_user_id=owner_user_id
            )

            if success:
                self.logger.info(f"JSON数据迁移成功: {json_file_path}")
            else:
                self.logger.error(f"JSON数据迁移失败: {json_file_path}")

            return success

        except Exception as e:
            self.logger.error(f"迁移JSON数据失败: {e}")
            return False

    def is_available(self) -> bool:
        """检查数据库服务是否可用"""
        return (((config.ENABLE_DATABASE and config.database_configured) or bool(config.DATABASE_URL)) and
                self.engine is not None and
                self.SessionLocal is not None)

    # ------------------------------
    # 新增：用户账户相关操作
    # ------------------------------
    def get_user_by_email(self, email: str):
        """通过邮箱获取用户"""
        try:
            from models.database_models import UserAccount
            with self.get_session() as session:
                return session.query(UserAccount).filter(UserAccount.email == email).first()
        except Exception as e:
            self.logger.error(f"查询用户失败: {e}")
            return None

    def create_user(self, email: str, password_hash: str):
        """创建用户账户"""
        try:
            from models.database_models import UserAccount
            with self.get_session() as session:
                existing = session.query(UserAccount).filter(UserAccount.email == email).first()
                if existing:
                    return None
                # 新注册用户默认未激活，待邮箱验证
                user = UserAccount(email=email, password_hash=password_hash, is_active=False, email_verified=False)
                session.add(user)
                session.commit()  # 确保提交事务
                session.refresh(user)  # 刷新对象以获取生成的ID
                return user
        except Exception as e:
            self.logger.error(f"创建用户失败: {e}")
            return None

    def update_user_last_login(self, user_id: int):
        """更新用户最后登录时间"""
        try:
            from models.database_models import UserAccount
            with self.get_session() as session:
                user = session.get(UserAccount, user_id)
                if not user:
                    return False
                from datetime import datetime
                user.last_login = datetime.utcnow()
                session.add(user)
                return True
        except Exception as e:
            self.logger.error(f"更新最后登录失败: {e}")
            return False

    def mark_user_email_verified(self, email: str) -> bool:
        """通过邮箱设置用户为已验证并激活账户"""
        try:
            from models.database_models import UserAccount
            with self.get_session() as session:
                user = session.query(UserAccount).filter(UserAccount.email == email).first()
                if not user:
                    return False
                user.email_verified = True
                user.is_active = True
                from datetime import datetime
                user.updated_at = datetime.utcnow()
                session.add(user)
                return True
        except Exception as e:
            self.logger.error(f"标记邮箱验证失败: {e}")
            return False

    # ------------------------------
    # 邮箱验证码（DB-only）
    # ------------------------------
    def store_email_code(self, email: str, code: str, ttl_seconds: int = 600) -> bool:
        try:
            from datetime import datetime, timedelta
            from models.database_models import EmailVerificationCode
            with self.get_session() as session:
                # 可选：清理该邮箱的旧验证码
                try:
                    session.query(EmailVerificationCode).filter(EmailVerificationCode.email == email).delete()
                except Exception:
                    pass
                entry = EmailVerificationCode(
                    email=email,
                    code=str(code),
                    expires_at=datetime.utcnow() + timedelta(seconds=int(ttl_seconds))
                )
                session.add(entry)
            return True
        except Exception as e:
            self.logger.error(f"存储邮箱验证码失败: {e}")
            return False

    def verify_email_code(self, email: str, code: str) -> bool:
        try:
            from datetime import datetime
            from models.database_models import EmailVerificationCode
            with self.get_session() as session:
                entry = session.query(EmailVerificationCode).filter(
                    EmailVerificationCode.email == email,
                    EmailVerificationCode.code == str(code)
                ).order_by(EmailVerificationCode.created_at.desc()).first()
                if not entry:
                    return False
                if entry.expires_at and entry.expires_at < datetime.utcnow():
                    return False
                try:
                    session.delete(entry)
                except Exception:
                    pass
            return True
        except Exception as e:
            self.logger.error(f"验证邮箱验证码失败: {e}")
            return False