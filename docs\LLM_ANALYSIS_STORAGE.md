# CogBridges Search - LLM分析数据存储功能

本文档详细介绍CogBridges Search中LLM筛选子版块和用户画像分析的数据存储功能。

## 功能概述

CogBridges Search集成了完整的LLM分析数据存储系统，支持：

### 🎯 LLM筛选子版块功能
- **相似性分析**：使用LLM分析用户关注的子版块与目标子版块的相似性
- **批量筛选**：一次性分析多个用户的子版块相似性
- **结果存储**：将筛选结果保存到数据库和JSON文件

### 👤 LLM用户画像分析功能
- **动机分析**：分析用户在特定子版块中发表评论的动机
- **画像构建**：基于用户历史数据构建详细的用户画像
- **价值评估**：评估用户作为潜在合作伙伴的价值

## 数据存储架构

### 数据库表结构

#### 1. 搜索会话表 (search_sessions)
增加了LLM分析相关字段：
```sql
ALTER TABLE search_sessions ADD COLUMN llm_analysis_success BOOLEAN DEFAULT FALSE;
ALTER TABLE search_sessions ADD COLUMN llm_analysis_time FLOAT DEFAULT 0.0;
ALTER TABLE search_sessions ADD COLUMN llm_analysis_error TEXT;
```

#### 2. 子版块相似性分析表 (subreddit_similarities)
```sql
CREATE TABLE subreddit_similarities (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(50) REFERENCES search_sessions(id),
    username VARCHAR(100) NOT NULL,
    target_subreddits JSON NOT NULL,
    user_subreddits JSON NOT NULL,
    similarity_results JSON NOT NULL,
    all_similar_subreddits JSON,
    analysis_success BOOLEAN DEFAULT TRUE,
    analysis_error TEXT,
    llm_response_raw TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 3. 评论动机分析表 (comment_motivation_analyses)
```sql
CREATE TABLE comment_motivation_analyses (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(50) REFERENCES search_sessions(id),
    username VARCHAR(100) NOT NULL,
    comment_id VARCHAR(20) REFERENCES reddit_comments(id),
    target_subreddit VARCHAR(100) NOT NULL,
    professional_background TEXT,
    participation_motivation TEXT,
    interest_areas TEXT,
    user_profile TEXT,
    matching_value TEXT,
    overall_assessment TEXT,
    analysis_success BOOLEAN DEFAULT TRUE,
    analysis_error TEXT,
    llm_response_raw TEXT,
    target_post_data JSON,
    user_comment_data JSON,
    similar_subreddits_data JSON,
    user_overview_data JSON,
    created_at TIMESTAMP DEFAULT NOW()
);
```

注意：为保持与JSON记录的一致性，像 `reddit_posts` 与 `reddit_comments` 这类原子数据表不再内嵌存储派生分析字段（例如 `motivation_analysis`、`credibility_analysis`）。这些分析内容应保存在完整会话JSON（`search_sessions.raw_data`）或以上专用分析表中。

### JSON数据结构

#### 相似性分析数据
```json
{
  "similarity_analysis": {
    "username": {
      "target_subreddits": ["ChatGPT", "ClaudeAI"],
      "user_subreddits": ["programming", "Python", "MachineLearning"],
      "similarity_results": {
        "ChatGPT": ["programming", "Python"],
        "ClaudeAI": ["programming"]
      },
      "all_similar_subreddits": ["programming", "Python"],
      "analysis_success": true,
      "llm_response_raw": "LLM原始响应..."
    }
  }
}
```

#### 动机分析数据
```json
{
  "motivation_analysis": {
    "username": [
      {
        "comment_id": "abc123",
        "target_subreddit": "ChatGPT",
        "professional_background": "专业背景分析",
        "participation_motivation": "参与动机分析",
        "interest_areas": "兴趣领域分析",
        "user_profile": "用户画像推断",
        "matching_value": "潜在匹配价值",
        "overall_assessment": "总体评价",
        "analysis_success": true,
        "raw_analysis": "LLM原始分析结果"
      }
    ]
  }
}
```

## API接口

### 获取会话LLM分析数据
```http
GET /api/sessions/{session_id}/llm-analysis
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "session_id": "20250727_155937_485fcd66",
    "llm_analysis": {
      "success": true,
      "analysis_time": 15.6,
      "similarity_analysis": { ... },
      "motivation_analysis": { ... }
    },
    "summary": {
      "analysis_success": true,
      "analysis_time": 15.6,
      "similarity_users_count": 2,
      "motivation_users_count": 2,
      "total_motivation_analyses": 3
    }
  }
}
```

### 获取存储统计（包含LLM数据）
```http
GET /api/storage/statistics
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "database": {
      "sessions_count": 10,
      "subreddit_similarities_count": 25,
      "motivation_analyses_count": 45
    }
  }
}
```

## 使用示例

### 1. 保存LLM分析数据
```python
from services.data_service import DataService

data_service = DataService()

# 构建完整会话数据（包含LLM分析）
complete_session_data = {
    "session_id": session_id,
    "search_result": search_result.to_dict(),
    "reddit_data": reddit_data,
    "llm_analysis": {
        "success": True,
        "analysis_time": 15.6,
        "similarity_analysis": { ... },
        "motivation_analysis": { ... }
    }
}

# 保存数据
data_service.save_complete_session(session_id, complete_session_data)
```

### 2. 加载LLM分析数据
```python
# 加载会话数据
session_data = data_service.load_session_data(session_id)

# 获取LLM分析数据
llm_analysis = session_data.get('llm_analysis', {})
similarity_data = llm_analysis.get('similarity_analysis', {})
motivation_data = llm_analysis.get('motivation_analysis', {})
```

### 3. 查询特定用户的分析结果
```python
# 从数据库查询特定用户的相似性分析
from services.database_service import DatabaseService

db_service = DatabaseService()
with db_service.get_session() as session:
    similarities = session.query(SubredditSimilarity).filter(
        SubredditSimilarity.username == "target_user"
    ).all()
    
    motivations = session.query(CommentMotivationAnalysis).filter(
        CommentMotivationAnalysis.username == "target_user"
    ).all()
```

## 数据完整性保证

### 1. 事务性保存
- 所有LLM分析数据在同一个数据库事务中保存
- 如果任何部分失败，整个事务回滚
- 确保数据一致性

### 2. 错误处理
- 记录LLM分析过程中的错误信息
- 保存原始LLM响应用于调试
- 支持部分成功的分析结果

### 3. 数据验证
- 验证JSON数据结构的完整性
- 检查必需字段的存在
- 处理数据类型转换错误

## 性能优化

### 1. 批量操作
- 支持批量保存多个用户的分析结果
- 使用数据库批量插入减少I/O操作
- 优化JSON数据的序列化和反序列化

### 2. 索引优化
```sql
-- 为常用查询创建索引
CREATE INDEX idx_subreddit_similarities_session_username 
ON subreddit_similarities(session_id, username);

CREATE INDEX idx_motivation_analyses_session_username 
ON comment_motivation_analyses(session_id, username);

CREATE INDEX idx_motivation_analyses_target_subreddit 
ON comment_motivation_analyses(target_subreddit);
```

### 3. 数据压缩
- 大型JSON字段使用压缩存储
- 定期清理过期的分析数据
- 支持数据归档和恢复

## 监控和统计

### 1. 分析成功率监控
```python
# 获取LLM分析成功率统计
def get_llm_analysis_stats():
    with db_service.get_session() as session:
        total_sessions = session.query(SearchSession).count()
        successful_llm = session.query(SearchSession).filter(
            SearchSession.llm_analysis_success == True
        ).count()
        
        return {
            "total_sessions": total_sessions,
            "llm_success_count": successful_llm,
            "success_rate": successful_llm / total_sessions if total_sessions > 0 else 0
        }
```

### 2. 性能监控
- 记录LLM分析耗时
- 监控数据库查询性能
- 跟踪存储空间使用情况

## 故障排除

### 常见问题

1. **LLM分析数据缺失**
   - 检查`llm_analysis`字段是否存在
   - 验证LLM服务是否正常工作
   - 查看错误日志中的详细信息

2. **数据库保存失败**
   - 检查数据库连接状态
   - 验证JSON数据格式是否正确
   - 确认数据库表结构是否最新

3. **性能问题**
   - 检查数据库索引是否存在
   - 监控JSON字段大小
   - 考虑数据分页和限制

### 调试工具

```python
# 测试LLM分析数据存储
python test_llm_analysis_storage.py

# 检查数据库状态
python scripts/init_database.py --stats

# 验证数据完整性
python scripts/init_database.py --check
```

## 更多信息

- [数据库设置指南](./DATABASE_SETUP.md)
- [API文档](./API.md)
- [项目主README](../README.md)
