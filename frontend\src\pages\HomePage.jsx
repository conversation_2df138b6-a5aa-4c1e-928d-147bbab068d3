// Import React Hooks: useState for state management, useEffect for side effects
import { useState, useEffect } from 'react'
// Import React Router navigation Hook for page navigation
import { useNavigate, useLocation } from 'react-router-dom'
// Import custom components
import SearchInput from '../components/SearchInput'
import PointsBalance from '../components/PointsBalance'
import PayPalPurchaseModal from '../components/PayPalPurchaseModal'
import InsufficientPointsModal from '../components/InsufficientPointsModal'
import TrialLimitModal from '../components/TrialLimitModal'
import FeedbackModal from '../components/FeedbackModal'
// Import Lucide React icon library components
import { History, MessageSquare } from 'lucide-react'
// Import API service module
import { api, getDisplayNameByEmail, getAvatarTextByEmail, authStore, getUserPoints } from '../services/api'
import logger from '../utils/logger'
import { setActiveSearch } from '../services/searchSession'
import { recordPageVisit } from '../services/analytics'

const HomePage = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const SEARCH_COST = 20 // 搜索消耗积分数

  const [apiStatus, setApiStatus] = useState('checking') // 'checking' | 'healthy' | 'error'
  const [auth, setAuth] = useState({ authenticated: false, user: null })
  const [showPurchaseModal, setShowPurchaseModal] = useState(false)
  const [showInsufficientPointsModal, setShowInsufficientPointsModal] = useState(false)
  const [showTrialLimitModal, setShowTrialLimitModal] = useState(false)
  const [isTrialLimitReached, setIsTrialLimitReached] = useState(false)
  const [showFeedbackModal, setShowFeedbackModal] = useState(false)
  const [userPoints, setUserPoints] = useState(null)
  const [isMobile, setIsMobile] = useState(false)
  const [anonLimitReached, setAnonLimitReached] = useState(false)

  // 处理动态视口高度
  useEffect(() => {
    const setVH = () => {
      const vh = window.innerHeight * 0.01
      document.documentElement.style.setProperty('--vh', `${vh}px`)
      setIsMobile(window.innerWidth < 640)
    }
    
    setVH()
    window.addEventListener('resize', setVH)
    window.addEventListener('orientationchange', setVH)
    
    return () => {
      window.removeEventListener('resize', setVH)
      window.removeEventListener('orientationchange', setVH)
    }
  }, [])
  
  
  useEffect(() => {
    // 初始读取持久化的匿名上限标记
    try {
      const v = window.localStorage.getItem('anon_limit_reached')
      if (v === '1' || v === 'true') {
        setAnonLimitReached(true)
      }
    } catch (_) {}

    // 检查是否从 LoadingPage 传回了显示试用限制模态框的状态
    if (location.state?.showTrialLimitModal) {
      setIsTrialLimitReached(true)
      setShowTrialLimitModal(true)
      setAnonLimitReached(true)
      try { window.localStorage.setItem('anon_limit_reached', '1') } catch (_) {}
      // 清除状态，避免重复显示
      navigate(location.pathname, { replace: true, state: {} })
    }
  }, [location.state, navigate])

  useEffect(() => {
    // 并行执行健康检查和认证检查，提高加载速度
    const checkApiHealth = async () => {
      try {
        await api.healthCheck()
        setApiStatus('healthy')
      } catch (error) {
        logger.error('home_api_health_check_failed')
        // 只有在认证也失败的情况下才显示错误
        // 因为如果认证成功，说明API是可用的，只是健康检查可能超时了
        setApiStatus('pending-auth-check')
      }
    }
    
    const checkAuth = async () => {
      try {
        const me = await api.me()
        setAuth(me)
        // Sync to lightweight store for other pages to read immediately
        authStore.authenticated = !!me?.authenticated
        authStore.user = me?.user || null
        // 如果认证成功，说明API是健康的
        if (apiStatus === 'pending-auth-check' || apiStatus === 'checking') {
          setApiStatus('healthy')
        }
        return true
      } catch (_) {
        // 认证失败时也要清空 authStore
        authStore.authenticated = false
        authStore.user = null
        return false
      }
    }

    // 并行执行健康检查、认证检查和访问记录
    Promise.all([checkApiHealth(), checkAuth(), recordPageVisit('/')]).then(([_, authSuccess]) => {
      // 如果认证失败且API状态是pending-auth-check，才设置为错误
      if (!authSuccess && apiStatus === 'pending-auth-check') {
        setApiStatus('error')
      }
    })
  }, [])

  // 不在 HomePage 自动取消，避免跨标签页误取消

  // 处理标签页切换 - 当用户回到页面时重新检查API状态
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && apiStatus === 'error') {
        // 用户回到页面且之前有错误，尝试重新检查
        setApiStatus('checking')
        
        // 使用getUserPoints作为健康检查的代理
        // 因为这个请求实际会被调用，如果成功说明API是健康的
        api.getUserPoints().then(() => {
          setApiStatus('healthy')
        }).catch(() => {
          // 只有在真的失败时才保持错误状态
          setApiStatus('error')
        })
      }
    }
    
    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [apiStatus])

  // 获取用户积分
  useEffect(() => {
    const fetchUserPoints = async () => {
      if (auth.authenticated) {
        try {
          const pointsData = await getUserPoints()
          if (pointsData.success) {
            setUserPoints(pointsData.balance)
          }
        } catch (error) {
          logger.error('home_fetch_user_points_failed')
        }
      }
    }
    fetchUserPoints()
  }, [auth.authenticated])

  const handleSearch = async (query) => {
    if (!query.trim()) {
      return
    }

    // 登录用户检查积分
    if (auth.authenticated && userPoints < SEARCH_COST) {
      setShowInsufficientPointsModal(true)
      return
    }

    // 对于未登录用户，直接尝试搜索（后端会处理限制）
    // 对于登录用户，也直接搜索
    try {
      const clientSessionId = `${Date.now()}_${Math.random().toString(36).slice(2, 8)}`
      setActiveSearch({ query, clientSessionId, status: 'starting', startedAt: Date.now() })
      navigate('/loading', {
        state: {
          query,
          sessionId: clientSessionId
        }
      })
    } catch (error) {
      logger.error('home_search_failed')
      // 如果是限制错误，显示相应的模态框
      if (error.status === 403) {
        const msg = (error.message || '').toLowerCase()
        const hitLimit = msg.includes('limit') || msg.includes('free search limit') || msg.includes('please register to search')
        if (hitLimit) {
          setIsTrialLimitReached(true)
          setAnonLimitReached(true)
          try { window.localStorage.setItem('anon_limit_reached', '1') } catch (_) {}
        }
        setShowTrialLimitModal(true)
      }
    }
  }

  
  
  const goToHistory = () => {
    navigate('/history')
  }

  const StatusDot = ({ status }) => {
    const color =
      status === 'healthy' ? 'bg-green-500' : status === 'error' ? 'bg-red-500' : 'bg-yellow-500'
    const label = status === 'healthy' ? 'Online' : status === 'error' ? 'Offline' : 'Checking'
    return (
      <div className="flex items-center space-x-2 text-xs text-gray-500">
        <span className={`inline-block w-2 h-2 rounded-full ${color}`} aria-hidden="true"></span>
        <span className="hidden sm:inline">{label}</span>
      </div>
    )
  }

  // Unified brand mark (not creating new file, maintaining modular reuse)
  const BrandMark = ({ withText = true, boxClass = 'w-9 h-9', textClass = 'text-sm' }) => (
    <div className="flex items-center space-x-3">
      <div className={`${boxClass} rounded-lg overflow-hidden flex items-center justify-center`}>
        <img src="/favicon.png" alt="CogBridges" className="w-full h-full object-contain" />
      </div>
      {withText && (
        <span className={`text-gray-800 font-semibold ${textClass}`}>CogBridges</span>
      )}
    </div>
  )

  
  
  return (
    <div 
      className="bg-gray-50 flex flex-col overflow-hidden sm:overflow-visible sm:min-h-screen" 
      style={{ 
        height: isMobile ? 'calc(var(--vh, 1vh) * 100)' : 'auto',
        minHeight: !isMobile ? '100vh' : 'auto'
      }}
    >
      {/* Top nav, minimal */}
      <header className="flex-shrink-0 flex justify-between items-center px-4 py-3 sm:py-4 md:px-6 md:py-6">
        <div className="flex items-center space-x-2 sm:space-x-3">
          <BrandMark withText={false} boxClass="w-9 h-9 sm:w-10 sm:h-10 md:w-11 md:h-11" textClass="text-lg sm:text-xl" />
          <span className="hidden sm:inline text-gray-800 font-semibold text-lg sm:text-xl">CogBridges</span>
          <div className="hidden md:block"><StatusDot status={apiStatus} /></div>
        </div>

        <div className="flex items-center gap-2">
          {auth?.authenticated && (
            <button
              onClick={goToHistory}
              className="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-4 py-1.5 sm:py-2 text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
            >
              <History className="w-4 h-4" />
              <span className="hidden sm:inline text-sm">History</span>
            </button>
          )}
          {auth?.authenticated ? (
            <div className="flex items-center gap-2 sm:gap-3">
              <PointsBalance onPurchaseClick={() => setShowPurchaseModal(true)} />
              <div className="hidden sm:flex items-center gap-2">
                <div className="w-9 h-9 rounded-full bg-primary-100 flex items-center justify-center border border-primary-200">
                  <span className="text-primary-700 text-xs font-semibold">{getAvatarTextByEmail(auth?.user?.email)}</span>
                </div>
                <span className="text-sm text-gray-700 hidden lg:inline">{getDisplayNameByEmail(auth?.user?.email)}</span>
              </div>
              <button
                onClick={async () => { await api.logout(); setAuth({ authenticated: false, user: null }) }}
                className="px-2 sm:px-3 py-1.5 sm:py-2 text-sm text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              >
                <span className="hidden sm:inline">Log out</span>
                <span className="sm:hidden">Exit</span>
              </button>
            </div>
          ) : (
            anonLimitReached ? (
              <>
                <button onClick={() => navigate('/login')} className="px-3 sm:px-4 py-1.5 sm:py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors">Log in</button>
                <button onClick={() => navigate('/register')} className="px-3 sm:px-4 py-1.5 sm:py-2 text-sm text-white bg-primary-600 hover:bg-primary-700 rounded-lg transition-colors">Sign up</button>
              </>
            ) : null
          )}
        </div>
      </header>

      {/* API status banner */}
      {apiStatus === 'error' && (
        <div className="flex-shrink-0 mx-4 sm:mx-6 mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-700">
          Cannot reach backend service. Please make sure the server is running.
        </div>
      )}

      {/* Hero + value + search CTA */}
      <main className="flex-1 flex flex-col items-center justify-center px-4 sm:px-6 pb-8 sm:pb-12 md:pb-20 overflow-y-auto sm:overflow-visible">
        {/* Hero */}
        <div className="text-center mb-6 sm:mb-8 md:mb-10 animate-fade-in max-w-5xl">
          <h1 className="text-2xl sm:text-3xl md:text-5xl lg:text-6xl font-semibold text-gray-900 tracking-tight leading-tight">
            Find more trustworthy answers from real Reddit discussions
          </h1>
        </div>

        {/* Primary CTA */}
        <div className="w-full max-w-4xl mb-4 sm:mb-6 md:mb-8 animate-slide-up">
          <SearchInput 
            onSearch={handleSearch} 
            placeholder="" 
            className="w-full" 
          />
        </div>

        {/* Value props - Optimized for mobile */}
        <div className="mt-2 text-center text-xs sm:text-sm text-gray-500">
          <div className="flex flex-col space-y-2 sm:space-y-0 sm:flex-row sm:items-center sm:justify-center sm:gap-3">
            <div className="flex items-center justify-center gap-2">
              <span>🔗</span>
              <span>Real content with citations</span>
            </div>
            <span className="hidden sm:inline text-gray-300">|</span>
            <div className="flex items-center justify-center gap-2">
              <span>🧭</span>
              <span>Commenter background analysis</span>
            </div>
            <span className="hidden sm:inline text-gray-300">|</span>
            <div className="flex items-center justify-center gap-2">
              <span>🎯</span>
              <span>Featured Comment</span>
            </div>
          </div>
        </div>
        
      </main>

      {/* Footer */}
      <footer className="flex-shrink-0 text-center py-4 sm:py-6 border-t border-gray-200 bg-white">
        <div className="flex items-center justify-center gap-4">
          <p className="text-gray-500 text-xs sm:text-sm">© 2025 CogBridges</p>
          <button
            onClick={() => setShowFeedbackModal(true)}
            className="flex items-center gap-1.5 text-xs sm:text-sm text-gray-600 hover:text-primary-600 transition-colors"
          >
            <MessageSquare className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
            <span>Feedback</span>
          </button>
        </div>
      </footer>

      {/* PayPal Purchase Modal */}
      <PayPalPurchaseModal
        isOpen={showPurchaseModal}
        onClose={() => setShowPurchaseModal(false)}
        onSuccess={() => {
          setShowPurchaseModal(false);
          // Refresh points after successful payment
          if (auth?.authenticated) {
            getUserPoints().then(data => {
              if (data?.success) {
                setUserPoints(data.balance);
              }
            }).catch(console.error);
          }
        }}
      />
      
      {/* Insufficient Points Modal */}
      <InsufficientPointsModal 
        isOpen={showInsufficientPointsModal} 
        onClose={() => setShowInsufficientPointsModal(false)} 
        onPurchaseClick={() => {
          setShowInsufficientPointsModal(false)
          setShowPurchaseModal(true)
        }}
        currentPoints={userPoints || 0}
      />
      
      {/* Trial Limit Modal */}
      <TrialLimitModal
        isOpen={showTrialLimitModal}
        onClose={() => {
          setShowTrialLimitModal(false)
          setIsTrialLimitReached(false)
        }}
        isLimitReached={isTrialLimitReached}
      />
      
      {/* Feedback Modal */}
      <FeedbackModal 
        isOpen={showFeedbackModal} 
        onClose={() => setShowFeedbackModal(false)} 
      />
      
    </div>
  )
}

export default HomePage 