import { User, MessageCircle, ExternalLink, Calendar, FileText } from 'lucide-react'
import CommenterCard from './CommenterCard'

const ResultCard = ({ result }) => {
  // Format time
  const formatTime = (timestamp) => {
    if (!timestamp) return 'Unknown time'
    try {
      // If Unix timestamp
      const date = new Date(timestamp * 1000)
      if (isNaN(date.getTime())) {
        // Try direct parse
        const directDate = new Date(timestamp)
        if (isNaN(directDate.getTime())) return 'Unknown time'
        return directDate.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      }
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch (e) {
      return 'Unknown time'
    }
  }

  // Post info
  const post = result._rawData?.post || {}
  const postTitle = post.title || 'Related discussion'
  const postSelftext = post.selftext || ''
  const postCreatedTime = post.created_utc

  return (
    <div className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-3 sm:p-4 md:p-6 border border-gray-200">
      {/* Post section */}
      <div className="mb-3 sm:mb-4 p-2.5 sm:p-3 md:p-4 bg-gray-50 rounded-lg border-l-4 border-primary-400">
        <div className="flex items-start justify-between mb-1.5 sm:mb-2">
          <h3 className="text-sm sm:text-base md:text-lg font-semibold text-gray-800 flex-1 break-words">
            <FileText className="w-4 h-4 sm:w-5 sm:h-5 inline mr-1.5 sm:mr-2 text-primary-600 flex-shrink-0" />
            {postTitle}
          </h3>
        </div>

        {postSelftext && (
          <div className="text-xs sm:text-sm text-gray-600 mb-1.5 sm:mb-2 line-clamp-3 break-words">
            {postSelftext.length > 200 ? `${postSelftext.substring(0, 200)}...` : postSelftext}
          </div>
        )}

        <div className="flex items-center text-xs text-gray-500 space-x-2 sm:space-x-4 flex-wrap gap-y-1">
          <span className="flex items-center">
            <Calendar className="w-3 h-3 mr-1" />
            Posted {formatTime(postCreatedTime)}
          </span>
          <span className="flex items-center">
            <MessageCircle className="w-3 h-3 mr-1" />
            From r/{result.subreddit}
          </span>
        </div>
      </div>

      {/* Comment */}
      <div className="mb-3 sm:mb-4">
        <div className="text-xs sm:text-sm text-gray-500 mb-1.5 sm:mb-2 font-medium">
          💬 Relevant answer:
        </div>
        <div className="text-sm sm:text-base md:text-lg font-medium text-gray-800 mb-1.5 sm:mb-2 leading-relaxed bg-blue-50 p-2.5 sm:p-3 rounded-lg">
          <div className="whitespace-pre-wrap break-words">{result.fullComment || result.comment}</div>
        </div>
        <div className="flex items-center text-xs sm:text-sm text-gray-500 space-x-2 sm:space-x-4 flex-wrap">
          {/* Upvote count removed */}
          <a
            href={`https://www.reddit.com/user/${encodeURIComponent(result.author || '')}`}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center text-blue-600 hover:text-blue-800 hover:underline"
            title={`View u/${result.author} on Reddit`}
          >
            <User className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1" />
            u/{result.author}
          </a>
        </div>
      </div>

      {/* Background analysis card - show if we have credibility data */}
      {result._rawData?.backgroundAnalysis?.credibility && (
        <CommenterCard
          credibilityData={result._rawData.backgroundAnalysis.credibility}
          commentAlignment={result._rawData.backgroundAnalysis.alignment}
          className="mb-3 sm:mb-4"
        />
      )}
      
      {/* Basic commenter info if no credibility analysis but have commenter history */}
      {!result._rawData?.backgroundAnalysis?.credibility && result._rawData?.commenterHistory && (result._rawData.commenterHistory.comment_karma || result._rawData.commenterHistory.link_karma) && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 sm:p-4 mt-3 sm:mt-4 mb-3 sm:mb-4">
          <div className="flex items-center space-x-2">
            <User className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
            <span className="font-medium text-gray-700 text-sm sm:text-base">Commenter Info</span>
          </div>
          <div className="mt-2 text-xs sm:text-sm text-gray-600">
            {result._rawData.commenterHistory.comment_karma && (
              <div>Comment Karma: {result._rawData.commenterHistory.comment_karma.toLocaleString()}</div>
            )}
            {result._rawData.commenterHistory.link_karma && (
              <div>Link Karma: {result._rawData.commenterHistory.link_karma.toLocaleString()}</div>
            )}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-end pt-3 sm:pt-4 border-t border-gray-100">
        <div className="flex items-center space-x-2">
          <a
            href={result.url}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-2.5 sm:px-3 py-1.5 sm:py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg shadow-sm transition-colors focus:outline-none focus:ring-2 focus:ring-primary-400 focus:ring-offset-1 text-xs sm:text-sm"
            aria-label="Open original post in a new tab"
            title="Open original post in a new tab"
          >
            <ExternalLink className="w-3.5 h-3.5 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" />
            <span className="hidden sm:inline">Open original post</span>
            <span className="sm:hidden">View on Reddit</span>
          </a>
        </div>
      </div>
    </div>
  )
}

export default ResultCard 