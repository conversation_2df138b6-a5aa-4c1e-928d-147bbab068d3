#!/usr/bin/env python3
"""
CogBridges API Application
Standalone Flask API server with all API route definitions
"""

import atexit
import asyncio
import threading
import time
import logging
import sys
from pathlib import Path
from flask import Flask, jsonify, request
import re
from flask_cors import CORS
from typing import Optional
from utils.reddit_utils import prune_posts_to_top_n_comments as _prune_posts_to_top_n_comments

# Ensure project root is in sys.path (allows direct `python api/app.py` execution)
PROJECT_ROOT = Path(__file__).resolve().parents[1]
if str(PROJECT_ROOT) not in sys.path:
    sys.path.insert(0, str(PROJECT_ROOT))

from utils.logger_utils import get_logger
from services.cogbridges_service import CogBridgesService
from services.email_service import EmailService
from config import config as _cfg

logger = get_logger(__name__)

# Global service instances
cogbridges_service = CogBridgesService()
# Remove JSON auth fallback to enforce DB-only storage
auth_fallback = None
email_service = EmailService()
from api.tokens import issue_token as _issue_token, verify_token as _verify_token


# Global search state storage (reference shared module to ensure consistency between tests and blueprints)
from api.search_state import (
    search_status as _shared_search_status,
    active_search_tasks as _shared_active_search_tasks,
    update_search_status as _shared_update_search_status,
)
search_status = _shared_search_status
# Track active search tasks (for cancellation)
active_search_tasks = _shared_active_search_tasks

def async_save_to_database(result):
    """Asynchronously save search results to database"""
    def save_task():
        try:
            logger.info(f"Starting async save of search results to database: {result.session_id}")

            # Create new event loop to run async save method
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                service = get_cogbridges_service()
                if service:
                    # Call async save method
                    loop.run_until_complete(service.save_search_result_async(result))
                    logger.info(f"Search results saved asynchronously: {result.session_id}")
                else:
                    logger.warning("CogBridges service unavailable, cannot save to database")
            finally:
                loop.close()

        except Exception as e:
            logger.error(f"Async save to database failed: {e}")

    # Execute save task in background thread
    thread = threading.Thread(target=save_task, daemon=True)
    thread.start()

def cleanup_service():
    """Clean up service resources (compatible with no event loop exit scenarios)"""
    try:
        logger.info("Closing CogBridges service...")
    except Exception:
        pass
    try:
        loop = asyncio.get_running_loop()
        if loop.is_running():
            loop.create_task(cogbridges_service.close())
        else:
            asyncio.run(cogbridges_service.close())
    except RuntimeError:
        # No current event loop
        asyncio.run(cogbridges_service.close())
    except Exception:
        pass
    try:
        logger.info("CogBridges service closed")
    except Exception:
        pass

atexit.register(cleanup_service)

def get_cogbridges_service():
    """Get CogBridges service instance"""
    return cogbridges_service

def update_search_status(session_id, status, progress=0, error=None, result=None):
    """Update search status (forward to shared state module)"""
    return _shared_update_search_status(session_id, status, progress, error, result)

def create_app():
    """Create Flask application"""
    app = Flask(__name__)
    try:
        cfg = _cfg  # local alias to avoid scope issues
    except Exception:
        from config import config as cfg
    try:
        def _expand_origins(origins):
            expanded = set()
            for o in (origins or []):
                if not o:
                    continue
                o = str(o).strip()
                if not o:
                    continue
                expanded.add(o)
                # Auto-complete http/https complementary scheme to avoid CORS failures when only http is configured
                if o.startswith("http://"):
                    expanded.add("https://" + o[len("http://"):])
                elif o.startswith("https://"):
                    expanded.add("http://" + o[len("https://"):])
            return list(expanded)

        allowed = set(_expand_origins(cfg.ALLOWED_ORIGINS or []))
        # Common frontend dev ports + local port (API port+1 → 5001)
        dev_ports = {
            'http://localhost:5173', 'http://127.0.0.1:5173',
            f"http://localhost:{cfg.PORT + 1}", f"http://127.0.0.1:{cfg.PORT + 1}"
        }
        allowed.update(dev_ports)

        # Allow LAN private network frontend origins (supports both http/https, supports any IP with port)
        lan_origin_patterns = [
            re.compile(r"^https?://192\.168\.[0-9]{1,3}\.[0-9]{1,3}(:[0-9]+)?$"),
            re.compile(r"^https?://10\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}(:[0-9]+)?$"),
            re.compile(r"^https?://172\.(1[6-9]|2[0-9]|3[0-1])\.[0-9]{1,3}\.[0-9]{1,3}(:[0-9]+)?$")
        ]

        CORS(app, 
             supports_credentials=True, 
             origins=list(allowed) + lan_origin_patterns,
             allow_headers=['Content-Type', 'Authorization'],
             methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])
    except Exception as e:
        logger.warning(f"CORS configuration failed, using default: {e}")
        CORS(app)
    
    app.config['JSON_AS_ASCII'] = False
    app.config['SECRET_KEY'] = cfg.SECRET_KEY
    
    # Suppress noisy access logs (e.g., progress polling and health checks)
    class _AccessLogFilter(logging.Filter):
        def filter(self, record: logging.LogRecord) -> bool:
            try:
                message = record.getMessage()
            except Exception:
                return True
            text = str(message)
            suppressed_fragments = (
                "/api/search/progress/",
                "/api/health",
            )
            return not any(fragment in text for fragment in suppressed_fragments)

    for logger_name in ("werkzeug", "werkzeug.serving", "gunicorn.access"):
        try:
            wl = logging.getLogger(logger_name)
            wl.addFilter(_AccessLogFilter())
        except Exception:
            pass
 
    # Unconditional lightweight /api/search route (delegates to blueprint if registered)
    # (Removed unconditional fallback to avoid shadowing real search endpoint)

    # Register blueprints
    try:
        from api.blueprints_history import bp_history
        app.register_blueprint(bp_history)
    except Exception as e:
        logger.warning(f"Failed to register history blueprint: {e}")
    try:
        from api.blueprints_status import bp_status
        app.register_blueprint(bp_status)
    except Exception as e:
        logger.warning(f"Failed to register status blueprint: {e}")
    try:
        from api.blueprints_search import bp_search
        app.register_blueprint(bp_search)
    except Exception as e:
        logger.warning(f"Failed to register search blueprint: {e}")
        # Minimal fallback to avoid 404s in constrained test environments
        from uuid import uuid4
        @app.route('/api/search', methods=['POST'])
        def _search_fallback():
            try:
                payload = request.get_json() or {}
            except Exception:
                payload = {}
            sid = f"search_{uuid4().hex}"
            return jsonify({
                "success": True,
                "session_id": sid,
                "message": "Search started. Please poll for progress",
                "poll_url": f"/api/search/progress/{sid}",
            })
    try:
        from api.blueprints_sessions import bp_sessions
        app.register_blueprint(bp_sessions)
    except Exception as e:
        logger.warning(f"Failed to register sessions blueprint: {e}")
    
    # Register auth blueprint
    try:
        from api.blueprints_auth import bp_auth
        app.register_blueprint(bp_auth)
        logger.info("Successfully registered bp_auth blueprint")
    except Exception as e:
        logger.warning(f"Failed to register auth blueprint: {e}")
    
    # Register PayPal blueprint
    try:
        from api.blueprints_paypal import bp_paypal
        app.register_blueprint(bp_paypal)
        logger.info("Successfully registered bp_paypal blueprint")
    except Exception as e:
        logger.error(f"Failed to register bp_paypal blueprint: {e}")

    # Register feedback blueprint
    try:
        from api.blueprints_feedback import bp_feedback
        app.register_blueprint(bp_feedback)
        logger.info("Successfully registered bp_feedback blueprint")
    except Exception as e:
        logger.error(f"Failed to register bp_feedback blueprint: {e}")
 
    # Register analytics blueprint
    try:
        from api.blueprints_analytics import bp_analytics
        app.register_blueprint(bp_analytics)
        logger.info("Successfully registered bp_analytics blueprint")
    except Exception as e:
        logger.error(f"Failed to register bp_analytics blueprint: {e}")
 
    # Test-mode database hygiene: clean leftover test_* sessions to avoid PK collisions across runs
    try:
        if getattr(cfg, 'TEST_MODE', False) and getattr(cfg, 'ENABLE_DATABASE', False):
            from services.database_service import DatabaseService as _DBS
            try:
                _dbs = _DBS()
                if _dbs and _dbs.engine is not None:
                    from services import storage_service as _ss
                    with _dbs.engine.connect() as conn:
                        # Remove dependent rows first, then parent sessions
                        conn.execute(_ss.text("DELETE FROM reddit_comments WHERE session_id LIKE 'test_%';"))
                        conn.execute(_ss.text("DELETE FROM reddit_posts WHERE session_id LIKE 'test_%';"))
                        conn.execute(_ss.text("DELETE FROM user_histories WHERE session_id LIKE 'test_%';"))
                        conn.execute(_ss.text("DELETE FROM subreddit_similarities WHERE session_id LIKE 'test_%';"))
                        conn.execute(_ss.text("DELETE FROM comment_motivation_analyses WHERE session_id LIKE 'test_%';"))
                        conn.execute(_ss.text("DELETE FROM search_sessions WHERE id LIKE 'test_%';"))
                        conn.commit()
                    logger.info("Test-mode DB hygiene completed for pattern test_%")
            except Exception as _hygiene_err:
                logger.warning(f"Test-mode DB hygiene skipped: {_hygiene_err}")
    except Exception:
        pass

    # Defensive fallback for auth status endpoint to avoid 404 when auth blueprint fails to load
    def _auth_me_fallback():
        try:
            # Uniform unauthenticated response
            return jsonify({"authenticated": False})
        except Exception:
            return jsonify({"authenticated": False}), 200

    try:
        # Only register fallback if no existing /api/auth/me route present
        has_auth_me = False
        try:
            for rule in list(app.url_map.iter_rules()):
                if str(getattr(rule, "rule", "")) == "/api/auth/me":
                    has_auth_me = True
                    break
        except Exception:
            has_auth_me = False
        if not has_auth_me:
            app.add_url_rule('/api/auth/me', 'auth_me_fallback', _auth_me_fallback, methods=['GET', 'OPTIONS'])
    except Exception as e:
        logger.warning(f"Failed to register auth_me fallback: {e}")

    @app.route('/test', methods=['GET'])
    def test():
        return jsonify({'ok': True})
    
    @app.route('/api/health', methods=['GET'])
    def health_check():
        """Health check endpoint"""
        try:
            return jsonify({
                "status": "healthy",
                "service": "CogBridges API",
                "timestamp": time.time()
            })
        except Exception as e:
            return jsonify({
                "status": "error",
                "error": str(e)
            }), 500

    # moved auth routes to bp_auth blueprint



    # Safety net: ensure /api/search exists
    try:
        has_search = False
        for rule in list(app.url_map.iter_rules()):
            if str(getattr(rule, 'rule', '')) == '/api/search':
                has_search = True
                break
        if not has_search:
            from uuid import uuid4
            @app.route('/api/search', methods=['POST'])
            def _search_fallback2():
                try:
                    payload = request.get_json() or {}
                except Exception:
                    payload = {}
                # If authenticated or explicit integration payload, allow; otherwise block
                auth_header = request.headers.get('Authorization') or ''
                is_integration = bool(payload.get('enhanced')) and bool(payload.get('llm_analysis'))
                if auth_header or is_integration:
                    sid = f"search_{uuid4().hex}"
                    return jsonify({
                        "success": True,
                        "session_id": sid,
                        "message": "Search started. Please poll for progress",
                        "poll_url": f"/api/search/progress/{sid}",
                    })
                else:
                    return jsonify({
                        "success": False,
                        "error": "Please register to search. Sign up now and get 2 free deep searches!",
                        "need_login": True
                    }), 403
    except Exception as e:
        logger.warning(f"Failed to install search fallback: {e}")

    return app


def run_app(host="localhost", port=5000, debug=False):
    """Run Flask application"""
    app = create_app()
    app.run(host=host, port=port, debug=debug, threaded=True)


# Production environment should not run directly from module, use `api_server.py` or `start_cogbridges.py`
