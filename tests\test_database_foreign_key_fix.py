"""
CogBridges Search - 数据库外键约束修复测试
专门测试修复后的数据库写入顺序，确保外键约束不再违反
"""

import pytest
import os
import tempfile
import sqlite3
from datetime import datetime
from unittest.mock import patch

# 设置测试环境变量
os.environ["ENABLE_DATABASE"] = "True"
os.environ["TEST_MODE"] = "True"

from services.database_service import DatabaseService
from models.search_models import SearchQuery, SearchResult
from models.database_models import Base
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker


@pytest.fixture
def real_database_service():
    """创建真实的SQLite数据库服务用于集成测试"""
    # 创建临时SQLite数据库
    temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_db.close()
    
    database_url = f"sqlite:///{temp_db.name}"
    
    # 创建真实的数据库引擎
    engine = create_engine(database_url, echo=False)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    # 创建数据库服务实例
    service = DatabaseService()
    service.engine = engine
    service.SessionLocal = SessionLocal
    
    # 创建表
    Base.metadata.create_all(bind=engine)
    
    yield service
    
    # 清理
    service.cleanup()
    os.unlink(temp_db.name)


@pytest.fixture
def sample_search_result():
    """创建示例搜索结果"""
    query = SearchQuery(
        query="碰到了难以修复的bug怎么办",
        timestamp=datetime.now(),
        search_type="reddit",
        max_results=5
    )
    
    result = SearchResult(
        query=query,
        success=True
    )
    
    # 添加时间度量
    result.total_time = 59.92
    result.google_search_time = 17.33
    result.reddit_posts_time = 1.80
    result.commenters_history_time = 19.77
    result.llm_analysis_time = 18.88
    
    return result


@pytest.fixture
def sample_reddit_data():
    """创建示例Reddit数据，模拟错误日志中的数据"""
    return {
        "posts": [
            {
                "id": "11045gh",
                "title": "what do you do when you've cant solve a bug?",
                "selftext": "[deleted]",
                "author": "[deleted]",
                "score": 266,
                "num_comments": 163,
                "created_utc": 1676166832.0,
                "subreddit": "learnprogramming",
                "permalink": "/r/learnprogramming/comments/11045gh",
                "url": "https://reddit.com/r/learnprogramming/comments/11045gh"
            },
            {
                "id": "qbactl",
                "title": "how are bugs fixed?",
                "selftext": "sorry, im a newbie in computer programming, and have always wonder how are bugs fixed",
                "author": "kemkomkinomi",
                "score": 24,
                "num_comments": 23,
                "created_utc": 1634646082.0,
                "subreddit": "learnprogramming",
                "permalink": "/r/learnprogramming/comments/qbactl",
                "url": "https://reddit.com/r/learnprogramming/comments/qbactl"
            },
            {
                "id": "13j7m4y",
                "title": "What is the hardest bug you've had to find and fix?",
                "selftext": "Over the years, this has been my most effective technical interview question.",
                "author": "Fair-Item-561",
                "score": 0,
                "num_comments": 3,
                "created_utc": 1684249176.0,
                "subreddit": "learnprogramming",
                "permalink": "/r/learnprogramming/comments/13j7m4y",
                "url": "https://reddit.com/r/learnprogramming/comments/13j7m4y"
            }
        ],
        "comments": [
            {
                "id": "jkdpnhv",
                "post_id": "13j7m4y",
                "body": "Fun story: not the hardest per se, but definitely one of the oddest and most frustrating. A junior coworker of mine was having some trouble debugging an issue.",
                "author": "insertAlias",
                "score": 8,
                "created_utc": 1684249500.0,
                "subreddit": "learnprogramming",
                "permalink": "/r/learnprogramming/comments/13j7m4y/jkdpnhv/",
                "relevance_score": 0.9
            },
            {
                "id": "jke1234",
                "post_id": "11045gh",
                "body": "Take a break, come back with fresh eyes, or ask for help from colleagues.",
                "author": "HappyRogue121",
                "score": 15,
                "created_utc": 1676167000.0,
                "subreddit": "learnprogramming",
                "permalink": "/r/learnprogramming/comments/11045gh/jke1234/",
                "relevance_score": 0.8
            }
        ]
    }


class TestDatabaseForeignKeyFix:
    """测试数据库外键约束修复"""
    
    def test_real_database_foreign_key_constraint_fix(self, real_database_service, sample_search_result, sample_reddit_data):
        """使用真实数据库测试外键约束修复"""
        session_id = "search_1756040647098_iywhd6"  # 使用错误日志中的session_id
        
        # 执行保存操作
        result = real_database_service.save_search_session(
            session_id=session_id,
            search_result=sample_search_result,
            reddit_data=sample_reddit_data,
            owner_user_id=3
        )
        
        # 验证保存成功
        assert result is True
        
        # 验证数据确实被保存到数据库
        loaded_session = real_database_service.load_search_session(session_id)
        assert loaded_session is not None
        assert loaded_session["id"] == session_id
        assert loaded_session["query"] == "碰到了难以修复的bug怎么办"
        assert loaded_session["success"] is True
        assert loaded_session["owner_user_id"] == 3
        
        # 验证Reddit帖子被保存
        assert len(loaded_session["reddit_posts"]) == 3
        post_ids = {post["id"] for post in loaded_session["reddit_posts"]}
        assert "11045gh" in post_ids
        assert "qbactl" in post_ids
        assert "13j7m4y" in post_ids
        
        # 验证Reddit评论被保存
        assert len(loaded_session["reddit_comments"]) == 2
        comment_ids = {comment["id"] for comment in loaded_session["reddit_comments"]}
        assert "jkdpnhv" in comment_ids
        assert "jke1234" in comment_ids
        
        # 验证时间度量被正确保存
        assert loaded_session["total_time_ms"] == 59920  # 59.92 * 1000
        assert loaded_session["grok_time_ms"] == 17330   # 17.33 * 1000
        assert loaded_session["reddit_time_ms"] == 1800  # 1.80 * 1000
        assert loaded_session["history_time_ms"] == 19770 # 19.77 * 1000
        assert loaded_session["llm_time_ms"] == 18880    # 18.88 * 1000
    
    def test_foreign_key_constraint_with_empty_posts(self, real_database_service, sample_search_result):
        """测试只有评论没有帖子时的外键约束处理"""
        session_id = "test_empty_posts_session"
        
        # 创建只有评论没有帖子的数据
        reddit_data = {
            "posts": [],  # 空的帖子列表
            "comments": [
                {
                    "id": "orphan_comment_1",
                    "post_id": "missing_post_123",
                    "body": "This comment references a missing post",
                    "author": "test_user",
                    "score": 10,
                    "created_utc": 1640995300.0,
                    "subreddit": "test",
                    "permalink": "/r/test/comments/missing_post_123/orphan_comment_1/",
                    "relevance_score": 0.7
                }
            ]
        }
        
        # 执行保存操作
        result = real_database_service.save_search_session(
            session_id=session_id,
            search_result=sample_search_result,
            reddit_data=reddit_data
        )
        
        # 验证保存成功
        assert result is True
        
        # 验证数据被正确保存
        loaded_session = real_database_service.load_search_session(session_id)
        assert loaded_session is not None
        
        # 验证自动创建了最小帖子记录
        assert len(loaded_session["reddit_posts"]) == 1
        minimal_post = loaded_session["reddit_posts"][0]
        assert minimal_post["id"] == "missing_post_123"
        assert minimal_post["title"] == "[Post data not available]"
        assert minimal_post["author"] == "[deleted]"
        
        # 验证评论被正确保存
        assert len(loaded_session["reddit_comments"]) == 1
        comment = loaded_session["reddit_comments"][0]
        assert comment["id"] == "orphan_comment_1"
        assert comment["post_id"] == "missing_post_123"
    
    def test_multiple_sessions_no_interference(self, real_database_service, sample_search_result, sample_reddit_data):
        """测试多个会话保存时不会相互干扰"""
        session_id_1 = "test_session_1"
        session_id_2 = "test_session_2"
        
        # 保存第一个会话
        result1 = real_database_service.save_search_session(
            session_id=session_id_1,
            search_result=sample_search_result,
            reddit_data=sample_reddit_data,
            owner_user_id=1
        )
        assert result1 is True
        
        # 保存第二个会话
        result2 = real_database_service.save_search_session(
            session_id=session_id_2,
            search_result=sample_search_result,
            reddit_data=sample_reddit_data,
            owner_user_id=2
        )
        assert result2 is True
        
        # 验证两个会话都能正确加载
        loaded_session_1 = real_database_service.load_search_session(session_id_1)
        loaded_session_2 = real_database_service.load_search_session(session_id_2)
        
        assert loaded_session_1["id"] == session_id_1
        assert loaded_session_1["owner_user_id"] == 1
        assert loaded_session_2["id"] == session_id_2
        assert loaded_session_2["owner_user_id"] == 2
        
        # 验证数据没有混淆
        assert len(loaded_session_1["reddit_posts"]) == 3
        assert len(loaded_session_2["reddit_posts"]) == 3


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
