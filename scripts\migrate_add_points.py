#!/usr/bin/env python3
"""
数据库迁移脚本：添加点数系统相关字段
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 sys.path
PROJECT_ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(PROJECT_ROOT))

from sqlalchemy import create_engine, text
from config import config as cfg
from utils.logger_utils import get_logger

logger = get_logger(__name__)


def run_migration():
    """执行数据库迁移"""
    if not cfg.database_configured:
        logger.error("数据库未配置")
        return False
    
    try:
        # 创建数据库引擎
        engine = create_engine(cfg.database_url)
        
        with engine.connect() as conn:
            # 开始事务
            trans = conn.begin()
            
            try:
                # 1. 检查并添加 points_balance 字段
                result = conn.execute(text("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name='user_accounts' AND column_name='points_balance'
                """))
                
                if not result.fetchone():
                    logger.info("添加 points_balance 字段...")
                    conn.execute(text("""
                        ALTER TABLE user_accounts 
                        ADD COLUMN points_balance INTEGER NOT NULL DEFAULT 0
                    """))
                    logger.info("✓ points_balance 字段添加成功")
                
                # 2. 检查并添加 trial_flags 字段
                result = conn.execute(text("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name='user_accounts' AND column_name='trial_flags'
                """))
                
                if not result.fetchone():
                    logger.info("添加 trial_flags 字段...")
                    conn.execute(text("""
                        ALTER TABLE user_accounts 
                        ADD COLUMN trial_flags JSONB NOT NULL DEFAULT '{}'::jsonb
                    """))
                    logger.info("✓ trial_flags 字段添加成功")
                
                # 3. 创建 points_ledger 表
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = 'points_ledger'
                    )
                """))
                
                if not result.scalar():
                    logger.info("创建 points_ledger 表...")
                    conn.execute(text("""
                        CREATE TABLE points_ledger (
                            id SERIAL PRIMARY KEY,
                            user_id INTEGER NOT NULL REFERENCES user_accounts(id),
                            delta INTEGER NOT NULL,
                            reason VARCHAR(50) NOT NULL,
                            ref VARCHAR(255),
                            metadata JSONB,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        );
                        
                        CREATE INDEX idx_points_ledger_user_id ON points_ledger(user_id);
                        CREATE INDEX idx_points_ledger_ref ON points_ledger(ref);
                    """))
                    logger.info("✓ points_ledger 表创建成功")
                
                # 4. 为现有用户赠送初始点数
                logger.info("为现有用户赠送初始点数...")
                conn.execute(text("""
                    WITH existing_users AS (
                        SELECT id, email_verified
                        FROM user_accounts
                        WHERE points_balance = 0
                    )
                    UPDATE user_accounts
                    SET points_balance = 40,  -- 所有用户都赠送40点（注册必须验证邮箱）
                    trial_flags = '{"granted20": true, "granted40": true}'::jsonb
                    WHERE id IN (SELECT id FROM existing_users)
                """))
                
                # 记录赠送到账本
                conn.execute(text("""
                    INSERT INTO points_ledger (user_id, delta, reason, ref, metadata)
                    SELECT 
                        id,
                        points_balance,
                        'trial',
                        'migration_grant',
                        '{"type": "migration", "note": "Initial grant during migration"}'::jsonb
                    FROM user_accounts
                    WHERE points_balance > 0
                    AND NOT EXISTS (
                        SELECT 1 FROM points_ledger 
                        WHERE user_id = user_accounts.id 
                        AND ref = 'migration_grant'
                    )
                """))
                
                # 提交事务
                trans.commit()
                logger.info("✅ 数据库迁移成功完成！")
                return True
                
            except Exception as e:
                trans.rollback()
                logger.error(f"迁移失败，事务已回滚: {str(e)}")
                return False
                
    except Exception as e:
        logger.error(f"连接数据库失败: {str(e)}")
        return False


if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)