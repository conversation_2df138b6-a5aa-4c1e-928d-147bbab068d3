<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>访问记录测试</title>
</head>
<body>
    <h1>访问记录测试页面</h1>
    <p>这个页面用于测试访问记录功能</p>
    
    <button onclick="testVisitRecording()">测试访问记录</button>
    <button onclick="testVisitRecordingWithUTM()">测试带UTM参数的访问记录</button>
    
    <div id="result"></div>

    <script>
        // 模拟匿名cookie
        document.cookie = 'cogbridges_anon_id=test_cookie_123; path=/';
        
        async function testVisitRecording() {
            try {
                const response = await fetch('/api/analytics/visit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        anonymous_cookie_id: 'test_cookie_123',
                        landing_path: '/test',
                        referrer: 'https://google.com',
                    }),
                });
                
                const result = await response.json();
                document.getElementById('result').innerHTML = `
                    <h3>访问记录结果:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3>错误:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }
        
        async function testVisitRecordingWithUTM() {
            try {
                const response = await fetch('/api/analytics/visit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        anonymous_cookie_id: 'test_cookie_123',
                        landing_path: '/test',
                        referrer: 'https://google.com',
                        utm_source: 'google',
                        utm_medium: 'cpc',
                        utm_campaign: 'test_campaign',
                    }),
                });
                
                const result = await response.json();
                document.getElementById('result').innerHTML = `
                    <h3>带UTM参数的访问记录结果:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3>错误:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        }
        
        // 页面加载时自动记录访问
        window.addEventListener('load', () => {
            testVisitRecording();
        });
    </script>
</body>
</html>
