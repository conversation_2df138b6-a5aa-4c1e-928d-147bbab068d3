import React, { useState, useEffect } from 'react';
import { getUserPoints } from '../services/api';

const PointsBalance = ({ onPurchaseClick }) => {
  const [points, setPoints] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchPoints = async () => {
    try {
      setLoading(true);
      const data = await getUserPoints();
      if (data.success) {
        setPoints(data);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPoints();
  }, []);

  if (loading) {
    return (
      <div className="inline-flex items-center text-sm text-gray-500">
        <span className="animate-pulse">Loading...</span>
      </div>
    );
  }

  if (error) {
    return null;
  }

  if (!points) {
    return null;
  }

  return (
    <div className="inline-flex items-center gap-1 sm:gap-2">
      <div className="flex items-center gap-1 bg-gray-100 px-2 sm:px-3 py-1 rounded-full">
        <svg className="w-3.5 h-3.5 sm:w-4 sm:h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
        <span className="text-xs sm:text-sm font-medium">{points.balance}<span className="hidden sm:inline"> points</span></span>
      </div>
      
      <button
        onClick={onPurchaseClick}
        className="text-xs sm:text-sm text-blue-600 hover:text-blue-700 font-medium"
      >
        Top up
      </button>
    </div>
  );
};

export default PointsBalance;