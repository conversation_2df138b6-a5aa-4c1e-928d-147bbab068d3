import pytest

from scripts.init_database import validate_schema, build_python_migration_script
from models.database_models import Base


class DummyEngine:
    # 使用 PostgreSQL 方言以生成接近生产的 DDL
    from sqlalchemy.dialects import postgresql
    dialect = postgresql.dialect()


class DummyDBService:
    def __init__(self):
        self.engine = DummyEngine()


def test_validate_schema_detects_missing(monkeypatch):
    """应能检测到缺失表并生成建议 SQL。"""

    class StubInspector:
        def get_table_names(self):
            # 模拟数据库里没有任何表
            return []

        def get_columns(self, table_name):
            return []

    import scripts.init_database as init_mod
    monkeypatch.setattr(init_mod, "inspect", lambda engine: StubInspector())

    svc = DummyDBService()
    result = validate_schema(svc)

    assert result.get("has_drift") is True
    assert "search_sessions" in set(result.get("missing_tables", []))
    suggested = "\n".join(result.get("suggested_sql", []))
    assert "CREATE TABLE IF NOT EXISTS search_sessions" in suggested


def test_validate_schema_no_drift(monkeypatch):
    """当表和列齐全时，应报告无漂移。"""

    class StubInspector:
        def get_table_names(self):
            return list(Base.metadata.tables.keys())

        def get_columns(self, table_name):
            table = Base.metadata.tables[table_name]
            return [{"name": c.name} for c in table.columns]

    import scripts.init_database as init_mod
    monkeypatch.setattr(init_mod, "inspect", lambda engine: StubInspector())

    svc = DummyDBService()
    result = validate_schema(svc)

    assert result.get("has_drift") is False
    assert result.get("missing_tables") == []
    assert result.get("missing_columns") == {}


def test_build_python_migration_script():
    stmts = [
        "CREATE TABLE IF NOT EXISTS foo (id INTEGER);",
        "ALTER TABLE foo ADD COLUMN IF NOT EXISTS bar TEXT;",
    ]
    script = build_python_migration_script(stmts)
    assert "psycopg2" in script
    assert "statements = [" in script
    assert "CREATE TABLE IF NOT EXISTS foo" in script


