#!/usr/bin/env python3
"""
测试匿名用户搜索会话跟踪功能
确保匿名用户的搜索会话被正确保存到数据库并可以进行分析
"""

import os
import pytest
import asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from datetime import datetime

# Configure test environment
os.environ.setdefault("ENABLE_DATABASE", "True")
os.environ.setdefault("TEST_MODE", "True")

from api.app import create_app
from services.cogbridges_service import CogBridgesService
from services.database_service import DatabaseService
from models.database_models import SearchSession


class TestAnonymousUserTracking:
    """匿名用户跟踪测试"""

    @pytest.fixture
    def app_client(self):
        """创建测试应用客户端"""
        app = create_app()
        app.config.update(TESTING=True)
        with app.test_client() as client:
            yield client

    @pytest.fixture
    def db_service(self):
        """创建数据库服务实例"""
        try:
            service = DatabaseService()
            if service.engine:
                yield service
            else:
                pytest.skip("Database service not available")
        finally:
            if 'service' in locals():
                service.cleanup()

    def test_anonymous_search_session_saved(self, app_client, db_service):
        """测试匿名用户搜索会话被保存到数据库"""
        # 模拟匿名用户搜索请求
        response = app_client.post('/api/search', json={
            'query': 'test anonymous search',
            'enhanced': True,
            'llm_analysis': True
        })
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        assert 'session_id' in data
        
        session_id = data['session_id']
        
        # 等待搜索完成
        import time
        max_wait = 30
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            progress_response = app_client.get(f'/api/search/progress/{session_id}')
            if progress_response.status_code == 200:
                progress_data = progress_response.get_json()
                if progress_data['status'] in ['completed', 'error', 'cancelled']:
                    break
            time.sleep(1)
        
        # 检查数据库中的会话记录
        with db_service.get_session() as session:
            db_session = session.query(SearchSession).filter(
                SearchSession.id == session_id
            ).first()
            
            if db_session:
                # 验证匿名用户字段
                assert db_session.is_anonymous is True
                assert db_session.anonymous_cookie_id is not None
                assert db_session.owner_user_id is None
                assert db_session.query == 'test anonymous search'
                
                print(f"✅ 匿名用户会话已保存到数据库")
                print(f"   Session ID: {db_session.id}")
                print(f"   Anonymous Cookie ID: {db_session.anonymous_cookie_id}")
                print(f"   Is Anonymous: {db_session.is_anonymous}")
                print(f"   Query: {db_session.query}")
            else:
                pytest.fail("Anonymous search session not found in database")

    def test_anonymous_analytics_endpoint(self, app_client, db_service):
        """测试匿名用户分析端点"""
        # 首先创建一些测试数据
        with db_service.get_session() as session:
            # 创建测试匿名会话
            test_session = SearchSession(
                id="test_anon_session_1",
                query="test query 1",
                is_anonymous=True,
                anonymous_cookie_id="test_cookie_1",
                success=True,
                total_time_ms=1000,
                reddit_posts_count=5,
                reddit_comments_count=20
            )
            session.add(test_session)
            
            test_session2 = SearchSession(
                id="test_anon_session_2",
                query="test query 2",
                is_anonymous=True,
                anonymous_cookie_id="test_cookie_2",
                success=False,
                error_message="Test error",
                total_time_ms=500,
                reddit_posts_count=0,
                reddit_comments_count=0
            )
            session.add(test_session2)
            
            session.commit()
        
        # 测试匿名用户分析端点
        response = app_client.get('/api/analytics/anonymous?days=30&limit=10')
        
        assert response.status_code == 200
        data = response.get_json()
        
        assert data['success'] is True
        assert 'summary' in data
        assert 'daily_stats' in data
        assert 'popular_queries' in data
        assert 'recent_sessions' in data
        
        # 验证统计数据
        summary = data['summary']
        assert summary['total_searches'] >= 2  # 至少包含我们创建的测试数据
        assert summary['successful_searches'] >= 1
        assert summary['failed_searches'] >= 1
        
        # 验证最近会话
        recent_sessions = data['recent_sessions']
        assert len(recent_sessions) >= 2
        
        # 查找我们的测试会话
        test_session_found = False
        for session_data in recent_sessions:
            if session_data['session_id'] == "test_anon_session_1":
                test_session_found = True
                assert session_data['query'] == "test query 1"
                assert session_data['success'] is True
                assert session_data['anonymous_cookie_id'] == "test_cookie_1"
                break
        
        assert test_session_found, "Test anonymous session not found in analytics"
        
        print(f"✅ 匿名用户分析端点工作正常")
        print(f"   总搜索数: {summary['total_searches']}")
        print(f"   成功搜索: {summary['successful_searches']}")
        print(f"   失败搜索: {summary['failed_searches']}")

    def test_conversion_analytics_endpoint(self, app_client, db_service):
        """测试转化分析端点"""
        response = app_client.get('/api/analytics/conversion?days=30')
        
        assert response.status_code == 200
        data = response.get_json()
        
        assert data['success'] is True
        assert 'anonymous_usage' in data
        assert 'search_count_distribution' in data
        assert 'conversion_metrics' in data
        assert 'recommendations' in data
        
        print(f"✅ 转化分析端点工作正常")
        print(f"   匿名用户使用统计: {data['anonymous_usage']}")

    def test_retention_analytics_endpoint(self, app_client, db_service):
        """测试留存分析端点"""
        response = app_client.get('/api/analytics/retention?days=30')
        
        assert response.status_code == 200
        data = response.get_json()
        
        assert data['success'] is True
        assert 'anonymous_users' in data
        assert 'registered_users' in data
        assert 'insights' in data
        
        print(f"✅ 留存分析端点工作正常")
        print(f"   匿名用户统计: {data['anonymous_users']}")
        print(f"   注册用户统计: {data['registered_users']}")

    def test_anonymous_user_limit_tracking(self, app_client, db_service):
        """测试匿名用户限制跟踪"""
        # 测试匿名用户搜索限制
        response1 = app_client.post('/api/search', json={
            'query': 'first anonymous search',
            'enhanced': True,
            'llm_analysis': True
        })
        
        assert response1.status_code == 200
        data1 = response1.get_json()
        session_id1 = data1['session_id']
        
        # 检查cookie是否被设置
        assert 'Set-Cookie' in response1.headers
        
        # 等待第一个搜索完成
        import time
        max_wait = 30
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            progress_response = app_client.get(f'/api/search/progress/{session_id1}')
            if progress_response.status_code == 200:
                progress_data = progress_response.get_json()
                if progress_data['status'] in ['completed', 'error', 'cancelled']:
                    break
            time.sleep(1)
        
        # 验证数据库中的记录
        with db_service.get_session() as session:
            db_session = session.query(SearchSession).filter(
                SearchSession.id == session_id1
            ).first()
            
            if db_session:
                assert db_session.is_anonymous is True
                assert db_session.anonymous_cookie_id is not None
                print(f"✅ 第一个匿名搜索已记录: {db_session.anonymous_cookie_id}")
            else:
                pytest.fail("First anonymous search not recorded")

    def test_database_schema_anonymous_fields(self, db_service):
        """测试数据库模式中的匿名用户字段"""
        with db_service.get_session() as session:
            # 检查表结构
            from sqlalchemy import inspect
            inspector = inspect(db_service.engine)
            
            columns = {col['name']: col for col in inspector.get_columns('search_sessions')}
            
            # 验证必需的字段存在
            required_fields = ['anonymous_cookie_id', 'is_anonymous']
            for field in required_fields:
                assert field in columns, f"Required field {field} not found in database schema"
            
            # 验证索引存在
            indexes = [idx['name'] for idx in inspector.get_indexes('search_sessions')]
            assert 'ix_search_sessions_anonymous_created' in indexes, "Anonymous user index not found"
            
            print(f"✅ 数据库模式验证通过")
            print(f"   匿名用户字段: {required_fields}")
            print(f"   匿名用户索引: ix_search_sessions_anonymous_created")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
